<?php

namespace App\Console;

use App\Console\Commands\SendRecurringGift;
use App\Models\Setting;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        SendRecurringGift::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $recurringSettings = Setting::where('name', 'recurring-gift')->first();

        if (!blank($recurringSettings)) {

            $run_at_day = Carbon::parse($recurringSettings->run_at)->day;

            $schedule->command('send:recurringGift')->monthlyOn($run_at_day, '00:00');
        }

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

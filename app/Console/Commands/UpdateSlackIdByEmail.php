<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UpdateSlackIdByEmail extends Command
{
    private $token;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:user-slack-id {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update user slack id by Email address';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->token = "********************************************************";
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userEmail = $this->argument('email');

        $allMembers = $this->getAllMembers();

        $member = array_filter($allMembers, function ($item) use($userEmail) {
            return $item["email"] === $userEmail;
        });

        $this->saveMembersSlackIdInUsersTable($member);

        return 0;
    }

    public function getAllMembers(): array
    {
        try {
            $allMembers = [];
            $response = Http::withHeaders(['Authorization' => 'Bearer '. $this->token])
                ->get('https://slack.com/api/users.list');

            if ($response->ok()) {
                $this->info("Got all members");
                $members = $response->json()['members'];
                foreach ($members as $member) {
                    array_push($allMembers, ['email' => data_get($member, 'profile.email'), 'slack_id' => data_get($member, 'id')]);
                }
                return $allMembers;
            } else {
                return [];
            }
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            return [];
        }
    }

    public function saveMembersSlackIdInUsersTable($allMembers)
    {
        foreach ($allMembers as $member) {
            $user = User::where('email', $member['email'])->first();
            if (!blank($user)) {
                $user->slack_id = $member['slack_id'];
                $user->save();
                $this->info("User information saved to database ". $member['email']);
            }
        }
    }
}

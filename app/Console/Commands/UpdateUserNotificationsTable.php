<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserNotifications;
use Illuminate\Console\Command;

class UpdateUserNotificationsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user-notification:sync-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $allUsers = User::all();
        $created = 0;
        $skipped = 0;

        foreach ($allUsers as $user) {
            // Check if a notification record already exists
            $exists = UserNotifications::where('user_id', $user->id)->exists();

            if (!$exists) {
                UserNotifications::create([
                    'user_id' => $user->id,
                    'maintenance_mode' => false,
                    'order_status' => true,
                ]);
                $created++;
            } else {
                $skipped++;
            }
        }

        $this->info("User notifications synced successfully.");
        $this->info("Created: {$created}, Skipped (already exists): {$skipped}");

        return Command::SUCCESS;
    }
}

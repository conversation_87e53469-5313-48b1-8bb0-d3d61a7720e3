<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetSlackChanelMembers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get-all-members';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get all the members and save user id';

    private $token;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->token = "********************************************************";
        parent::__construct();

    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // new
            $allMembers = $this->getAllMembers();
            $this->saveMembersSlackIdInUsersTable($allMembers);

        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }

        return 0;
    }

    public function saveMembersSlackIdInUsersTable($allMembers)
    {
        foreach ($allMembers as $member) {
            $user = User::where('email', $member['email'])->first();
            if (!blank($user)) {
                $user->slack_id = $member['slack_id'];
                $user->save();
                $this->info("User information saved to database ". $member['email']);
            }
        }
    }

    // new
    public function getAllMembers(): array
    {
        try {
            $allMembers = [];
            $response = Http::withHeaders(['Authorization' => 'Bearer '. $this->token])
                ->get('https://slack.com/api/users.list');

            if ($response->ok()) {
                $this->info("Got all members");
                $members = $response->json()['members'];
                foreach ($members as $member) {
                    array_push($allMembers, ['email' => data_get($member, 'profile.email'), 'slack_id' => data_get($member, 'id')]);
                }
                return $allMembers;
            } else {
                return [];
            }
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            return [];
        }
    }
}

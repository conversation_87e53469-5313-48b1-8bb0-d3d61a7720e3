<?php

namespace App\Console\Commands;

use App\Models\Coupon;
use App\Models\Gift;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendRecurringGift extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:recurringGift';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Recurring Gift to all';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();

        try {

            $recurringSettings = Setting::where('name', 'recurring-gift')->first();
            $currentDate = Carbon::today()->toDateString();

            if ($currentDate === Carbon::parse($recurringSettings->run_at)->toDateString()) {

                $items = json_decode($recurringSettings->data);

                $allEmployee_id = User::all()->pluck('id');

                foreach ($allEmployee_id as $key => $emp_id) {

                    $gift = Gift::create([
                        'items' => $recurringSettings->data,
                        'created_at' => Carbon::now(),
                        'user_id' => $emp_id
                    ]);

                    foreach ($items as $item) {
                        $data = json_decode($item);
                        for ($i = 1; $i <= $data->qty * $data->req_coupon ; $i++) {
                            $code = rand(1000, 9999);
                            $cuponData[] = array(
                                'code' => 'G'.$emp_id.'-'.$code,
                                'user_id' => $emp_id,
                                'menu_id' => $data->id,
                                'gift_id' => $gift->id,
                                'created_at' => Carbon::now()
                            );
                        }
                    }
                }

                $coupon = Coupon::insert($cuponData);
                DB::commit();

            }


        } catch (\Exception $exception) {

            DB::rollBack();

        }

    }
}

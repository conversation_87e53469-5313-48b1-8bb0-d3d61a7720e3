<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class User extends Authenticatable implements HasMedia
{
    use HasFactory, Notifiable, InteractsWithMedia, SoftDeletes,HasApiTokens;

    const AVATAR_COLLECTION = "avatar";

    const EMPLOYEE = 'employee';

    protected $fillable = [
        'name',
        'email',
        'password',
        'designation',
        'role',
        'fcm_token',
        'is_available',
        'permission',
        'slack_id'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'avatar'
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'permission' => 'array'
    ];

    protected $with = [
        'media'
    ];

    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = bcrypt($value);
    }

    public function getAvatarAttribute()
    {
        $image = $this->getMedia(self::AVATAR_COLLECTION)->first();

        if (!blank($image)) {
            return $image->getUrl();
        }

        return url('assets/img/user1.png');
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, 'user_team');
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class,'user_id','id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function logs()
    {
        return $this->hasMany(Log::class, 'log_owner_id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(self::AVATAR_COLLECTION)->singleFile();
    }

    public function availableCoupons()
    {
        return $this->coupons()->where('is_used', false);
    }

    public function consumedCoupons()
    {
        return $this->coupons()->where('is_used', true);
    }

    public function getMostOrderedAttribute()
    {
        return $this->orders()->select([
                'id',
                'menu_id',
                DB::raw('count(menu_id) as ordered')
            ])->groupBy('menu_id')->orderBy('ordered', 'desc')->take(1)->get()->first()->menu ?? null;
    }

    public function getAvailableCouponCountAttribute()
    {
        return $this->getCalculatedCoupons(false)->pluck('available')->sum() ?? 0;
    }

    public function getConsumedCouponCountAttribute()
    {
        return $this->getCalculatedCoupons(true)->pluck('available')->sum() ?? 0;
    }

    public function getCalculatedCoupons($isUsed)
    {
//        return DB::table('coupons')->join('menus', 'coupons.menu_id', 'menus.id')
//            ->select(["coupons.menu_id", DB::raw("ROUND(COUNT(coupons.menu_id) / menus.required_coupon, 1) as available")])
//            ->where('coupons.user_id', $this->id)->where('coupons.is_used', $isUsed)
//            ->groupBy('coupons.menu_id')
//            ->get();

        $query = DB::table('coupons')
            ->join('menus', 'coupons.menu_id', '=', 'menus.id')
            ->select([
                'coupons.menu_id',
                DB::raw('ROUND(COUNT(coupons.menu_id) / menus.required_coupon, 1) as available')
            ])
            ->where('coupons.user_id', $this->id)
            ->where('coupons.is_used', $isUsed);

        if ($isUsed) {
            $query->whereNotNull('coupons.order_id');
        }

        return $query->groupBy('coupons.menu_id')->get();
    }
    public function availableNotificationChannel(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(UserNotifications::class);
    }
}

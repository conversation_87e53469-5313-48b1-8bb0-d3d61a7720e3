<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Coupon extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code',
        'user_id',
        'menu_id',
        'expire_at',
        'type',
        'is_used',
        'order_id',
        'gift_id',
        'coffee_transfer_id'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }


    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }
    public function gift()
    {
        return $this->belongsTo(Gift::class);
    }
    public function getAvailableCouponCountAttribute()
    {
        return $this->getCalculatedCoupons(false)->pluck('available')->sum() ?? 0;
    }
    public function getCalculatedCoupons($isUsed)
    {
//        return DB::table('coupons')->join('menus', 'coupons.menu_id', 'menus.id')
//            ->select(["coupons.menu_id", DB::raw("ROUND(COUNT(coupons.menu_id) / menus.required_coupon, 1) as available")])
//            ->where('coupons.is_used', $isUsed)
//            ->groupBy('coupons.menu_id')
//            ->get();

        $query = DB::table('coupons')
            ->join('menus', 'coupons.menu_id', '=', 'menus.id')
            ->select(
                [
                    "coupons.menu_id",
                    DB::raw("ROUND(COUNT(coupons.menu_id) / menus.required_coupon, 1) as available")
                ])
            ->where('coupons.is_used', $isUsed);
        if ($isUsed) {
            $query->whereNotNull('coupons.order_id');
        }
        return $query->groupBy('coupons.menu_id')->get();
    }
}

<?php

namespace App\Models;

use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CoffeeTransfer extends Model
{
    use HasFactory, SoftDeletes, Loggable;

    protected $fillable = [
        'sender_id',
        'receiver_id',
        'message',
        'items',
        'status',
        'last_seen',
        'expired_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'items' => 'array'
    ];

    public function sender(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
    public function receiver(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function logs()
    {
        return $this->morphMany(Log::class,'loggable');
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class, 'coffee_transfer_id');
    }

    public function generateMessage(): string
    {
        $items = $this->items;
        return "You have received a gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ") from {$this->sender->name}";
    }

    public function generateSendMessage(): string
    {
        $items = $this->items;
        return "You have sent a gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ") to {$this->receiver->name}";
    }

    public function generateRejectedMessage($userType)
    {
        $items = $this->items;
        if ($userType === 'sender') {
            return "{$this->receiver->name} has rejected your gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ")";
        }
        if ($userType === 'receiver') {
            return "You have rejected a gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ") from {$this->sender->name}";
        }
    }
    public function generateAcceptedMessage($userType)
    {
        $items = $this->items;
        if ($userType === 'sender') {
            return "{$this->receiver->name} has accepted your gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ")";
        }
        if ($userType === 'receiver') {
            return "You have accepted a gift of " . data_get($items, 'name') . " (" . data_get($items, 'qty') . ") from {$this->sender->name}";
        }
    }
}

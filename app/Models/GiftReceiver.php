<?php

namespace App\Models;

use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GiftReceiver extends Model
{
    use HasFactory;
    use SoftDeletes;
    use Loggable;

    protected $guarded = [];
    protected $table = 'gift_receivers';

    protected $fillable = [
        'receiver_id',
        'gift_id',
        'last_seen',
        'sender_id'
    ];

    public function receiverDetails(): BelongsTo
    {
        return $this->belongsTo(User::class,'receiver_id');
    }

    /**
     * @return BelongsTo
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class,'sender_id');
    }

    /**
     * generates message for gift
     *
     * @return string
     */
    public function generateMessage(): string
    {
        return "You have received a Gift Coupon from {$this->sender->name}";
    }

    /**
     * @return MorphMany
     */
    public function logs(): MorphMany
    {
        return $this->morphMany(Log::class,'loggable');
    }

    public function gifts(): BelongsTo
    {
        return $this->belongsTo(Gift::class,'gift_id');
    }
}

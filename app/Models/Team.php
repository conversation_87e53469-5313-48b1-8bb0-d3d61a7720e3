<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Team extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    const THUMBNAIL_COLLECTION = 'team_thumb';

    protected $guarded = [];

    protected $table = 'team';

    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function getThumbnailAttribute()
    {
        $image = $this->getMedia(self::THUMBNAIL_COLLECTION)->first();

        if (!blank($image)) {
            return $image->getUrl();
        }

        return url('assets/img/ea-logo.svg');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(self::THUMBNAIL_COLLECTION)->singleFile();
    }
}

<?php

namespace App\Models;

use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Gift extends Model
{
    use SoftDeletes,Loggable;

    protected $guarded = [];
    protected $table = 'gifts';

    protected $fillable = [
        'user_id',
        'items',
        'message',
        'send_by',
        'expired_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'items' => 'array'
    ];

    public function receivers()
    {
        return $this->hasMany(GiftReceiver::class,'gift_id');
    }
    public function sender(){
        return $this->belongsTo(User::class,'send_by');
    }
    public function onereceiver(){
        return $this->belongsTo(User::class,'user_id');
    }

    public function owner(){
        return $this->belongsTo(User::class);
    }

    public function logs()
    {
        return $this->morphMany(Log::class,'loggable');
    }

    public function generateMessage()
    {
        return "You have received a Gift Coupon from {$this->sender->name}";
    }

}

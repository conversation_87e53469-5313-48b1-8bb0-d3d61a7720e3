<?php

namespace App\Models;

use App\Constants\LogType;
use App\Constants\OrderStatus;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Log extends Model
{
    use HasFactory;

    protected $fillable = [
        'notifiable_type',
        'notifiable_id',
        'action_type',
        'log_manager_id',
        'log_owner_id',
        'message',
    ];

    protected $appends = ['new_message'];

    protected $dates = ['created_at', 'updated_at', 'deleted_at'];

    public function NotificationOwner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'log_owner_id');
    }

    public function NotificationManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'log_manager_id');
    }

    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'log_manager_id');
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'log_owner_id');
    }

    public function customerMessage(): string
    {
        $message = "Your request of {$this->message}";
        if (in_array($this->action_type, [OrderStatus::PENDING, OrderStatus::REJECTED]) && $this->log_manager_id == auth()->user()->id) {
            return $message;
        }
        $actionBy = $this->manager->name;
        if ($this->log_manager_id == auth()->user()->id) {
            $actionBy .= '(You)';
        }
        return "Your request of {$this->message} by {$actionBy}";
    }

    public function convertMessage(): string
    {
        $actionBy = $this->manager->name;
        if ($this->log_manager_id == auth()->user()->id) {
            $actionBy .= '(You)';
        }
        return "{$actionBy} successfully converted {$this->message}";
    }

    public function baristaMessage(): string
    {
        return "Request of {$this->message} - By {$this->manager->name}";
    }

    public function giftMessage(): string
    {
        if (!blank($this->loggable->gifts)){
            $giftMessage = $this->loggable->gifts->message;
            $items = $this->loggable->gifts->items;
        }else{
            $giftMessage = $this->loggable ? $this->loggable->message : '';
            $items = $this->loggable ? $this->loggable->items : [];
        }

        $giftDetails = '';

        foreach ($items as $key => $item) {
            $item = json_decode($item);
            if ($key == 0 && count($items) <= 1 || $key == count($items) - 1) {
                $giftDetails .= "{$item->qty} {$item->name}";
            } else {
                $giftDetails .= "{$item->qty} {$item->name}, ";
            }
        }
        return "You have been gifted {$giftDetails} Coupon with the following message: <i>{$giftMessage}</i>";
    }

    public function getMessage(): string
    {
        switch ($this->action_type) {
            case LogType::CONVERT:
                return $this->convertMessage();
            default:
                return $this->customerMessage();
        }
    }

    /**
     * @return string
     */
    public function getNewMessageAttribute(): string
    {
        switch ($this->action_type) {
            case LogType::CONVERT:
                return $this->convertMessage();
            case LogType::GIFT:
                return $this->giftMessage();
            case LogType::TRANSFER:
                return $this->transferMessage();
            default:
                return $this->customerMessage();
        }
    }

    public static function userNotifications(): LengthAwarePaginator
    {
        return auth()->user()->logs()->orderBy('created_at', 'desc')->paginate(20);
    }

    public static function userNotificationsToday(): Collection
    {
        return auth()->user()->logs()
            ->whereNull('read_at')
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public static function userPreviousNotifications(): LengthAwarePaginator
    {
        return auth()->user()->logs()
            ->with('NotificationOwner:id,name', 'NotificationManager:id,name')
            ->whereNull('read_at')->where('created_at', '<', today())
            ->orderBy('created_at', 'desc')->paginate(15);
    }

    public static function baristaNotifications()
    {
        return Log::where('loggable_type', Order::class)
            ->with('NotificationOwner:id,name')
            ->whereNull('read_at')
            ->orderBy('created_at', 'desc')
            ->paginate(30);
    }

    //transfer
    public function transferMessage(): string
    {
        return $this->message;
    }
}

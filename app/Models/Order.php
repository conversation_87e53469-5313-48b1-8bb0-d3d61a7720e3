<?php

namespace App\Models;

use App\Constants\OrderStatus;
use App\Traits\Loggable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes, Loggable;

    protected $fillable = [
        'user_id',
        'menu_id',
        'note',
        'status',
        'last_updated_by',
        'for_guest',
        'is_espresso_double_shot',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }
    public function log()
    {
        return $this->morphOne(Log::class, 'loggable');
    }

    public function generateMessage()
    {
        $doubleShot = $this->is_espresso_double_shot ? 'with double shot Espresso' : '';
        return "{$this->menu->name} {$doubleShot} {$this->checkForGuest()} (#{$this->id}) {$this->getPrepositionByStatus()} {$this->status}";
    }

    public function getPrepositionByStatus()
    {
        switch ($this->status) {
            case OrderStatus::REJECTED:
            case OrderStatus::CONFIRMED:
            case OrderStatus::COMPLETED:
                return 'has been ';
            case OrderStatus::PENDING:
                return 'is ';
            default:
                return 'is currently in ';
        }
    }

    public function checkForGuest()
    {
        if ($this->for_guest) {
            return "for Guest";
        }
    }

    public function getCompletedOrderCount()
    {
        return Order::where('status',OrderStatus::COMPLETED)->count();
    }

    public function getCompletedOrderCountByToday()
    {
        $startOfDay = Carbon::today()->startOfDay()->toDateTimeString();
        $endOfOfDay = Carbon::today()->endOfDay()->toDateTimeString();
        return Order::where('status',OrderStatus::COMPLETED)
            ->where('created_at', '>=', $startOfDay)
            ->where('created_at', '<=', $endOfOfDay)
            ->count();
    }

    /**
     * Scope a query to filter orders not canceled by a barista.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeNotCancelledByBarista(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->where('status', '!=', OrderStatus::REJECTED)
                ->orWhere(function ($q) {
                    $q->where('status', OrderStatus::REJECTED)
                        ->whereHas('log.manager', function ($query) {
                            $query->where('role', '!=', 'employee');
                        });
                });
        });
    }
}

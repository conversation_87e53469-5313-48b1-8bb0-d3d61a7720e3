<?php

namespace App\Models;

use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Menu extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, SoftDeletes, Loggable;

    use SoftDeletes;

    const THUMBNAIL_COLLECTION = 'menu_thumb';

    protected $guarded = [];

    protected $with = ['media'];

    protected $appends = [
        'thumbnail', 'ingredients_with_bar', 'can_order', 'ingredients_with_keys'
    ];

    protected $casts = [
        'ingredients' => 'array'
    ];

    public function getThumbnailAttribute()
    {
        $image = $this->getMedia(self::THUMBNAIL_COLLECTION)->first();

        if (!blank($image)) {
            return $image->getUrl();
        }

        return url('assets/img/coffee1.png');
    }

    public function getIngredientsWithBarAttribute()
    {
        $ingress = collect(($this->ingredients ?? []))->map(function ($ing) {
            return trans("constants.ingredients.{$ing}");
        })->toArray();

        return !blank($ingress) ? implode(" | ", $ingress) : "";
    }

    public function getIngredientsWithKeysAttribute()
    {
        /*$ingrss = $this->ingredients ?? [];
        $newIngress = [];
        foreach ($ingrss as $ing) {
            $newIngress[] = [
                'title' => trans("constants.ingredients.{$ing}"),
                'key' => $ing
            ];
        }

        return $newIngress;*/
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(self::THUMBNAIL_COLLECTION)->singleFile();
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function availableCoupons(User $user = null)
    {
        if (auth()->check()) {
            $user = blank($user) ? auth()->user() : $user;
            $value = optional($user->getCalculatedCoupons(false)->where('menu_id', $this->id)->first())->available ?? 0;
            if ((int)$value == $value) {
                $value = (int)$value;
            }
            return $value;
        }

        return 0;
    }

    public function totalDistributed($start, $end, $menu_id)
    {
        $menu_id = $menu_id == 'all' ? '' : $menu_id;
        $startTime = $start->startOfDay()->toDateTimeString();
        $endTime = $end->endOfDay()->toDateTimeString();
        $requiredCoupon = $this->required_coupon > 0 ? $this->required_coupon : 1;

        $couponCount = $this->coupons()
                ->when($menu_id, function ($query) use ($menu_id) {
                    return $query->where('menu_id', $menu_id);
                })
                ->whereBetween('created_at', [$startTime, $endTime])
                ->whereHas('user', function ($query) {
                    $query->whereNull('deleted_at');
                })
                ->where(function ($query) {
                    $query->whereNotNull('gift_id')
                        ->whereNull('coffee_transfer_id')
                        ->orWhere(function ($query) {
                            $query->whereNull('gift_id')
                            ->whereNotNull('coffee_transfer_id')
                            ->where('is_used', 0);
                        });
                })
                ->count();

        return $couponCount / $requiredCoupon;
    }

    public function totalRedeemed($start, $end, $menu_id)
    {
        $menu_id = $menu_id == 'all' ? '' : $menu_id;
        $startTime = $start->startOfDay()->toDateTimeString();
        $endTime = $end->endOfDay()->toDateTimeString();
        $requiredCoupon = $this->required_coupon > 0 ? $this->required_coupon : 1;

        return $this->coupons()
                ->when($menu_id, function ($query) use ($menu_id) {
                    return $query->where('menu_id', $menu_id);
                })
                ->where('is_used', true)
                ->whereBetween('updated_at', [$startTime, $endTime])
                ->count() / $requiredCoupon;
    }

    public function allCoffeeCount()
    {
        $requiredCoupon = $this->required_coupon > 0 ? $this->required_coupon : 1;
        return $this->coupons()
                ->whereHas('user', function ($query) {
                    $query->whereNull('deleted_at');
                })
                ->where('is_used', '=', 0)->count() / $requiredCoupon;
    }

    public function userCoupons($user)
    {
        return $this->coupons()->where('user_id', $user->id)->where('is_used', false)->count();
    }

    public function getCanOrderAttribute($user = null)
    {
        return $this->availableCoupons($user) > 0;
    }

    public function scopeActiveMenus($query)
    {
        return $query->where('is_active', 1);
    }
}

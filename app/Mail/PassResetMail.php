<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PassResetMail extends Mailable
{
    use Queueable, SerializesModels;

    private $details;
    private $email;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($details, $email)
    {
        $this->details = $details;
        $this->email = $email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Reset WPCafe Password')
            ->to($this->email)
            ->from('<EMAIL>', 'Wpcafe')
            ->view('customer-pass-reset-mail', ['details' => $this->details]);
    }
}

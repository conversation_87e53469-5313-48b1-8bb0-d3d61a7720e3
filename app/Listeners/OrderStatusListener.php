<?php

namespace App\Listeners;

use App\Constants\OrderStatus;
use App\Events\OrderStatusChanged;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class OrderStatusListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderStatusChanged  $event
     * @return void
     */
    public function handle(OrderStatusChanged $event)
    {
        if ($event->order->user->fcm_token) {
            $title = "Your coffee is ";
            $content = "Hi {$event->order->user->name}, your request for {$event->order->menu->name} has been ";

            if ($event->order->status == OrderStatus::COMPLETED) {
                $title .= "ready :)";
                $content .= "completed";
            } elseif ($event->order->status == OrderStatus::PROCESSING) {
                $title .= "on processing";
                $content .= "confirmed";
            } elseif ($event->order->status == OrderStatus::REJECTED) {
                $title .= "rejected";
                $content .= "cancelled";
            }

            sendFcmMessage($event->order->user, $title, $content);
        }
    }
}

<?php


namespace App\Services;


use App\Constants\LogType;
use App\Models\Menu;
use Symfony\Component\HttpFoundation\Response;
use Exception;

class MenuService
{
    public function getAllMenus()
    {
        return Menu::get()
            ->map(function ($menu) {
                $menu->availableCoupon = $menu->availableCoupons();
                return $menu;
            })->sortByDesc('availableCoupon')->values();
    }

    public function getMenu($id)
    {
        return Menu::findOrFail($id);
    }

    public function showConversion()
    {
        return Menu::with(['coupons' => function ($q) {
            return $q->where('user_id', auth()->user()->id)->where('is_used',0);
        }])->get()->map(function ($menu) {
            $menu->availableCoupon = $menu->availableCoupons();
            return $menu;
        })
        ->sort(function ($a, $b) {
            return $b->availableCoupon <=> $a->availableCoupon;
        })->values();
    }

    public function extraCoffeeConvert($request,$fromQty)
    {
        $fromCoffee = Menu::findOrFail(data_get($request, 'from_coffee_id'));
        $extraConversionCoffee = Menu::findOrFail(data_get($request, 'extra_coffee.id'));
        $extraConversionQty = data_get($request, 'extra_coffee_quantity', 0);
        $toXtraConversionCouponCount = (int)$extraConversionQty * $extraConversionCoffee->required_coupon;
        $availableCouponsForExtraConversion = auth()->user()->availableCoupons()->where('menu_id', $fromCoffee->id)->take($toXtraConversionCouponCount)->get();

        $availableCouponsForExtraConversion->each->update([
            "menu_id" => $extraConversionCoffee->id
        ]);

        $fromCoffee->addLog([
            'log_manager_id' => auth()->user()->id,
            'message'        => "{$fromCoffee->name}({$fromQty}) to {$extraConversionCoffee->name}({$extraConversionQty})",
            'action_type'    => LogType::CONVERT,
            'log_owner_id'   => auth()->user()->id
        ]);
    }
}

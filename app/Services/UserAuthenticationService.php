<?php

namespace App\Services;

use App\Http\Controllers\ApiV1\ApiController;
use App\Models\User;
use http\Env\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UserAuthenticationService extends ApiController
{
    const AUTH_TOKEN = 'wptoken';

    public function loginHandler($request)
    {
        if (Auth::attempt($request->all())) {
            $user = Auth::user();
            $user->token = $user->createToken(self::AUTH_TOKEN)->plainTextToken;
            $message = auth()->user()->role . ' ' . 'logged in successfully';
            return $this->sendSuccessResponse($user, $message, Response::HTTP_OK);
        }
        $message = 'invalid credentials';
        return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, $message);
    }

    public function registrationHandler($request)
    {
        try {
            $user = User::query()->create($request->all());
            $token = $user->createToken(self::AUTH_TOKEN)->plainTextToken;
            $response = [
                'user' => $user,
                'token' => $token,
            ];
            return $this->sendSuccessResponse($response, 'user created successfully', Response::HTTP_OK);
        } catch (\Exception $exceptions) {
            return $this->sendErrorResponse($exceptions->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function logoutHandler(): \Illuminate\Http\JsonResponse
    {
        auth()->user()->tokens()->delete();
        $response = 'user logged out';
        return $this->sendSuccessResponse($response);
    }

    public function handlePassChange($request): array
    {
        $oldPassword = $request->old_password;
        $newPassword = $request->new_password;
        $confirmPassword = $request->confirm_password;

        if ($newPassword === $confirmPassword) {
            if (Auth::attempt(['email' => auth()->user()->email, 'password' => $oldPassword])) {
                return [
                    "type" => true,
                    "content" => $newPassword
                ];
            }
            return [
                "type" => false,
                "content" => "Old password is incorrect"
            ];
        }
        return [
            "type" => false,
            "content" => "New password and confirm password do not match"
        ];
    }
}

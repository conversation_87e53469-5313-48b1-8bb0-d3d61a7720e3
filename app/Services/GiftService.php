<?php

namespace App\Services;

use App\Constants\LogType;
use App\Models\Gift;
use Carbon\Carbon;
use Illuminate\Validation\Validator;

class GiftService
{
    /**
     * saves the users to gift receivers table who received gift
     *
     * @param $giftReceiver
     * @param $gift
     * @param $empId
     * @return mixed
     */
    public function saveGiftReceivers($giftReceiver, $gift, $empId)
    {
        $giftReceiver->gift_id = $gift->id;
        $giftReceiver->receiver_id = $empId;
        $giftReceiver->sender_id = $gift->send_by;
        $giftReceiver->save();

        return $giftReceiver;
    }

    /**
     * saves log for each user who got gift
     *
     * @param $giftReceiver
     * @return void
     */
    public function addGiftLogs($giftReceiver)
    {
        $message = $giftReceiver->generateMessage();

        $giftReceiver->addLog([
            'log_manager_id' => auth()->user()->id ?? 2,
            'log_owner_id' => $giftReceiver->receiver_id,
            'action_type' => LogType::GIFT,
            'message' => $message
        ]);
    }

    /**
     * makes an array to save in coupons table
     *
     * @param $emp_id
     * @param $data
     * @param $gift
     * @return array
     */
    public function makeCouponArray($emp_id, $data, $gift): array
    {
        $code = rand(1000, 9999);
        return array(
            'code' => 'G' . $emp_id . '-' . $code,
            'user_id' => (int) $emp_id,
            'menu_id' => $data->id,
            'gift_id' => $gift->id,
            'created_at' => Carbon::now()
        );
    }

    /**
     * validates gift request
     *
     * @param $request
     * @return Validator
     */
    public function validateRequest($request): Validator
    {
        return \Validator::make($request->all(), [
            'items' => 'required|array',
            'user_id' => 'required',
            'message' => 'nullable|string'
        ]);
    }

    /**
     * saves gift session to gift table
     *
     * @param $request
     * @param $items
     * @return mixed
     */
    public function saveGift($request, $items)
    {
        return Gift::create(array_merge($request->only(['message']), [
            'items' => $items,
            'send_by' => auth()->user()->id,
            'created_at' => Carbon::now(),
            'user_id' => 1,
        ]));
    }
}

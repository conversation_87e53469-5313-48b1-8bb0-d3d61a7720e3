<?php

namespace App\Services;

use App\Constants\OrderStatus;
use App\Models\Coupon;
use App\Models\Menu;

class BaristaOrderService
{
    /**
     *gets all pending and processing orders list and update each of their status to "canceled"
     *
     * @param $orders
     * @return void
     */
    public function updateEachOrderStatus($orders)
    {
        $espressoMenu = Menu::query()
            ->where('name', 'like', 'Espresso')
            ->first();
        foreach ($orders as $order){
            $order->status = OrderStatus::REJECTED;
            $order->save();

            Coupon::where('order_id', $order->id)->update(['order_id' => null, 'is_used' => null]);
            if ($order->is_espresso_double_shot) {
                Coupon::where('order_id', $order->id)->where('menu_id', $espressoMenu->id)->update(['order_id' => null, 'is_used' => null]);
            }
        }
    }

    //mark all given orders as completed
    public function markAllOrdersAsCompleted($orders)
    {
        foreach ($orders as $order){
            $order->status = OrderStatus::COMPLETED;
            $order->save();
        }
    }

}

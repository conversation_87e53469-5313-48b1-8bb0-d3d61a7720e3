<?php

namespace App\Services;

use App\Constants\OrderStatus;
use App\Models\Menu;
use App\Models\Order;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class ReportService
{
    public function getCoffeeConsumeAnalytics($start, $end,$menu_id) :array
    {
        if ($menu_id == 'all') {
            $menus = Menu::all();
        } else {
            // Check if it's a comma-separated list of IDs
            if (strpos($menu_id, ',') !== false) {
                $menuIds = explode(',', $menu_id);
                $menus = Menu::whereIn('id', $menuIds)->get();
            } else {
                $menus = Menu::where('id', $menu_id)->get();
            }
        }
        if($start->toDateString() === $end->toDateString()){
            return $this->todayToHour($start->toDateString(), $menus);
        }
        $period = CarbonPeriod::create($start, $end);
        $data = [];
        $dates = [];

        foreach ($menus as $menu) {
            $countArr = [];
            foreach ($period as $value) {
                $curr = $value->format('D');
                if ( $curr !== 'Fri' && $curr !== 'Sat' ){
                    $dates[$value->toDateString()] = $value->format('d-M');
                    $countArr[] = Order::query()->where('menu_id', $menu->id)
                        ->where('status', OrderStatus::COMPLETED)
                        ->whereRaw("date(`updated_at`) = '{$value->toDateString()}'")
                        ->count();
                }
            }

            $data[] = [
                'name' => $menu->name,
                'data' => $countArr
            ];
        }
        return [$data,$dates];
    }

    public function todayToHour($date,$menus) :array
    {
        $data = [];
        $dates = [];

        foreach ($menus as $menu) {
            $countArr = [];
            for ($i = 8; $i <= 20; $i++) {
                $currentHour = sprintf('%02d', $i);
                $startTime = "$date $currentHour:00:00";
                $endTime = "$date $currentHour:59:59";
                //$dates[$i] = "$currentHour:00 AM";
                $dates[$i] = date('h:i A', strtotime($startTime));
                $countArr[] = Order::query()->where('menu_id', $menu->id)
                    ->where('status', OrderStatus::COMPLETED)
                    ->whereBetween('updated_at', [$startTime, $endTime])
                    ->count();
            }

            $data[] = [
                'name' => $menu->name,
                'data' => $countArr
            ];
        }

        return [$data,$dates];
    }

    /**
     * Get coffee consumption statistics for ApexCharts
     *
     * @param string|null $start_date Start date in Y-m-d format, defaults to 7 days ago
     * @param string|null $end_date End date in Y-m-d format, defaults to today
     * @param int|string|null $menu_id Specific menu ID or 'all' for all menus
     * @return array Array containing series and categories for ApexCharts
     */
    public function getCoffeeConsumptionStats($start_date = null, $end_date = null, $menu_id = 'all'): array
    {
        // Set default dates if not provided
        $start = $start_date ? Carbon::parse($start_date) : Carbon::now()->subDays(6);
        $end = $end_date ? Carbon::parse($end_date) : Carbon::now();

        // Query to get coffee stats
        $query = Menu::query();

        // Filter by menu_id if specified
        if ($menu_id !== 'all') {
            // Check if it's a comma-separated list of IDs
            if (strpos($menu_id, ',') !== false) {
                $menuIds = explode(',', $menu_id);
                $query->whereIn('id', $menuIds);
            } else {
                $query->where('id', $menu_id);
            }
        }

        // Get coffee statistics with counts
        $coffeeStats = $query->withCount([
            // Count total requested orders
            'orders as total_requested' => function ($query) use ($start, $end) {
                $query->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            // Count completed (consumed) orders
            'orders as total_consumed' => function ($query) use ($start, $end) {
                $query->where('status', OrderStatus::COMPLETED)
                      ->whereBetween('updated_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            // Count pending orders
            'orders as total_pending' => function ($query) use ($start, $end) {
                $query->where('status', OrderStatus::PENDING)
                      ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            // Count processing orders
            'orders as total_processing' => function ($query) use ($start, $end) {
                $query->where('status', OrderStatus::PROCESSING)
                      ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            // Count rejected orders
            'orders as total_rejected' => function ($query) use ($start, $end) {
                $query->where('status', OrderStatus::REJECTED)
                      ->whereBetween('updated_at', [$start->startOfDay(), $end->endOfDay()]);
            }
        ])->get();

        // Format data for ApexCharts
        $categories = $coffeeStats->pluck('name')->toArray();

        // Create series for ApexCharts
        $series = [
            [
                'name' => 'Requested',
                'data' => $coffeeStats->pluck('total_requested')->toArray()
            ],
            [
                'name' => 'Consumed',
                'data' => $coffeeStats->pluck('total_consumed')->toArray()
            ],
            [
                'name' => 'Pending',
                'data' => $coffeeStats->pluck('total_pending')->toArray()
            ],
            [
                'name' => 'Processing',
                'data' => $coffeeStats->pluck('total_processing')->toArray()
            ],
            [
                'name' => 'Rejected',
                'data' => $coffeeStats->pluck('total_rejected')->toArray()
            ]
        ];

        // Calculate totals for summary
        $totals = [
            'total_requested' => $coffeeStats->sum('total_requested'),
            'total_consumed' => $coffeeStats->sum('total_consumed'),
            'total_pending' => $coffeeStats->sum('total_pending'),
            'total_processing' => $coffeeStats->sum('total_processing'),
            'total_rejected' => $coffeeStats->sum('total_rejected'),
            'date_range' => [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d')
            ]
        ];

        return [
            'series' => $series,
            'categories' => $categories,
            'coffeeStats' => $coffeeStats,
            'totals' => $totals
        ];
    }

    /**
     * Generate time chart data for customer orders
     *
     * @param Collection $orders Collection of orders to analyze
     * @param Carbon $start Start date
     * @param Carbon $end End date
     * @return array Time chart data with series and categories
     */
    private function generateCustomerTimeChartData($orders, Carbon $start, Carbon $end): array
    {
        $ordersByDay = [];
        $categories = [];

        // If same day, show by hour
        if ($start->toDateString() === $end->toDateString()) {
            for ($i = 8; $i <= 20; $i++) {
                $currentHour = sprintf('%02d', $i);
                $startTime = "{$start->toDateString()} {$currentHour}:00:00";
                $endTime = "{$start->toDateString()} {$currentHour}:59:59";
                $categories[$i] = date('h:i A', strtotime($startTime));

                $ordersByDay[] = $orders
                    ->where('status', OrderStatus::COMPLETED)
                    ->filter(function($order) use ($startTime, $endTime) {
                        return $order->updated_at >= $startTime && $order->updated_at <= $endTime;
                    })
                    ->count();
            }
        } else {
            // Show by day
            $period = new CarbonPeriod($start, $end);
            foreach ($period as $date) {
                $dateString = $date->toDateString();
                $categories[$dateString] = $date->format('d-M');

                $ordersByDay[] = $orders
                    ->where('status', OrderStatus::COMPLETED)
                    ->filter(function($order) use ($date) {
                        return $order->updated_at->toDateString() === $date->toDateString();
                    })
                    ->count();
            }
        }

        // Prepare time chart data
        return [
            'series' => [[
                'name' => 'Orders',
                'data' => $ordersByDay
            ]],
            'categories' => array_values($categories)
        ];
    }

    /**
     * Get customer order analytics data
     *
     * @param int $user_id User ID to filter orders
     * @param string|null $start_date Start date in Y-m-d format
     * @param string|null $end_date End date in Y-m-d format
     * @param int|string|null $menu_id Specific menu ID or 'all' for all menus
     * @return array Array containing analytics data for the customer
     */
    public function getCustomerOrderAnalytics(int $user_id, $start_date = null, $end_date = null, $menu_id = 'all'): array
    {
        // Set default dates if not provided
        $start = $start_date ? Carbon::parse($start_date) : Carbon::now()->startOfDay();
        $end = $end_date ? Carbon::parse($end_date) : Carbon::now();

        // Query to get coffee stats for the current user
        $query = Menu::query();

        // Filter by menu_id if specified
        if ($menu_id !== 'all') {
            // Check if it's a comma-separated list of IDs
            if (strpos($menu_id, ',') !== false) {
                $menuIds = explode(',', $menu_id);
                $query->whereIn('id', $menuIds);
            } else {
                $query->where('id', $menu_id);
            }
        }

        // Get coffee statistics with counts filtered by user_id
        $coffeeStats = $query->withCount([
            'orders as total_requested' => function ($order) use ($start, $end, $user_id) {
                $order->where('user_id', $user_id)
                    ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            'orders as request_completed' => function ($order) use ($start, $end, $user_id) {
                $order->where('user_id', $user_id)
                    ->where('status', OrderStatus::COMPLETED)
                    ->whereBetween('updated_at', [$start->startOfDay(), $end->endOfDay()]);
            },
            'orders as request_rejected' => function ($order) use ($start, $end, $user_id) {
                $order->where('user_id', $user_id)
                    ->where('status', OrderStatus::REJECTED)
                    ->whereBetween('updated_at', [$start->startOfDay(), $end->endOfDay()]);
            }
        ])->get();



        // Build the order query with user_id filter
        $orderQuery = Order::where('user_id', $user_id)
            ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()]);

        // Apply coffee filter if specified
        if ($menu_id !== 'all') {
            if (strpos($menu_id, ',') !== false) {
                $menuIds = explode(',', $menu_id);
                $orderQuery->whereIn('menu_id', $menuIds);
            } else {
                $orderQuery->where('menu_id', $menu_id);
            }
        }

        // Get filtered orders
        $userOrders = $orderQuery->get();

        // Get order counts by status
        $orderStatusCounts = [
            'pending' => $userOrders->where('status', OrderStatus::PENDING)->count(),
            'processing' => $userOrders->where('status', OrderStatus::PROCESSING)->count(),
            'completed' => $userOrders->where('status', OrderStatus::COMPLETED)->count(),
            'rejected' => $userOrders->where('status', OrderStatus::REJECTED)->count(),
            'total' => $userOrders->count()
        ];

        // Generate time chart data
        $timeChartData = $this->generateCustomerTimeChartData($userOrders, $start, $end);

        return [
            'coffeeStats' => $coffeeStats,
            'orderStatusCounts' => $orderStatusCounts,
            'timeChartData' => $timeChartData
        ];
    }
}

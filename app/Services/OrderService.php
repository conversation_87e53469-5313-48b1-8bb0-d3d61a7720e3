<?php

namespace App\Services;

use App\Constants\OrderStatus;
use App\Constants\Role;
use App\Events\OrderCreated;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Menu;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class OrderService
{
    public function placeOrder($request)
    {
        $user = Auth::user();
        $menu = Menu::query()->find($request->get('menu_id'));
        $espressoShot = $request->get('espresso_shot', 'single');
        if (blank($menu) || !$menu->is_active) {
            return ['warn_message' => 'Coffee not available, It is out of market now.'];
        }

        $availableCoupon = $user->availableCoupons()->where('menu_id', $request->menu_id)->orderBy('created_at', 'asc')->take($menu->required_coupon)->get();

        if ($availableCoupon->count() < $menu->required_coupon) {
            return Response()->json(['message' => 'You don\'t have enough coupon', 'code' => 201]);
        }
        if ($espressoShot === 'double') {
            $espressoMenu = Menu::query()
                ->where('name', 'like', 'Espresso')
                ->first();
            if (blank($espressoMenu)) {
                return Response()->json(['message' => 'Espresso not available, Please contact admin.', 'code' => 201]);
            }
            $espressoCoupon = $user->availableCoupons()->where('menu_id', $espressoMenu->id)
                ->orderBy('created_at', 'asc')->take($espressoMenu->required_coupon)
                ->get();
            if ($espressoCoupon->count() <= 0) {
                return Response()->json(['message' => 'You don\'t have enough espresso coupon for double shot.', 'code' => 201]);
            }
        }

        DB::beginTransaction();
        $order = Order::query()->create(array_merge($request->except(['_token', 'menu_id', 'required_coupon', 'for_guest', 'espresso_shot']), [
            'user_id'   => $user->id,
            'menu_id'   => (int)$request->menu_id,
            'status'    => OrderStatus::PENDING,
            'for_guest' => $request->guest ? 1 : 0,
            'is_espresso_double_shot' => $espressoShot === 'double'
        ]));
        DB::commit();
        $coupon = $availableCoupon->each->update(['is_used' => true, 'order_id' => $order->id]);

        // If double shot, use the espresso coupon
        if ($espressoShot === 'double' && isset($espressoCoupon) && $espressoCoupon->count() > 0) {
            $espressoCoupon->each->update(['is_used' => true, 'order_id' => $order->id]);
        }
        event(new OrderCreated($order));

        // send push notification here
        $title = "A new coffee request";
        $doubleShot = $order->is_espresso_double_shot ? 'with double shot Espresso' : '';
        $content = "{$order->user->name} create a request for {$order->menu->name} {$doubleShot}";
        User::where('role', Role::BARISTA)->get()->each(function ($user) use ($title, $content, $order) {
            if (!blank($user->fcm_token)) sendFcmMessage($user, $title, $content, $order->menu->thumbnail);
        });
        return $content;
    }
}

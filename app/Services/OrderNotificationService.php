<?php

namespace App\Services;

use App\Constants\OrderStatus;
use App\Jobs\SlackNotificationMessage;
use App\Models\Order;
use Illuminate\Foundation\Bus\DispatchesJobs;

class OrderNotificationService
{
    use DispatchesJobs;

    private $order;
    private $message;

    public function __construct($order)
    {
        $this->order = $order;
        $this->message = $this->generateMessage();
    }

    public function sendSlackNotification()
    {
        // new
        $payload = $this->generatePayloadForDirectMessage();
        $messageJob = new SlackNotificationMessage($payload);
        $this->dispatch($messageJob);
    }

    public function generateMessage(): string
    {
        $doubleShot = $this->order->is_espresso_double_shot ? 'with double shot Espresso' : '';
        $message = "{$this->getEmojiByOrderStatus()} Your request of {$this->order->menu->name} {$doubleShot} {$this->checkForGuest()} {$this->getPrepositionByStatus()} {$this->order->status} {$this->getExtraMessage()}";
        $message = preg_replace('/\s+/', ' ', $message);
        return $message;
    }

    public function getEmojiByOrderStatus(): string
    {
        switch ($this->order->status) {
            case OrderStatus::PROCESSING:
            case OrderStatus::PENDING:
                return ":coffee:";
            case OrderStatus::REJECTED:
                return ":broken_heart:";
            case OrderStatus::COMPLETED:
                return ':white_check_mark:';
            default:
                return '';
        }
    }

    public function getExtraMessage(): string
    {
        switch ($this->order->status) {
            case OrderStatus::COMPLETED:
                return 'and ready for delivery.';
            default:
                return '';
        }
    }

    public function getPrepositionByStatus(): string
    {
        switch ($this->order->status) {
            case OrderStatus::REJECTED:
            case OrderStatus::CONFIRMED:
            case OrderStatus::COMPLETED:
                return 'has been ';
            case OrderStatus::PENDING:
                return 'is ';
            default:
                return 'is currently in ';
        }
    }

    public function checkForGuest()
    {
        if ($this->order->for_guest) {
            return "for Guest";
        }
        return '';
    }

    public function generatePayloadForDirectMessage(): array
    {
        return [
            "channel"=> $this->order->user->slack_id,
            "text" => $this->message

        ];
    }


}

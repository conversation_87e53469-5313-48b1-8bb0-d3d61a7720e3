<?php

namespace App\Http\Controllers\ApiV1\Barista;

use App\Http\Controllers\ApiV1\ApiController;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class BaristaProfileController extends ApiController
{
    public function index()
    {
        $user = Auth::user();
        return $this->sendSuccessResponse($user);
    }

    public function store(Request $request, User $user)
    {
        $this->validate($request, [
            "name" => "required|string",
            "designation" => "required|string",
            "email" => "required|email|unique:users,email," . Auth::id(),
            "password" => "nullable|min:6"
        ]);

        if ($request->password == !'undefined'){
            $user->update($request->except(['password']));
        }

        $user->update($request->except(['_token', 'avatar']));

        if ($request->has('password')) {
            $user->update(['password' => $request->get('password')]);
        }

        if ($request->hasFile('avatar')) {
            $user->addMediaFromRequest('avatar')->toMediaCollection(User::AVATAR_COLLECTION);
        }
        return $this->sendSuccessResponse($user, 'User Updated Successfully');
    }

    public function availabilityStatusUpdate($id)
    {
        $barista = User::findOrFail($id);
        try {
            DB::beginTransaction();
            if ($barista->is_available == 0) {
                $barista->is_available = 1;
            } else {
                $barista->is_available = 0;
            }
            $barista->save();
            DB::commit();
            return $this->sendSuccessResponse($barista, 'Barista Status Updated Successfully');

        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

}
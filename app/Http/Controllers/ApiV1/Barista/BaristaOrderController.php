<?php

namespace App\Http\Controllers\ApiV1\Barista;

use App\Constants\OrderStatus;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Coupon;
use App\Models\Menu;
use App\Models\Order;
use App\Models\User;
use App\Services\BaristaOrderService;
use App\Services\ReportService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class BaristaOrderController extends ApiController
{
    public function history()
    {
        $order_history_today = Order::whereDate('created_at', today())->with('menu')->orderBy('created_at', 'desc')->get();
        $order_history_previous = Order::where('created_at', '<', today())->with('menu')->orderBy('created_at', 'desc')->paginate(30);
        $orders = compact('order_history_today', 'order_history_previous');
        return $this->sendSuccessResponse($orders);
    }

    public function showOrderByStatus($status)
    {
        $orders = Order::query();
        if (!blank($status)) {
            $orders = $orders->where('status', $status);
        }
        $orders = $orders->orderBy('created_at', 'asc')->get();

        return $this->sendSuccessResponse($orders);
    }

    public function updateOrderStatus($order, $status)
    {
        $orderStatusCondition = [
            OrderStatus::PENDING => [OrderStatus::PROCESSING, OrderStatus::REJECTED],
            OrderStatus::PROCESSING => [OrderStatus::COMPLETED, OrderStatus::REJECTED]
        ];

        $order = Order::findOrFail($order);
        $requireTargetStatus = data_get($orderStatusCondition, $order->status, []);

        if (!in_array($status, $requireTargetStatus)) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Request condition not matched');
        }
        DB::beginTransaction();
        try {
            $order->update(['status' => $status]);
            if ($status === OrderStatus::REJECTED) {
                Coupon::where('order_id', $order->id)->update(['order_id' => null, 'is_used' => null]);
                if ($order->is_espresso_double_shot) {
                    $espressoMenu = Menu::query()
                        ->where('name', 'like', 'Espresso')
                        ->first();
                    if ($espressoMenu) {
                        Coupon::where('order_id', $order->id)->where('menu_id', $espressoMenu->id)->update(['order_id' => null, 'is_used' => null]);
                    }

                }
            }

            DB::commit();
//            event(new OrderStatusChanged($order));

            if ($order->user->fcm_token) {
                $doubleShot = $order->is_espresso_double_shot ? 'with double shot Espresso' : '';
                $title = "Your coffee is ";
                $content = "Hi {$order->user->name}, your request for {$order->menu->name} {$doubleShot} has been ";

                if ($order->status == OrderStatus::COMPLETED) {
                    $title .= "ready :)";
                    $content .= "completed";
                } elseif ($order->status == OrderStatus::PROCESSING) {
                    $title .= "on processing";
                    $content .= "confirmed";
                } elseif ($order->status == OrderStatus::REJECTED) {
                    $title .= "rejected";
                    $content .= "cancelled";
                }

                sendFcmMessage($order->user, $title, $content, $order->menu->thumbnail);
            }

        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Something went wrong');
        }
        return $this->sendSuccessResponse($order, 'order ' . $status);
    }

    /**
     * cancels all pending and processing requests all at a time from barista end
     *
     * @return JsonResponse
     */
    public function cancelAllPendingOrders()
    {
        DB::beginTransaction();
        try {
            $pendingOrders = Order::whereIn('status', [OrderStatus::PENDING, OrderStatus::PROCESSING])->get();

            $baristaOrderService = new BaristaOrderService();

            $baristaOrderService->updateEachOrderStatus($pendingOrders);

            DB::commit();
            return $this->sendSuccessResponse([], 'All pending and processing orders has been canceled.');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Something went wrong');
        }
    }

        /**
     * cancels all pending and processing requests all at a time from barista end
     *
     * @return JsonResponse
     */
    public function completeAllProcessingOrders()
    {
        DB::beginTransaction();
        try {
            $pendingOrders = Order::where('status', OrderStatus::PROCESSING)->get();

            $baristaOrderService = new BaristaOrderService();

            $baristaOrderService->markAllOrdersAsCompleted($pendingOrders);

            DB::commit();
            return $this->sendSuccessResponse([], 'All processing orders has been completed.');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Something went wrong');
        }
    }


    public function placeOrder(Request $request)
    {
        $user = User::find($request->get('employees'));
        $menu = Menu::find($request->get('menu_id'));

        if (blank($menu) || !$menu->is_active) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Coffee not available, It is out of market now.');
        }
        if (blank($user)) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Please select a user.');
        }

        $availableCoupon = $user->availableCoupons()->where('menu_id', $request->menu_id)->orderBy('created_at', 'asc')->take($menu->required_coupon)->get();

        if ($availableCoupon->count() < $menu->required_coupon) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'User don\'t have enough coupon.');
        }

        DB::beginTransaction();
        try {
            $order = Order::create(array_merge($request->except(['_token', 'menu_id', 'employees', 'menu_quantity']), [
                'user_id' => $user->id,
                'menu_id' => (int)$request->menu_id,
                'status' => OrderStatus::PENDING
            ]));
            $coupon = $availableCoupon->each->update(['is_used' => true, 'order_id' => $order->id]);
            DB::commit();
            return $this->sendSuccessResponse($order);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

    public function customerList(Request $request)
    {
        $employee = User::query();
        $allMenu = Menu::ActiveMenus()->get();
        $search = $request->get('search');

        if (!blank($search)) {
            $employee = $employee->where('name', 'LIKE', '%' . $search . '%');
        }
        $employee = $employee->get();
        $data = compact('employee', 'allMenu');
        return $this->sendSuccessResponse($data);
    }

    public function showEmployeesForOrder(Request $request)
    {
        $employee = User::query();

        $search = $request->search;
        $selected_emp = $request->selected_emp;

        if (blank($selected_emp)) {
            $selected_emp = [];
        }

        if (!blank($search)) {
            $employee = $employee->where('name', 'LIKE', '%' . $search . '%');
        }
        $employee = $employee->get();

        $data = compact('employee', 'selected_emp');
        return $this->sendSuccessResponse($data);
    }

    /**
     * Get coffee analytics data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function analytics(Request $request)
    {
        try {
            $coffee_id = $request->get('coffee_id', 'all');
            $start_date = $request->get('start_date') ? Carbon::parse($request->get('start_date')) : Carbon::now()->subDays(6);
            $end_date = $request->get('end_date') ? Carbon::parse($request->get('end_date')) : Carbon::now();

            // Get coffee stats
            $query = Menu::query();

            // Filter by coffee_id if specified
            if ($coffee_id !== 'all') {
                // Check if it's a comma-separated list of IDs
                if (strpos($coffee_id, ',') !== false) {
                    $coffeeIds = explode(',', $coffee_id);
                    $query->whereIn('id', $coffeeIds);
                } else {
                    $query->where('id', $coffee_id);
                }
            }

            $coffeeStats = $query->withCount(['orders' => function ($order) use ($start_date, $end_date) {
                $order->whereBetween('updated_at', [$start_date->startOfDay(), $end_date->endOfDay()]);
            }])->get()->map(function ($coffee) use ($start_date, $end_date) {
                $coffee->total_requested = Order::where('menu_id', $coffee->id)
                    ->whereBetween('created_at', [$start_date->startOfDay(), $end_date->endOfDay()])
                    ->count();

                $coffee->request_completed = Order::where('menu_id', $coffee->id)
                    ->where('status', OrderStatus::COMPLETED)
                    ->whereBetween('updated_at', [$start_date->startOfDay(), $end_date->endOfDay()])
                    ->count();

                $coffee->request_rejected = Order::where('menu_id', $coffee->id)
                    ->where('status', OrderStatus::REJECTED)
                    ->whereBetween('updated_at', [$start_date->startOfDay(), $end_date->endOfDay()])
                    ->count();

                return $coffee;
            });

            // Get consumption statistics
            $reportService = new ReportService();
            $consumptionStats = $reportService->getCoffeeConsumptionStats($start_date->format('Y-m-d'), $end_date->format('Y-m-d'), $coffee_id);

            // Get time-based consumption analytics
            list($series, $categories) = $reportService->getCoffeeConsumeAnalytics($start_date, $end_date, $coffee_id);

            $data = [
                'coffeeStats' => $coffeeStats,
                'consumptionStats' => [
                    'series' => $consumptionStats['series'],
                    'categories' => $consumptionStats['categories'],
                    'totals' => $consumptionStats['totals']
                ],
                'series' => $series,
                'categories' => array_values($categories)
            ];

            return $this->sendSuccessResponse($data);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

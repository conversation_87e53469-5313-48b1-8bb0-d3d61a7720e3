<?php

namespace App\Http\Controllers\ApiV1\Barista;

use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Menu;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;

class BaristaMenuController extends ApiController
{
    public function index()
    {
        $allMenu = Menu::all();
        return $this->sendSuccessResponse($allMenu);
    }

    public function update(Request $request)
    {
        try {
            $menu = Menu::findOrFail($request->id);
            $menu->is_active = $request->is_active;
            $menu->save();
            return $this->sendSuccessResponse($menu, 'Menu Updated Successfully');
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }

    }
}

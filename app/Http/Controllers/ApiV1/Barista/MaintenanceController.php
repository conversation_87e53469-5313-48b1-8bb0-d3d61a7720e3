<?php

namespace App\Http\Controllers\ApiV1\Barista;

use App\Constants\Role;
use App\Http\Controllers\ApiV1\ApiController;
use App\Jobs\SendMaintenanceNotificationToUsers;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class MaintenanceController extends ApiController
{
    /**
     * changes the maintenance status from barista end
     *
     * @return JsonResponse
     */
    public function handleMaintenance()
    {
        try {
            $isMaintenance = User::where('role', Role::BARISTA)->first()->is_maintenance;

            if ($isMaintenance == 0) {
                User::where('role', Role::BARISTA)
                    ->update([
                        'is_maintenance' => 1
                    ]);

                //send Slack notification to all users
                SendMaintenanceNotificationToUsers::dispatch(auth()->user(), !$isMaintenance);

                return $this->sendSuccessResponse(["is_maintenance" => true, "type" => 'warning'], "Maintenance mode turned on.");
                // send slack notification
            } else {
                User::where('role', Role::BARISTA)
                    ->update([
                        'is_maintenance' => 0,
                    ]);

                //send Slack notification to all users
                SendMaintenanceNotificationToUsers::dispatch(auth()->user(), !$isMaintenance);

                return $this->sendSuccessResponse(["is_maintenance" => false, "type" => 'success'], "Maintenance mode turned off.");
            }
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }

    }
}

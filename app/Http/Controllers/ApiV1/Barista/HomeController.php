<?php


namespace App\Http\Controllers\ApiV1\Barista;


use App\Constants\OrderStatus;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class HomeController extends ApiController
{
    public function index(Request $request)
    {
        $orders = Order::with('menu:id,is_active,required_coupon,name','user:id,is_available,name,designation')
            ->whereIn('status',[OrderStatus::PENDING,OrderStatus::PROCESSING])
            ->orderBy('created_at', 'asc')
            ->get()->map(function ($order, $index) {
                $order->time_changed_format = "{$order->updated_at->diffForHumans()}";
                $order->serial_number = $index + 1;
                return $order;
            });
        $completedOrdersCount = (new Order())->getCompletedOrderCountByToday();
        $data = compact('orders','completedOrdersCount');
        return $this->sendSuccessResponse($data);
    }

    public function getPendingOrders()
    {
        $orders = Order::with('menu','user')
            ->where('status','=',OrderStatus::PENDING)
            ->orderBy('created_at', 'ASC')
            ->get();
        return $this->sendSuccessResponse($orders);
    }

    public function getProcessingOrders()
    {
        $orders = Order::with('menu','user')
            ->where('status','=',OrderStatus::PROCESSING)
            ->orderBy('created_at', 'asc')
            ->get();
            return $this->sendSuccessResponse($orders);
    }
}

<?php


namespace App\Http\Controllers\ApiV1;


use App\Constants\Role;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class ApiController extends Controller
{

    public function sendSuccessResponse($data, $message = null, $code = 200, $headers = []): JsonResponse
    {
        return $this->sendResponse($data, $message, $code, $headers);
    }

    public function sendErrorResponse($code, $message = null, $data = [], $headers = []): JsonResponse
    {
        return $this->sendResponse($data, $message, $code, $headers);
    }

    public function getUser($guard = 'api')
    {
        return auth()->guard('api');
    }

    private function sendResponse($data, $message, $code, $headers): JsonResponse
    {
        $response = compact('data', 'message', 'code');
        return response()->json($response, $code, $headers);
    }

    public function sendMenuResponse($data, $message = null, $code = 200, $headers = [] ): JsonResponse
    {
        $is_maintenance =  User::where('role', Role::BARISTA)->where('is_maintenance', 1)->exists();
//
//        if ($is_maintenance){
//            $data = [];
//        }

        $response = compact('data', 'message', 'code', 'is_maintenance');

        return response()->json($response, $code, $headers);
    }

    public function sendMaintenanceResponse($data, $message = null, $code = 200, $headers = [] ): JsonResponse
    {
        $is_barista_maintenance =  User::where('role', Role::BARISTA)->where('is_maintenance', 1)->exists();

        $response = compact('data', 'message', 'code', 'is_barista_maintenance');

        return response()->json($response, $code, $headers);
    }
}

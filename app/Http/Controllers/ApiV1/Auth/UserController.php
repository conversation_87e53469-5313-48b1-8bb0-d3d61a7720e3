<?php


namespace App\Http\Controllers\ApiV1\Auth;


use App\Http\Controllers\ApiV1\ApiController;
use App\Http\Requests\ChangePassRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegistrationRequest;
use App\Http\Requests\ResetPassRequest;
use App\Models\User;
use App\Services\UserAuthenticationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Resend;

class UserController extends ApiController

{
    private $authenticationService;

    public function __construct(UserAuthenticationService $authenticationService)
    {
        $this->authenticationService = $authenticationService;
    }

    public function register(RegistrationRequest $request)
    {
        return $this->authenticationService->registrationHandler($request);
    }

    public function login(LoginRequest $request)
    {
        return $this->authenticationService->loginHandler($request);
    }

    public function logout()
    {
        return $this->authenticationService->logoutHandler();
    }

    public function changePassword(ChangePassRequest $request)
    {
        try {
            $response = $this->authenticationService->handlePassChange($request);
            if ($response['type']) {
                auth()->user()->update(['password' => $response['content']]);

                return $this->sendSuccessResponse('', 'password changed successfully');
            }

            return $this->sendErrorResponse(500, $response['content']);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(500, $exception->getMessage());
        }

    }

    public function resetPasswordMail(Request $request)
    {
        try {

            $user = DB::table('users')->where('email', '=', $request->get("email"))->exists();
            if (!$user) {
                return $this->sendErrorResponse(404, 'User not found');
            }

            $token = Str::random(60);
            $email = $request->get("email");

            DB::table('password_resets')->insert([
                'email' => $email,
                'token' => $token,
                'created_at' => Carbon::now()
            ]);

            $success = $this->sendResetEmail($email, $token);
            return $this->sendSuccessResponse('', 'Reset password link sent on your email.');
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(500, $exception->getMessage());

        }
    }

    private function sendResetEmail($email, $token)
    {
        $user = DB::table('users')->where('email', $email)->select('email')->first();
        $link = config('app.domain') . '/reset-password?token=' . $token . '&email=' . urlencode($user->email);

        try {
            $resend = Resend::client(config('app.resend_api_key'));

            $resend->emails->send([
                'from' => config('mail.from.address'),
                'to' => $email,
                'subject' => 'Reset WPCafe Password',
                'html' => (new \App\Mail\PassResetMail($link, $email))->render()
            ]);

//            Mail::to($email)->send(new \App\Mail\PassResetMail($link, $email));
            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::info($e->getMessage());
            return false;
        }
    }

    public function validateToken($token)
    {
        $tokenData = DB::table('password_resets')->where('token', $token)->first();

        if (!$tokenData) {
            return $this->sendErrorResponse(404, 'Token is invalid');
        }

        return $this->sendSuccessResponse($tokenData->token, 'Token is valid');
    }

    public function resetPassword(ResetPassRequest $request)
    {
        try {
            $token = $request->get("token");
            $token = DB::table('password_resets')->where('token', $token)->first();

            if (blank($token)) {
                return $this->sendErrorResponse(404, 'Invalid token.Please try again');
            }

            DB::beginTransaction();
            $user = User::where('email', $token->email)->first();

            if (blank($user)) {
                return $this->sendErrorResponse(404, 'User not found');
            }

            $user->password = $request->get("new_password");
            $user->save();

            DB::commit();
            DB::table('password_resets')->where('email', $user->email)->delete();

            return $this->sendSuccessResponse($user, 'Password reset successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->sendErrorResponse(500, $exception->getMessage());
        }

    }
}

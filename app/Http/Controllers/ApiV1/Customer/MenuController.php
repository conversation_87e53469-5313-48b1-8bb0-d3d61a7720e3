<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Constants\LogType;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Menu;
use App\Services\MenuService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class MenuController extends ApiController
{
    private $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    public function index()
    {
        $menus = $this->menuService->getAllMenus();

        return $this->sendMenuResponse(compact('menus'));
    }

    public function getSingleMenu($id): JsonResponse
    {
        $menu = $this->menuService->getMenu($id);
        return $this->sendMenuResponse($menu);
    }

    public function showCoffeeConversion(): JsonResponse
    {
        try {
            $items = $this->menuService->showConversion();
            return $this->sendSuccessResponse($items);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

    public function saveCoffeeConversion(Request $request)
    {
        $fromCoffee = Menu::findOrFail(data_get($request, 'from_coffee_id'));
        $fromQty = data_get($request, 'from_quantity', 0);

        $toCoffee = Menu::findOrFail(data_get($request, 'to_coffee_id'));
        $toQty = data_get($request, 'to_coffee_quantity', 0);
        $toCouponCount = (int)$toQty * $toCoffee->required_coupon;
        $availableCoupons = auth()->user()->availableCoupons()->where('menu_id', $fromCoffee->id)->take($toCouponCount)->get();


        if ($availableCoupons->count() < $toCouponCount) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }

        DB::beginTransaction();
        try {

            $availableCoupons->each->update([
                "menu_id" => $toCoffee->id
            ]);
            $fromCoffee->addLog([
                'log_manager_id' => auth()->user()->id,
                'message'        => "{$fromCoffee->name}({$fromQty}) to {$toCoffee->name}({$toQty})",
                'action_type'    => LogType::CONVERT,
                'log_owner_id'   => auth()->user()->id
            ]);

            if ($request->extra_coffee && $request->extra_coffee_quantity) {

                $this->menuService->extraCoffeeConvert($request,$fromQty);
            }
            DB::commit();

        } catch (\Exception $exception) {

            DB::rollBack();
        }
        return [
            "converted_coffee" => $availableCoupons->toArray() ? $availableCoupons->toArray() : null
        ];
    }
}

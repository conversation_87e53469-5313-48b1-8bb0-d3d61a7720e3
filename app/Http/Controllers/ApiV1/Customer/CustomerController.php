<?php


namespace App\Http\Controllers\ApiV1\Customer;


use App\Http\Controllers\ApiV1\ApiController;
use App\Http\Resources\CustomerResource;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CustomerController extends ApiController
{
    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $customers = User::query()->where('role', User::EMPLOYEE)->get();
            return $this->sendSuccessResponse(CustomerResource::collection($customers),
                'customers fetched successfully', Response::HTTP_OK);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse($exceptions->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

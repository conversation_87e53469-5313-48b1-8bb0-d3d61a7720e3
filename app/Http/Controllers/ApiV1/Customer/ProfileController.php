<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Http\Controllers\ApiV1\ApiController;
use App\Http\Requests\ProfileUpdateRequest;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ProfileController extends ApiController
{
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();
            return $this->sendMaintenanceResponse($user,
                'User fetched successfully', Response::HTTP_OK);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse($exceptions->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(Request $request, $id)
    {
        try {
            // Prevent users from information update if not logged in
            if ($id === auth()->user()->id) {
                return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'You must logged in to update user info');
            }

            $user = User::query()->findOrFail($id);
            if ($request->password !== 'undefined') {
                $user->update($request->all());
            }
            $user->update($request->except(['password']));
            if ($request->hasFile('avatar')) {
                $user->addMediaFromRequest('avatar')->toMediaCollection(User::AVATAR_COLLECTION);
            }
            if (blank(data_get($user, 'slack_id', null)) && !blank($userEmail = data_get($request, 'email'))) {
                $this->updateSlackId($userEmail);
            }

            return $this->sendSuccessResponse($user, 'user updated successfully');
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse($exceptions->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updateSlackId($email)
    {
        Artisan::call("update:user-slack-id", ['email' => $email]);
    }
}

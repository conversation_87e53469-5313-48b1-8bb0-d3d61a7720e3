<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Constants\CoffeeTransferStatus;
use App\Constants\LogType;
use App\Http\Controllers\ApiV1\ApiController;
use App\Http\Requests\CoffeeTransferRequest;
use App\Jobs\SlackNotificationMessage;
use App\Models\CoffeeTransfer;
use App\Models\Coupon;
use App\Models\Menu;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;


class CoffeeTransferController extends ApiController
{
    public function getGiftHistory(Request $request): JsonResponse
    {
        try {
            $query = CoffeeTransfer::query()
                ->where('sender_id', auth()->user()->id)
                ->with(['receiver'])
                ->orderBy('created_at', 'desc');

            // Filter by receiver_id if provided
            if ($request->has('receiver_id') && !empty($request->receiver_id)) {
                $receiverIds = explode(',', $request->receiver_id);
                $query->whereIn('receiver_id', $receiverIds);
            }

            // Filter by coffee_id if provided
            if ($request->has('coffee_id') && !empty($request->coffee_id)) {
                $coffeeIds = explode(',', $request->coffee_id);
                $query->where(function($q) use ($coffeeIds) {
                    foreach ($coffeeIds as $coffeeId) {
                        $q->orWhereJsonContains('items->id', (int)$coffeeId);
                    }
                });
            }

            // Filter by date range if provided
            if ($request->has('start_date') && !empty($request->start_date)) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }

            if ($request->has('end_date') && !empty($request->end_date)) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            $giftHistory = $query->get()->map(function ($transfer) {
                $transfer->time_changed_format = "{$transfer->created_at->diffForHumans()} | {$transfer->created_at->format('h:i A')}";
                $transfer->date_formatted = "{$transfer->created_at->format('d M Y')}";
                return $transfer;
            });

            return $this->sendSuccessResponse($giftHistory);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'Something went wrong: ' . $exception->getMessage());
        }
    }

    public function showCoffeeTransferPageData(Request $request): JsonResponse
    {
        try {
            $employees = User::select('id', 'name', 'email', 'designation')
                ->where('id', '!=', auth()->user()->id)
                ->orderBy('name', "ASC")
                ->get();
            $allMenu = Menu::ActiveMenus()->with(['coupons' => function ($q) {
                return $q->where('user_id', auth()->user()->id)->where('is_used', 0);
            }])->get()->map(function ($menu) {
                $menu->availableCoupon = $menu->availableCoupons();
                return $menu;
            })->sort(function ($a, $b) {
                return $b->availableCoupon <=> $a->availableCoupon;
            })->values();

            $data = compact('employees', 'allMenu');
            return $this->sendSuccessResponse($data);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

    public function saveCoffeeTransferData(CoffeeTransferRequest $request)
    {
        try {
            $receiverId = data_get($request, 'receiver_id');
            $sender = auth()->user();

            // Prevent users from transferring coffee to themselves
            if ($sender->id == $receiverId) {
                return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'You cannot send coffee to yourself');
            }

            $receiver = User::findOrFail(data_get($request, 'receiver_id'));
            $sendCoffee = Menu::findOrFail(data_get($request, 'coffee_id'));
            $sendCoffeeQty = data_get($request, 'coffee_quantity', 0);

            $sendCouponCount = (int)$sendCoffeeQty * $sendCoffee->required_coupon;
            $availableCoupons = auth()->user()->availableCoupons()->where('menu_id', $sendCoffee->id)->take($sendCouponCount)->get();

            if ($availableCoupons->count() < $sendCouponCount) {
                return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
            }

            DB::beginTransaction();

            $transferData = [
                'sender_id' => auth()->user()->id,
                'receiver_id' => $receiver->id,
                'message' =>  "({$sendCoffeeQty}) {$sendCoffee->name} from " . auth()->user()->name,
                'items' => [
                    'id' => $sendCoffee->id,
                    'name' => $sendCoffee->name,
                    'req_coupon' => $sendCoffee->required_coupon,
                    'qty' => $sendCoffeeQty,
                    'thumb' => $sendCoffee->thumbnail
                ],
                'status' => CoffeeTransferStatus::PENDING,
            ];
            $coffeeTransfer = CoffeeTransfer::create($transferData);
            $coffeeTransfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $coffeeTransfer->generateSendMessage(),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => auth()->user()->id
            ]);
            $coffeeTransfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $coffeeTransfer->generateMessage(),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => $receiver->id
            ]);

            //coupon
            $availableCoupons->each->update([
                "is_used" => true,
                "coffee_transfer_id" => $coffeeTransfer->id
            ]);
            DB::commit();

            // send slack notification
            if (!blank($receiver->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($coffeeTransfer->generateMessage(), $receiver);
                SlackNotificationMessage::dispatch($payload);
            }
            if (!blank(auth()->user()->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($coffeeTransfer->generateSendMessage(), auth()->user());
                SlackNotificationMessage::dispatch($payload);
            }


            return $this->sendSuccessResponse([], 'Gift sent');

        } catch (\Exception $exception) {
            Log::alert($exception->getMessage() . $exception->getFile() . $exception->getLine());
            DB::rollBack();
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

    public function getPendingCoffeeTransferData(Request $request)
    {
        $user = Auth::user();
        $pendingTransfers = CoffeeTransfer::query()
            ->where('status', CoffeeTransferStatus::PENDING)
            ->orderBy('created_at', 'asc')
            ->where('receiver_id', $user->id)
            ->get()
            ->map(function ($transfer, $index) {
                $transfer->time_changed_format = "{$transfer->updated_at->diffForHumans()} | {$transfer->updated_at->format('h:i A')}";
//                $transfer->sender = $transfer->sender()->first();
//                $transfer->receiver = $transfer->receiver()->first();
                return $transfer;
            });

        return $this->sendSuccessResponse($pendingTransfers);
    }

    public function cancelPendingTransfer(CoffeeTransfer $transfer)
    {
        if ($transfer->status !== CoffeeTransferStatus::PENDING) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }
        if (auth()->user()->id !== $transfer->receiver_id) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }
        try {
            DB::beginTransaction();
            $transfer->update(['status' => CoffeeTransferStatus::REJECTED]);
            $transfer->coupons()->update([
                "is_used" => false,
                "coffee_transfer_id" => null
            ]);
            $transfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $transfer->generateRejectedMessage('receiver'),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => auth()->user()->id
            ]);
            $transfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $transfer->generateRejectedMessage('sender'),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => $transfer->sender_id
            ]);
            DB::commit();

            // send slack notification
            if (!blank($transfer->sender->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($transfer->generateRejectedMessage('sender'), $transfer->sender);
                SlackNotificationMessage::dispatch($payload);
            }
            if (!blank(auth()->user()->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($transfer->generateRejectedMessage('receiver'), auth()->user());
                SlackNotificationMessage::dispatch($payload);
            }

        } catch (\Exception $exception) {
            DB::rollBack();
            logger($exception->getMessage() . $exception->getFile() . $exception->getLine());
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }
    public function acceptTransfer(CoffeeTransfer $transfer)
    {
        if ($transfer->status !== CoffeeTransferStatus::PENDING) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }
        if (auth()->user()->id !== $transfer->receiver_id) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }
        try {
            DB::beginTransaction();
            $transfer->update(['status' => CoffeeTransferStatus::ACCEPTED]);
            $transfer->coupons()->get()->each(function ($coupon) use ($transfer) {
                $newCoupon = new Coupon();
                $newCoupon->code = 'T' . substr($coupon->code, 1);
                $newCoupon->user_id = auth()->user()->id;
                $newCoupon->menu_id = $coupon->menu_id;
                $newCoupon->expire_at = $coupon->expire_at;
                $newCoupon->is_used = false;
                $newCoupon->gift_id = null;
                $newCoupon->coffee_transfer_id = $transfer->id;
                $newCoupon->save();
            });

            $transfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $transfer->generateAcceptedMessage('receiver'),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => auth()->user()->id
            ]);
            $transfer->addLog([
                'log_manager_id' => auth()->user()->id,
                'message' => $transfer->generateAcceptedMessage('sender'),
                'action_type' => LogType::TRANSFER,
                'log_owner_id' => $transfer->sender_id
            ]);

            DB::commit();

            // send slack notification
            if (!blank($transfer->sender->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($transfer->generateAcceptedMessage('sender'), $transfer->sender);
                SlackNotificationMessage::dispatch($payload);
            }
            if (!blank(auth()->user()->slack_id)) {
                $payload = $this->generatePayloadForDirectMessage($transfer->generateAcceptedMessage('receiver'), auth()->user());
                SlackNotificationMessage::dispatch($payload);
            }

        } catch (\Exception $exception) {
            DB::rollBack();
            logger($exception->getMessage() . $exception->getFile() . $exception->getLine());
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'something went wrong');
        }
    }

    private function generatePayloadForDirectMessage($message, User $user): array
    {
        return [
            "channel"=> $user->slack_id,
            "text" => $message
        ];
    }
}

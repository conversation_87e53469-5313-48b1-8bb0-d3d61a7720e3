<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Log;
use App\Models\UserNotifications;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class NotificationController extends ApiController
{
    /**
     * returns user notification preferences
     * @return JsonResponse
     */
    public function getUserNotificationSettings()
    {
        try {
            $user = auth()->user();
            $preference = [
                'maintenance_mode' => $user->availableNotificationChannel->maintenance_mode,
                'order_status' => $user->availableNotificationChannel->order_status
            ];
            return $this->sendSuccessResponse($preference);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $exceptions->getMessage());
        }
    }
    /**
     * returns user notification preferences
     * @return JsonResponse
     */
    public function updateUserNotificationSettings(Request $request)
    {
        try {
            $user = $request->user();
            $data = [
                'maintenance_mode' => $request->get('maintenance_mode', $user->availableNotificationChannel->maintenance_mode),
                'order_status' => $request->get('order_status', $user->availableNotificationChannel->order_status)
            ];
            // Update or create the user's notification settings
            UserNotifications::updateOrCreate(
                ['user_id' => $user->id],
                $data
            );

            return $this->sendSuccessResponse($data);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $exceptions->getMessage());
        }
    }

    /**
     * returns today's notifications for customer
     *
     * @return JsonResponse
     */
    public function getTodayNotifications()
    {
        try {
            $todayNotifications = Log::userNotificationsToday();
            return $this->sendSuccessResponse($todayNotifications);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $exceptions->getMessage());
        }
    }

    /**
     * returns notifications older than one day for customer
     *
     * @return JsonResponse
     */
    public function getPreviousNotifications()
    {
        try {
            $previousNotifications = Log::userPreviousNotifications();
            return $this->sendSuccessResponse($previousNotifications);
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $exceptions->getMessage());
        }
    }
}

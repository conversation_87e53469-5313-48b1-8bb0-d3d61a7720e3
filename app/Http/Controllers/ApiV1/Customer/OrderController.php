<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Constants\OrderStatus;
use App\Constants\Role;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\Coupon;
use App\Models\Menu;
use App\Models\Order;
use App\Models\User;
use App\Services\OrderService;
use App\Services\ReportService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class OrderController extends ApiController
{
    private $placeOrder;

    public function __construct(OrderService $placeOrder)
    {
        $this->placeOrder = $placeOrder;
    }

    public function history(): JsonResponse
    {
        try {
            $order_history_today = Order::query()->with('menu')->where('user_id', Auth::id())->whereDate('created_at', today())->orderBy('created_at', 'desc')->get();
            $order_history_previous = Order::query()->with('menu')->where('user_id', Auth::id())->where('created_at', '<', today())->orderBy('created_at', 'desc')->paginate(30);
            $data = compact('order_history_today', 'order_history_previous');
            return $this->sendSuccessResponse($data, 'history fetched successfully');
        } catch (Exception $exceptions) {
            return $this->sendErrorResponse($exceptions->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function placeOrder(Request $request): JsonResponse
    {
        try {
            $is_maintenance = User::where('role', Role::BARISTA)->where('is_maintenance', 1)->exists();

            if ($is_maintenance){
                return $this->sendErrorResponse(Response::HTTP_NOT_ACCEPTABLE, 'Wpcafe is currently in maintenance mode', );
            }

            $data = $this->placeOrder->placeOrder($request);

            return $this->sendSuccessResponse($data, 'order placed successfully', Response::HTTP_OK);
        } catch (Exception $exception) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST);
        }
    }

    public function getPendingOrders()
    {
        $user = Auth::user();
        $pendingOrders = Order::query()
            ->whereIn('status', [OrderStatus::PROCESSING, OrderStatus::PENDING])
            ->orderBy('created_at', 'asc')
//            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(function ($order, $index) {
                $order->time_changed_format = "{$order->updated_at->diffForHumans()} | {$order->updated_at->format('h:i A')}";
                $order->serial_number = $index + 1;
                return $order;
            })
            ->where('user_id', $user->id)
            ->load('menu');

        return $this->sendSuccessResponse($pendingOrders);
    }

    public function cancelOrder(Order $order)
    {
        if ($order->status !== OrderStatus::PENDING) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'This order is not pending');
        }
        if ($order->user_id !== auth()->user()->id) {
            return $this->sendErrorResponse(Response::HTTP_BAD_REQUEST, 'This order is not yous to cancel');
        }
        DB::beginTransaction();
        try {
            $espressoMenu = Menu::query()
                ->where('name', 'like', 'Espresso')
                ->first();

            $order->update(['status' => OrderStatus::REJECTED]);
            Coupon::where('order_id', $order->id)->update(['order_id' => null, 'is_used' => null]);
            if ($espressoMenu && $order->is_espresso_double_shot) {
                Coupon::where('order_id', $order->id)->where('menu_id', $espressoMenu->id)->update(['order_id' => null, 'is_used' => null]);
            }

            DB::commit();
            $doubleShot = $order->is_espresso_double_shot ? 'with double shot Espresso' : '';
            $title = "Coffee request cancelled";
            $content = "{$order->user->name} cancelled a request for {$order->menu->name} {$doubleShot}";
            User::where('role', Role::BARISTA)->get()->each(function ($user) use ($title, $content, $order) {
                if (!blank($user->fcm_token)) sendFcmMessage($user, $title, $content, $order->menu->thumbnail);
            });
            return $this->sendSuccessResponse($order, 'Your Order has been canceled');

        } catch (Exception $exception) {
            DB::rollBack();
            return $this->sendErrorResponse('something went wrong');
        }

    }

    public function getUserCoupons()
    {
        $available = auth()->user()->available_coupon_count;
        $consumed = auth()->user()->consumed_coupon_count;
        $coupons = compact('available', 'consumed');
        return $this->sendSuccessResponse($coupons);
    }

    /**
     * Get customer order analytics data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function analytics(Request $request)
    {
        try {
            $user_id = Auth::id();
            $coffee_id = $request->get('coffee_id', 'all');
            $start_date = $request->get('start_date');
            $end_date = $request->get('end_date');

            // Get analytics data from ReportService
            $reportService = new ReportService();
            $data = $reportService->getCustomerOrderAnalytics($user_id, $start_date, $end_date, $coffee_id);

            return $this->sendSuccessResponse($data);
        } catch (\Exception $exception) {
            return $this->sendErrorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

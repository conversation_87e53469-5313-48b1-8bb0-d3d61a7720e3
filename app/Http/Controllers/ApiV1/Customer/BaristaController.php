<?php

namespace App\Http\Controllers\ApiV1\Customer;

use App\Constants\Role;
use App\Http\Controllers\ApiV1\ApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class BaristaController extends ApiController
{
    /**
     * returns if the barista is active or not
     *
     * @return JsonResponse
     */
    public function getBaristaStatus(){
        $isAvailable =  User::where('role', Role::BARISTA)->where('is_available', 1)->exists();

        return $this->sendSuccessResponse(["barista_status" => $isAvailable]);
    }
}

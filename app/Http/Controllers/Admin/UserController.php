<?php

namespace App\Http\Controllers\Admin;

use App\Constants\OrderStatus;
use App\Constants\Role;
use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Menu;
use App\Models\Team;
use App\Models\User;
use App\Models\UserNotifications;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Faker\Provider\Uuid;

class UserController extends Controller
{

    public function index(Request $request)
    {
        $employeeEdit = $request->get('edit');

        $employeeSearch = $request->get('employee_search');
        $users = User::query();

        if (!blank($employeeSearch)) {
            $users = $users->where('name','LIKE','%'. $employeeSearch.'%');
        }
        $users = $users->orderBy('name', 'asc')->paginate(12);

        $teams = Team::all()->pluck('name','id');

        if (!blank($employeeEdit)) {

            $employeeEdit = User::findOrFail($employeeEdit);
        }

        return view('admin.employees', compact('users', 'teams', 'employeeEdit' , 'employeeSearch'));
    }

    public function showListView(Request $request)
    {
        $search = $request->get('search');
        $employees = User::query();

        if (!blank($search)) {
            $employees = $employees->where('name','LIKE','%'. $search.'%');
        }

        if (!blank($request->get('sort'))){
            $employees = $employees->orderBy('name','asc');
        }
        $employees = $employees->paginate(10);

//        $employees = $employees->sortBy(function($emp){
//            return $emp->available_coupon_count;
//        });

        //$employees = $this->paginate($employees);
        return view('admin.employee-list-view', compact('employees','search'));
    }

     public function show(Request $request,User $user)
    {
        $teams = Team::all()->pluck('name','id');

        $team = $user->teams()->first();
        $userMenu = Menu::whereHas('coupons',function ($query) use ($user){
            return $query->where('user_id',$user->id)->where('is_used',false);
        })->get();

        return view('admin.employee',compact('user','team', 'userMenu', 'teams'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            "name" => "required|max:50",
            "teams" => "nullable|array",
            "designation" => "required|max:100",
            "email" => "required|email|unique:users|max:150",
            "password" => "required|min:6",
            "role" => "required",
            "avatar" => "required|image|max:1024"
        ]);

        DB::beginTransaction();

        try {
            $employee = User::create($request->all());

            if ($request->get('teams')) {
                $employee->teams()->sync($request->get('teams'));
            }

            if ($request->hasFile('avatar')) {
                $employee->addMediaFromRequest('avatar')->toMediaCollection(User::AVATAR_COLLECTION);
            }

            DB::commit();

            if (blank(data_get($employee, 'slack_id', null)) && !blank($userEmail = data_get($employee, 'email'))) {
                $this->updateSlackId($userEmail);
            }

            // Create default notifications for the user
            UserNotifications::create([
                'user_id' => $employee->id,
                'maintenance_mode' => false,
                'order_status' => true,
            ]);

            toastr()->success('Success', 'New employee created successfully :)');
        } catch (\Exception $exception) {
            DB::rollBack();
            toastr()->error('Opps', 'Something went wrong');
        }

        return redirect()->back();
    }


    public function update(Request $request, User $user)
    {
        $url = url()->previous();
        $validator = \Validator::make($request->all(), [
            "name" => "required|max:50",
            "teams" => "nullable|array",
            "designation" => "required|max:100",
            "email" => "required|email|max:150|unique:users,email,".$user->id,
            "password" => "nullable|min:6",
            "role" => "required",
            "avatar" => "nullable|image|max:1024"
        ]);

        if ($validator->fails()) {
            return redirect($url)->withErrors($validator, 'editEmployee');
        }

        $excepts = ['avatar', '_token'];

        if (blank($request->get('password'))) {
            $excepts[] = 'password';
        }

        $user->update($request->except($excepts));
        $user->teams()->sync($request->get('teams'));

        if ($request->hasFile('avatar')) {
            $user->addMediaFromRequest('avatar')->toMediaCollection(User::AVATAR_COLLECTION);
        }

        if (blank(data_get($user, 'slack_id', null)) && !blank($userEmail = data_get($user, 'email'))) {
            $this->updateSlackId($userEmail);
        }

        toastr()->success('Employee updated successfully :)', 'Updated');
        return redirect()->route('employees');
    }

    public function destroy(User $user)
    {
        $user->orders()->delete();
        $user->coupons()->delete();
        $user->availableNotificationChannel()->delete();
        $user->delete();
        toastr()->success('Employee deleted successfully!', 'Deleted');
        return redirect()->route('employees');
    }

    public function showCustomerLogin()
    {
        if (auth()->check()) {
            return redirect()->route('customer');
        }

        return view('customer-app.auth.customer_login');
    }

    public function postLoginToCustomerApp(Request $request)
    {
        $this->validate($request, [
            'email' => 'email|required|string',
            'password' => 'required|string',
        ]);

        if (auth()->attempt($request->except('_token'), true)) {
            if (auth()->user()->role === Role::BARISTA) {
                return redirect()->route('barista.dashboard');
            }

            return redirect()->route('customer');
        }

        return redirect()->back()->withErrors(['message' => "Invalid credentials"]);
    }

    public function customerLogout(Request $request)
    {
        auth()->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('customer.login');
    }

    public function couponCountUpdate(Request $request, User $user, Menu $coffee)
    {
        $previousCouponCount = (int) $coffee->availableCoupons($user) ;
        $requestedCouponCount = (int) $request->get('coupon_count', 0);

        if ($requestedCouponCount == $previousCouponCount || $requestedCouponCount < 0) {
            toastr()->error('Coupon count is not valid', 'Invalid Request');
            return redirect()->back();
        }

        $newCouponCount = ($previousCouponCount - $requestedCouponCount);
        DB::beginTransaction();
        try {

            if ($requestedCouponCount > $previousCouponCount) {
                $coupons = [];

                for ($i = 0; $i < abs($newCouponCount * $coffee->required_coupon); $i++) {
                    $coupons[] = [
                        'code' => Uuid::uuid(),
                        'user_id' => $user->id,
                        'menu_id' => $coffee->id,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                }

                Coupon::insert($coupons);
            } else {
                $actualCoupons = abs($newCouponCount) * $coffee->required_coupon;
                $user->availableCoupons()->where('menu_id', $coffee->id)->orderBy('created_at', 'desc')->take($actualCoupons)->forceDelete();
            }

            DB::commit();
            toastr()->success('You have updated coupons successfully.', 'updated!');

        } catch (\Exception $exception) {

            DB::rollBack();
            toastr()->error('Opps', 'Something went wrong');
        }

        return redirect()->back();
    }

    public function couponCountDelete(Request $request, User $user, Menu $coffee)
    {
        DB::beginTransaction();

        try {

            $coupon = $user->availableCoupons()->where('menu_id', $coffee->id)->orderBy('created_at', 'asc')->forceDelete();

            DB::commit();
            toastr()->success('Successfully coupon deleted', 'Deleted !');

        } catch (\Exception $exception) {

            DB::rollBack();
            toastr()->error('Opps', 'Something went wrong');
        }

        return redirect()->back();
    }

    public function saveFcmToken(Request $request)
    {
        auth()->user()->update([
           'fcm_token' => $request->get('fcm_token')
        ]);

        return response()->json([
            'message' => "fcm token saved"
        ]);
    }

    public function paginate($items, $perPage = 12, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    public function updateSlackId($email)
    {
        Artisan::call("update:user-slack-id", ['email' => $email]);
    }
}

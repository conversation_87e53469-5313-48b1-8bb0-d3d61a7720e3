<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Permissions;
use App\Constants\Role;
use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SettingsController extends Controller
{
    public function index()
    {
        $allMenu = Menu::all();
        $recurringSettings = Setting::where('name', 'recurring-gift')->first();
        $admins = User::where('role', Role::ADMIN)->get();
        $permissions = array_values((new \ReflectionClass(Permissions::class))->getConstants());
        return view('admin.settings', compact('allMenu', 'recurringSettings','admins', 'permissions'));
    }

    public function storeRecurringGift(Request $request)
    {
        $request->validate([
            'items' => 'required|array'
        ]);

        DB::beginTransaction();

        try {

            $items = array_values($request->items);

            Setting::updateOrCreate(
                ['name' =>  'recurring-gift'],
                array_merge($request->only(['run_at']), [
                    'data' => json_encode($items),
                    'name' => 'recurring-gift'
                ])
            );

            DB::commit();
            toastr()->success('', 'Recurring Gift Updated successfully :)');

        } catch (\Exception $exception) {

            DB::rollBack();
            toastr()->error('Opps', 'Something went wrong');
        }

        return redirect()->back();
    }

    public function permissionChanges(User $user, Request $request)
    {
        $permissions = $request->get('permission', []);
        $user->update(['permission' => $permissions]);
        toastr()->success('', 'Permission changed successfully');
        return redirect()->back();
    }
}

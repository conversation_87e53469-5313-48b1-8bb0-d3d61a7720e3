<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\MenuCreateRequest;
use App\Models\Menu;
use Illuminate\Http\Request;

class MenuController extends Controller
{

    public function index(Request $request)
    {
        $coffee = null;
        $edit = $request->get('edit');
        $search = $request->get('search');

        if (!blank($edit)) {
            $coffee = Menu::findOrFail($edit);
        }
        $menus = Menu::query();

        if (!blank($search)) {
            $menus->where('name', 'LIKE', '%' . $search . '%');
        }

        $menus = $menus->orderBy('is_active', 'desc')->orderBy('updated_at', 'desc')->get();
        return view('admin.menus', compact('menus', 'coffee', 'search'));
    }


    public function store(MenuCreateRequest $request)
    {
        $menu = Menu::create($request->except(['_token', 'thumbnail']));

        if ($request->hasFile('thumbnail')) {
            $menu->addMediaFromRequest('thumbnail')->toMediaCollection(Menu::THUMBNAIL_COLLECTION);
        }

        toastr()->success('Success', 'New coffee added to menu !');
        return redirect()->back();
    }

    public function update(Request $request, Menu $menu)
    {
        $request->request->set('is_active', $request->has('is_active'));

        $this->validate($request, [
            'name' => 'required|string',
            'thumbnail' => 'nullable|file|max:1024',
            'ingredients' => 'required|array',
            'description' => 'string|max:200',
            'is_active' => 'nullable|boolean',
            'required_coupon' => 'required|numeric|min:1'
        ]);

        if ($request->hasFile('thumbnail')) {
            $menu->addMediaFromRequest('thumbnail')->toMediaCollection(Menu::THUMBNAIL_COLLECTION);
        }

        $menu->update($request->except(['_token', 'thumbnail']));

        toastr()->success('Updated', 'Successfully updated menu item');
        return redirect()->route('coffees.index');
    }

    public function destroy(Menu $menu)
    {
        $menu->delete();
        toastr()->success("Deleted", "Successfully deleted menu");
        return redirect()->route('coffees.index');
    }
}

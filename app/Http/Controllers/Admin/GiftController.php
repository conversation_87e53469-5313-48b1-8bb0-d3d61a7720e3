<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\GiftReceiver;
use App\Models\User;
use App\Services\GiftService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\DB;

class GiftController extends Controller
{
    /**
     * @var GiftService
     */
    private $giftService;

    public function __construct(GiftService $giftService)
    {
        $this->giftService = $giftService;
    }

    /**
     * handles coupon gift for users
     *
     * @param Request $request
     * @return Application|RedirectResponse|Redirector
     */
    public function store(Request $request)
    {
        return $request->all();
        $url = url()->previous();
        $validator = $this->giftService->validateRequest($request);

        if ($validator->fails()) {
            return redirect($url)->withErrors($validator, 'gift');
        }
        $items = array_values($request->items);

        DB::beginTransaction();

        try {
            $couponData = [];
            $gift = $this->giftService->saveGift($request, $items);

            if ($request->user_id[0] === 'all') {
                $allEmployee_id = User::all()->pluck('id');

                foreach ($allEmployee_id as $emp_id) {
                    $giftReceiver = new GiftReceiver();
                    $giftReceiver = $this->giftService->saveGiftReceivers($giftReceiver, $gift, $emp_id);
                    $this->giftService->addGiftLogs($giftReceiver);

                    foreach ($items as $item) {
                        $data = json_decode($item);
                        for ($i = 1; $i <= $data->qty * $data->req_coupon; $i++) {
                            $couponData[] = $this->giftService->makeCouponArray($emp_id, $data, $gift);
                        }
                    }
                }
            } else {
                foreach ($request->user_id as $userId) {
                    $giftReceiver = new GiftReceiver();

                    $giftReceiver = $this->giftService->saveGiftReceivers($giftReceiver, $gift, $userId);
                    $this->giftService->addGiftLogs($giftReceiver);

                    foreach ($items as $item) {
                        $data = json_decode($item);
                        for ($i = 1; $i <= $data->qty * $data->req_coupon; $i++) {
                            $couponData[] = $this->giftService->makeCouponArray($userId, $data, $gift);
                        }
                    }
                }
            }

            Coupon::insert($couponData);

            DB::commit();
            toastr()->success('Success', 'Gift added successfully :)');

        } catch (\Exception $exception) {
            DB::rollBack();
            toastr()->error('Ops', 'Something went wrong');
        }

        return redirect()->back();
    }
}

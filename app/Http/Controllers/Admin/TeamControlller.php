<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\TeamUpdateRequest;
use App\Models\Team;
use Illuminate\Http\Request;

class TeamControlller extends Controller
{

    public function index(Request $request)
    {
        $editTeam = null;
        $search = $request->get('search');

        if (!blank($request->get('edit'))) {
            $editTeam = Team::findOrFail($request->get('edit'));
        }

        $teams = Team::query();

        if (!blank($search)) {
            $teams = $teams->where('name','LIKE','%'.$search.'%');
        }
        $teams = $teams->get();

        return view('admin.teams',compact('teams', 'editTeam', 'search'));
    }


    public function store(Request $request)
    {
        $url = url()->previous();
        $validator = \Validator::make($request->all(), [
            'name' => 'required|string',
            'avatar' => 'required|file|max:1024',
            'description' => 'required|string'
        ]);

        if ($validator->fails()) {
            return redirect($url)->withErrors($validator, 'addTeam');
        }

        $team = Team::create($request->only(['name', 'description']));

        if ($request->hasFile('avatar')) {
            $team->addMediaFromRequest('avatar')
                ->toMediaCollection(Team::THUMBNAIL_COLLECTION);
        }

        toastr()->success('Success', 'New team added successfully :)');
        return redirect()->back();
    }

    public function update(TeamUpdateRequest $request, Team $team)
    {
        $team->update($request->only(['name', 'description']));

        if ($request->hasFile('thumbnail')) {
            $team->addMediaFromRequest('thumbnail')
                ->toMediaCollection(Team::THUMBNAIL_COLLECTION);
        }

        toastr()->success('Team updated successfully :)', 'Updated');
        return redirect()->route('teams.index');
    }


    public function destroy(Team $team)
    {
        $team->delete();
        toastr()->success('Deleted', 'Team deleted successfully!');
        return redirect()->route('teams.index');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Constants\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\Order;
use App\Services\ReportService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class HomeController extends Controller
{

    public function index(Request $request)
    {
        $repostService = new ReportService();

        if ($request->coffeeFilter == null) {
            $request->coffeeFilter = 'all';
        }

        $start = $request->get('start') ? Carbon::parse($request->get('start')) : Carbon::now()->subDays(6);
        $end = $request->get('end') ? Carbon::parse($request->get('end')) : Carbon::now();

        $menu_id = $request->coffeeFilter;
        $search = $request->get('search');

        $coffeeStat = Menu::withCount(['orders' => function ($order) use ($request) {
            $order->where('status', OrderStatus::COMPLETED)
                ->whereBetween('updated_at', [Carbon::parse($request->start)->startOfDay(), Carbon::parse($request->end)->endOfDay()]);
        }])->orderBy('name', 'asc')->get();

        $coffeeAnalytics = $coffeeStat->reduce(function ($result, $coffee) use ($start, $end, $menu_id) {
            $result['totalAvailable'] += round($coffee->allCoffeeCount());
            $result['totalDistributed'] += round($coffee->totalDistributed($start, $end, $menu_id));
//            $result['totalRedeemed'] += round($coffee->totalRedeemed($start, $end, $menu_id));

            return $result;
        }, [
            'totalDistributed' => 0,
            'totalRedeemed' => 0,
            'totalAvailable' => 0,
        ]);


        $stat = Menu::query();

        if ($request->coffeeFilter !== 'all') {
            $stat = $stat->where('id', $request->coffeeFilter);
        }

        $coffeeAnalyticsStat = $this->coffeeAnalytics($stat, $request);
        $coffeeOrderTotal = $this->orderAnalytics($start, $end, $menu_id);
        $coffeeOrderTotalForGuest = $this->guestOrderAnalytics($start, $end, $menu_id);
        list($series, $categories) = $repostService->getCoffeeConsumeAnalytics($start, $end, $menu_id);
        $categories = array_values($categories);
        $totalAvailableCoffee = $coffeeAnalytics['totalAvailable'];

        return view('admin.home', compact(
            'coffeeStat',
            'search',
            'coffeeAnalytics',
            'coffeeAnalyticsStat',
            'categories',
            'series',
            'coffeeOrderTotal',
            'totalAvailableCoffee',
            'coffeeOrderTotalForGuest'
        ));
    }

    public function coffeeAnalytics($stat, $request)
    {

        return $stat->withCount(['orders as request_completed' => function ($order) use ($request) {
            $order->where('created_at', '>=', Carbon::parse($request->start)->startOfDay())
                ->where('updated_at', '<=', Carbon::parse($request->end)->endOfDay())
                ->where('status', OrderStatus::COMPLETED);
        }, 'orders as total_requested' => function ($order) use ($request) {
            $order->where('created_at', '>=', Carbon::parse($request->start)->startOfDay())
                ->where('created_at', '<=', Carbon::parse($request->end)->endOfDay());
        }, 'coupons as coupon_distributed' => function ($coupon) use ($request) {
            $coupon->where('created_at', '>=', Carbon::parse($request->start)->startOfDay())
                ->where('created_at', '<=', Carbon::parse($request->end)->endOfDay())
                ->where('is_used', false);
        }])->orderBy('name', 'asc')->get();
    }

    public function orderAnalytics($start, $end, $coffeeFilter)
    {
        $coffeeFilter = $coffeeFilter == 'all' ? '' : $coffeeFilter;
        $starTime = $start->startOfDay()->toDateTimeString();
        $endTime = $end->endOfDay()->toDateTimeString();

        return Order::query()
            ->when($coffeeFilter, function ($query) use ($coffeeFilter) {
                return $query->where('menu_id', $coffeeFilter);
            })
            ->whereBetween('updated_at', [$starTime, $endTime])
            ->where('status', OrderStatus::COMPLETED)
            ->count();
    }

    public function guestOrderAnalytics($start, $end, $coffeeFilter)
    {
        $coffeeFilter = $coffeeFilter == 'all' ? '' : $coffeeFilter;
        $starTime = $start->startOfDay()->toDateTimeString();
        $endTime = $end->endOfDay()->toDateTimeString();

        return Order::query()
            ->when($coffeeFilter, function ($query) use ($coffeeFilter) {
                return $query->where('menu_id', $coffeeFilter);
            })
            ->whereBetween('updated_at', [$starTime, $endTime])
            ->where([
                "status" => OrderStatus::COMPLETED,
                "for_guest" => 1
            ])
            ->count();
    }

}

<?php

namespace App\Http\Controllers\Admin;

use App\Constants\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\Gift;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Http\Request;

class OrderControlller extends Controller
{

    public function index(Request $request)
    {
        $search = $request->get('search');
        $gifts = Gift::query();

        if (!blank($search)) {
            $gifts = $gifts->WhereHas('sender', function ($query) use ($search) {
                $query->where('name', 'LIKE', '%' . $search . '%');
            })->orWhereHas('receivers.receiverDetails', function ($query) use ($search) {
                $query->where('name', 'LIKE', '%' . $search . '%');
            });
        }

        $data = $gifts->with(
            'onereceiver',
            'sender',
            'receivers',
            'receivers.receiverDetails'
        )
        ->orderBy('id','DESC')
        ->paginate(20);
        return view('admin.orders', compact('data','search'));
    }


    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        //
    }


    public function show(Order $orderHistory)
    {
        //
    }

    public function edit(Order $orderHistory)
    {
        //
    }

    public function update(Request $request, Order $orderHistory)
    {
        //
    }

    public function destroy(Order $orderHistory)
    {
        //
    }

    public function orderAnalytics(Request $request)
    {
        return Order::where('created_at', '>=', Carbon::parse($request->start)->startOfDay())
            ->where('updated_at', '<=', Carbon::parse($request->end)->endOfDay())
            ->where('status', OrderStatus::COMPLETED)->count();
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MenuCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $this->request->set('is_active', $this->has('is_active'));
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string',
            'thumbnail' => 'required|file|max:1024',
            'description' => 'string|max:200',
            'ingredients' => 'required',
            'is_active' => 'nullable|boolean',
            'required_coupon' => 'required|numeric|min:1'
        ];
    }
}

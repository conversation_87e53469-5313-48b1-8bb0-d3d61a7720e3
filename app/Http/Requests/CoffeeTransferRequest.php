<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CoffeeTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'coffee_id' => 'required|exists:menus,id',
            'coffee_quantity' => 'required|integer|min:1',
            'receiver_id' => 'required|exists:users,id',
        ];
    }

    public function messages()
    {
        return [
            'coffee_id.required' => 'The coffee selection is required.',
            'coffee_id.exists' => 'The selected coffee is invalid.',
            'coffee_quantity.required' => 'You must specify the quantity of coffee.',
            'coffee_quantity.integer' => 'The coffee quantity must be an integer.',
            'coffee_quantity.min' => 'The coffee quantity must be at least 1.',
            'receiver_id.required' => 'You must specify a receiver.',
            'receiver_id.exists' => 'The selected receiver is invalid.',
        ];
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    public function handle($request, Closure $next, ...$guards)
    {
        if ($request->expectsJson()){
            $token = str_replace('Bearer ', '', $request->header('Authorization'));

            if ($token == "null" || blank($token) || !auth('sanctum')->check()) {
                return response()->json([
                    'message' => 'Unauthenticated.'
                ], 401);
            }
        }

        return parent::handle($request, $next, []);
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return '/login';
        }

    }
}

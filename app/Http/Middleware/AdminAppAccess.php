<?php

namespace App\Http\Middleware;

use App\Constants\Role;
use Closure;
use Illuminate\Http\Request;

class AdminAppAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $adminUsers = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

        if (auth()->user()->role == Role::ADMIN || in_array(auth()->user()->email, $adminUsers)) {
            return $next($request);
        }

        return redirect()->route('customer');

    }
}

<?php

namespace App\Http\Middleware;

use App\Constants\Permissions;
use Closure;
use Illuminate\Http\Request;

class AdminPermission
{
    private $permissionRouteMap = [
        '*' => ['home','coffees.analytics','gift.store','orders.analytics'],
        Permissions::EMPLOYEES => [
            'employees',
            'employees.list',
            'employees.store',
            'employees.show',
            'employees.update',
            'employees.delete',
            'employees.coupon.update',
            'employees.coupon.delete',
        ],
        Permissions::COFFEE => [
            'coffees.index',
            'coffees.store',
            'coffees.update',
            'coffees.delete',
        ],
        Permissions::TEAMS => [
            'teams.index',
            'teams.store',
            'teams.update',
            'teams.delete',
        ],
        Permissions::REPORT => [
            'orders',
            'orders.analytics',
        ],
        Permissions::SETTINGS => [
            'settings',
            'settings.coupon.recurring-gift-store',
            'settings.permission.store',
        ],
    ];

    public function handle(Request $request, Closure $next)
    {
        $routeName = $request->route()->getName();

        if (in_array($routeName, $this->permissionRouteMap['*'])) {
            return $next($request);
        }

        $userPermission = auth()->user()->permission ?? [];
        $routePermission = $this->getPermissionRoute($request);

        if (blank($userPermission)) {
            return $next($request);
        }

        if (!blank($routePermission) && in_array($routePermission,$userPermission)) {
            return $next($request);
        }

        toastr()->error('Opps', 'You don\'t have enough permission to perform this operation!');
        return redirect()->route('home');
    }

    private function getPermissionRoute($request) {
        $route = $request->route()->getName();
        foreach ($this->permissionRouteMap as $permission => $routes) {
            if (in_array($route, $routes)) {
                return $permission;
            }
        }
    }

}

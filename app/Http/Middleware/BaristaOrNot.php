<?php

namespace App\Http\Middleware;

use App\Constants\Role;
use Closure;
use Illuminate\Http\Request;

class BaristaOrNot
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $allowRoles = [Role::BARISTA, Role::ADMIN];

        if (auth()->check() &&  !in_array(auth()->user()->role, $allowRoles)) {
            return redirect()->route('customer');
        }

        return $next($request);
    }
}

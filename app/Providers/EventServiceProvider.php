<?php

namespace App\Providers;

use App\Events\AllOrderCanceled;
use App\Listeners\SaveOrderCanceledLog;
use App\Models\Gift;
use App\Models\Order;
use App\Observers\GiftObserver;
use App\Observers\OrderObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AllOrderCanceled::class => [
            SaveOrderCanceledLog::class
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
         Order::observe(OrderObserver::class);
    }
}

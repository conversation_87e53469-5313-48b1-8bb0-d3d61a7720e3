<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SlackNotificationMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $token;
    private $payload;
    private $headers;
    private $secret;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payload)
    {
        $this->token = "********************************************************";
        $this->headers = [
            'Authorization' => 'Bearer '. $this->token,
            'Content-Type' => "application/json",
        ];
        $this->payload = $payload;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $response = Http::withHeaders($this->headers)
                ->post('https://slack.com/api/chat.postMessage', $this->payload);

            !$response->ok() && Log::alert($response->json());

        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }

    }
}

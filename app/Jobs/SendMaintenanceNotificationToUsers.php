<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendMaintenanceNotificationToUsers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $actionMaker;
    private bool $isMaintenance;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($actionMaker, $isMaintenance)
    {
        $this->actionMaker = $actionMaker;
        $this->isMaintenance = $isMaintenance;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $employees = User::select('id', 'name', 'email', 'designation', 'slack_id')
            ->orderBy('name', "ASC")
            ->get();

        foreach ($employees as $employee) {
            if ($employee->slack_id && $employee->availableNotificationChannel->maintenance_mode) {
                $payload = $this->generatePayloadForDirectMessage($this->generateMessage($employee), $employee);
                SlackNotificationMessage::dispatch($payload);
            }

        }

    }

    private function generateMessage($receiver): string
    {

        $isSelf = $this->actionMaker->id === $receiver->id;

        if ($this->isMaintenance) {
            return $isSelf
                ? "🚧 Maintenance mode turned on."
                : "🚧 Maintenance mode turned on by {$this->actionMaker->name}.";
        } else {
            return $isSelf
                ? "🚀 Maintenance mode turned off."
                : "🚀 Maintenance mode turned off by {$this->actionMaker->name}.";
        }

    }

    private function generatePayloadForDirectMessage($message, User $user): array
    {
        return [
            "channel"=> $user->slack_id,
            "text" => $message
        ];
    }
}

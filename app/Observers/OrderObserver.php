<?php

namespace App\Observers;

use App\Models\Order;
use App\Services\OrderNotificationService;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function created(Order $order)
    {
        $message = $order->generateMessage();
        $order->addLog([
            'log_manager_id' => auth()->user()->id ?? 2,
            'message' => $message,
            'action_type' => $order->status,
            'log_owner_id' => $order->user_id
        ]);

        if (auth()->user()->availableNotificationChannel->order_status) {
            //notifications
            $notificationService = new OrderNotificationService($order);
            $notificationService->sendSlackNotification();
        }


    }

    /**
     * Handle the Order "updated" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function updated(Order $order)
    {
        $message = $order->generateMessage();
        $order->addLog([
            'log_manager_id' => auth()->user()->id ?? 2,
            'message' => $message,
            'action_type' => $order->status,
            'log_owner_id' => $order->user_id,
        ]);

        if (auth()->user()->availableNotificationChannel->order_status) {
            //notifications
            $notificationService = new OrderNotificationService($order);
            $notificationService->sendSlackNotification();
        }
    }

    /**
     * Handle the Order "deleted" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function deleted(Order $order)
    {
        $order->logs()->delete();

        if (auth()->user()->availableNotificationChannel->order_status) {
            //notifications
            $notificationService = new OrderNotificationService($order);
            $notificationService->sendSlackNotification();
        }
    }

    /**
     * Handle the Order "restored" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function restored(Order $order)
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function forceDeleted(Order $order)
    {
        $order->logs()->delete();

        if (auth()->user()->availableNotificationChannel->order_status) {
            //notifications
            $notificationService = new OrderNotificationService($order);
            $notificationService->sendSlackNotification();
        }
    }
}

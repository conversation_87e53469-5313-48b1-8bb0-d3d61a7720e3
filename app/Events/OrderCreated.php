<?php

namespace App\Events;

use App\Constants\OrderStatus;
use App\Models\Order;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('order.created');
    }

    public function broadcastWith()
    {
        $order = $this->order->load('menu', 'user');
//        $order->reject_url = route('barista.order.status-change', ['orderId' => $order->id,'status' => OrderStatus::REJECTED]);
//        $order->processing_url = route('barista.order.status-change', ['orderId' => $order->id,'status' => OrderStatus::PROCESSING]);
//        $order->delivared_url = route('barista.order.status-change', ['orderId' => $order->id,'status' => OrderStatus::COMPLETED]);
        return $order->toArray();
    }
}

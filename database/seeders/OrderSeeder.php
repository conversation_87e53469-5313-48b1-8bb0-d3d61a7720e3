<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        foreach (User::all() as $user) {
            $coupon = $user->availableCoupons()->first();
            $order = Order::factory()->create([
                'user_id' => $user->id,
                'menu_id' => $coupon->menu_id
            ]);
            $coupon->order()->associate($order);
        }

    }
}

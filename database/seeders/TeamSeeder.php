<?php

namespace Database\Seeders;

use App\Models\Team;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $teams = [
            'Essential Addons' => public_path('assets/img/ea-logo.svg'),
            'NotificationX' => public_path('assets/img/notificationX.png'),
            'Easy.jobs' => public_path('assets/img/easyjobs.png'),
            'Templately' => public_path('assets/img/templately.gif'),
            'BetterDocs' => public_path('assets/img/betterdocs.png'),
            'Flexia' => public_path('assets/img/flexia.svg'),
            'YIVE' => public_path('assets/img/yive.png'),
        ];
        foreach ($teams as $name => $thumbnail) {
            $team = Team::factory()->create([
                'name' => $name,
            ]);
            $team->addMedia($thumbnail)->preservingOriginal()->toMediaCollection(Team::THUMBNAIL_COLLECTION);
        }
    }
}

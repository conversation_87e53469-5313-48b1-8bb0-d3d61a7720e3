<?php

namespace Database\Seeders;

use App\Models\Menu;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $menus = [
            'Cappuccino' => public_path('assets/img/coffee1.png'),
            'Mocha' => public_path('assets/img/coffee2.png'),
            'Latte' => public_path('assets/img/coffee3.png'),
            'Americano' => public_path('assets/img/coffee4.png'),
        ];
        foreach ($menus as $name => $thumbnail) {
            $menu = Menu::factory()->create([
                'name' => $name,
                'required_coupon' => random_int(1, 2),
            ]);
            $menu->addMedia($thumbnail)->preservingOriginal()->toMediaCollection(Menu::THUMBNAIL_COLLECTION);
        }

    }
}

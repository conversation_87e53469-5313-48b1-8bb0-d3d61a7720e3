<?php

namespace Database\Seeders;

use App\Models\Coupon;
use App\Models\Menu;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $menus = Menu::all()->pluck('id')->toArray();
        foreach (User::all() as $user) {
            for ($i = 0; $i < 10; $i++) {
                Coupon::factory()->create(['user_id' => $user->id, 'menu_id' => Arr::random($menus)]);
            }

        }
    }
}

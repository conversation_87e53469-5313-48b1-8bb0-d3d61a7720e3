<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->createAdmin('<PERSON><PERSON><PERSON> Rana', '<EMAIL>', 'wpcafe', 'admin', 'Software Engineer');
        $this->createAdmin('Ar Rana', '<EMAIL>', 'wpcafe', 'admin', 'Software Engineer');
        $this->createAdmin('Mujtaba rumi', '<EMAIL>', 'wpcafe', 'admin', 'Software Engineer');
        User::factory()->create([
            'name' => 'Mujtaba rumi',
            'email' => '<EMAIL>',
            'password' => 'wpcafe',
            'role' => 'employee',
            'designation' => 'Software Engineer'
        ]);
        User::factory(10)->create();
    }

    private function createAdmin($name, $email, $password, $role, $designation)
    {
        User::factory()->create([
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'role' => $role,
            'designation' => $designation
        ]);
    }


}

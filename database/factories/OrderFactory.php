<?php

namespace Database\Factories;

use App\Constants\OrderStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Order;
use Illuminate\Support\Arr;

class OrderFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Order::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        $orderStatus = new \ReflectionClass(OrderStatus::class);
        return [
            'user_id' => $this->faker->randomNumber(),
            'menu_id' => $this->faker->randomNumber(),
            'note' => $this->faker->text,
            'status' => Arr::random(array_values($orderStatus->getConstants())),
            'last_updated_by' => $this->faker->randomNumber(),
        ];
    }
}

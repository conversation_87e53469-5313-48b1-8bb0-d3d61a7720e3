<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Coupon;

class CouponFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Coupon::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'code' => $this->faker->word,
            'user_id' => $this->faker->randomNumber(),
            'menu_id' => $this->faker->randomNumber(),
            'expire_at' => $this->faker->dateTime(),
            'type' => $this->faker->word,
            'is_used' => $this->faker->boolean,
            'gift_id' => $this->faker->randomNumber(),
        ];
    }
}

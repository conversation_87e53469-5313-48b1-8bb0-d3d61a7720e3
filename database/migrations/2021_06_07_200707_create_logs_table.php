<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //purpose of this table is to give the user a beautiful timeline view about his order
        //for instance user will see a timeline
        Schema::create('logs', function (Blueprint $table) {
            $table->id();
            $table->string('action_type')->nullable(); //what action we are logging like barista
            // accpeted/rejected/completed the order
            $table->unsignedBigInteger('log_manager_id')->nullable(); //who created this log?
            $table->unsignedBigInteger('log_owner_id')->nullable(); //who owns this log .. adding this for making query simple!
            $table->unsignedBigInteger('loggable_id')->nullable();
            $table->string('loggable_type')->nullable();
            $table->string('message')->nullable(); //short special message related to the action if needed
            $table->timestamp('read_at')->nullable();
            $table->foreign('log_manager_id')->references('id')->on('users');
            $table->foreign('log_owner_id')->references('id')->on('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('logs');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropGiftTrackersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('gift_trackers');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('gift_trackers', function (Blueprint $table) {
            $table->id();
            $table->integer('receiver_id');
            $table->integer('gift_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }
}

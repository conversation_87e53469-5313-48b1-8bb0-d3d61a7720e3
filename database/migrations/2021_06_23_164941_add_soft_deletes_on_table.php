<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSoftDeletesOnTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('menus', function (Blueprint $table){
            $table->softDeletes();
        });

        Schema::table('users', function (Blueprint $table){
            $table->softDeletes();
        });

        Schema::table('orders', function (Blueprint $table){
            $table->softDeletes();
        });

        Schema::table('gifts', function (Blueprint $table){
            $table->softDeletes();
        });

        Schema::table('coupons', function (Blueprint $table){
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('menus', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('users', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('orders', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('gifts', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('coupons', function (Blueprint $table){
            $table->dropSoftDeletes();
        });
    }
}

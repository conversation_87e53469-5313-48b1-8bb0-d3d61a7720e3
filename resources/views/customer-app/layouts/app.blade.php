<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @include('customer-app.layouts.head')
    @toastr_css
    @yield('customer-head')
</head>
<body>
<div class="wrapper">
    <div class="wpc-body">
        <!-- site header -->
        @if(Request::routeIs('customer.dashboard'))
            @include('customer-app.layouts.header')
        @endif

        @include('customer-app.layouts.sidebar')
        @yield('customer-content')
        @include('customer-app.layouts.footer')
    </div>

</div>
<script>
    window.EVENTS = {
        'orderStatusChanged' : "{{ \App\Events\OrderStatusChanged::class }}"
    };
</script>
@include('customer-app.layouts.script')
@yield('scripts')
@stack('scripts')
</body>
</html>

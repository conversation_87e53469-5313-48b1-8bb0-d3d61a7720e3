<div class="header" style="background-image: url('/wpcafe-app-header-bg.jpg')">
    <div class="header__nav">
        <div class="nav__icon">
            <a href="#"><i class="wpc-icon wpc-bar"></i></a>
        </div>
        <div class="notification__icon">
            <a href="{{ route('customer.notifications')  }}"><i class="wpc-icon wpc-notification"></i></a>
        </div>
    </div>
    <div class="header__greeting">
        <p>
            @php
                $hour = \Carbon\Carbon::now()->hour;
                $user = \Illuminate\Support\Facades\Auth::User();
                $barista = \App\Models\User::where('role', \App\Constants\Role::BARISTA)->where('is_available', 1)->first();
                $checkBaristaStatus = (bool) $barista;
            @endphp
            @if($hour >= 0 && $hour < 6 )
                Good Night
            @elseif($hour >= 6 && $hour < 12 )
                Good Morning
            @elseif($hour >= 12 && $hour < 18 )
                Good Afternoon
            @elseif($hour >= 18 && $hour <= 23 )
                Good Evening
            @endif

        </p>
        <span class="barista_available_status @if($checkBaristaStatus) isOnline @endif "> Barista is @if($checkBaristaStatus)Online @else Offline @endif</span>
        <h2>{{ ucfirst($user->name) }}</h2>
    </div>
</div>

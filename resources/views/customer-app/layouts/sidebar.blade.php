@php
$user = \Illuminate\Support\Facades\Auth::user();

$image_url = $user->avatar;
@endphp
<div class="wpc-navigation__drawer__wrap">
    <div class="wpc-navigation__drawer">
        <a href="#" class="navigation__drawer__closer">
            <i class="wpc-icon wpc-times"></i>
        </a>
        <div class="navigation__drawer__header">
            <div class="navigation__drawer__user">
                <div class="navigation__drawer__user__thumb bgi__property" style="background-image: url('{{$image_url}}')">
                </div>
                <div class="navigation__drawer__user__body">
                    <h4 class="user__name">{{ $user->name }}</h4>
                    <p class="designation">{{ $user->designation }}</p>
                </div>
            </div>
            <div class="navigation__drawer__user__edit">
                <a href="{{route('customer.profile')}}"><i class="wpc-icon wpc-edit"></i></a>
            </div>
        </div>
        <ul class="navigation__menu">
            <li class="navigation__list">
                <a href="{{route('customer.notifications')}}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-notification"></i>
								</span>
                    <span class="text">Notifications</span>
                </a>
            </li>
            <li class="navigation__list">
                <a href="{{ route('customer.coffee') }}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-menu"></i>
								</span>
                    <span class="text">Coffee Menu</span>
                </a>
            </li>
            <li class="navigation__list">
                <a href="{{ route('customer.coffee.conversion') }}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-exchange"></i>
								</span>
                    <span class="text">Coffee Conversion</span>
                </a>
            </li>
            <li class="navigation__list">
                <a href="{{ route('customer.order.history') }}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-order"></i>
								</span>
                    <span class="text">Request History</span>
                </a>
            </li>
            @if(auth()->user()->role === \App\Constants\Role::BARISTA)
                <li class="navigation__list">
                    <a href="{{ route('barista.dashboard') }}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-swap"></i>
								</span>
                        <span class="text">Switch to Barista</span>
                    </a>
                </li>
            @endif
        </ul>
        <ul class="navigation__menu mt-auto">
            <li class="navigation__list">
                <a href="{{route('customer.logout')}}" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-logout"></i>
								</span>
                    <span class="text">Log Out</span>
                </a>
            </li>
        </ul>
    </div>
</div>

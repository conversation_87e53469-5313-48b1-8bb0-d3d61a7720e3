@extends('customer-app.layouts.app')
@section('customer-content')
    @php
    $couponCount = [
        'available' => auth()->user()->available_coupon_count,
        'consumed' => auth()->user()->consumed_coupon_count
    ];
    @endphp
    <div id="customerApp">
        <home-page coupons="{{ json_encode($couponCount) }}" orders="{{ json_encode($pendingOrders) }}"
                   coffees="{{ json_encode($items->values()) }}" modal-url="{{ route("customer.item.modal.show", ":id") }}"
        />
    </div>
    @push('scripts')
        <script>
            window.USER_ID = "{{ auth()->user()->id }}";
        </script>
        <script src="{{ mix('js/customer/home.js') }}"></script>
    @endpush
@endsection
@extends('customer-app.layouts.app')
@section('customer-content')

        <div class="page__header">
            <div class="left__content">
                <a href="{{ route('customer.dashboard') }}" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
            </div>
            <div class="page__title">
                <h5>Edit Profile</h5>
            </div>
            <div class="right__content"></div>
        </div>
        <div class="edit__profile__form">
            <form action="{{ route('customer.profile.update', $user->id) }}" method="post" enctype="multipart/form-data">
                @csrf
                <div class="user__thumb__change">
                    <div class="user__thumb bgi__property" id="profile_image" style="background-image: url('{{$user->avatar}}')">
                    </div>
                    <label class="WpcEditButton">
                        <input type="file" name="avatar" id="avatar" accept="image/*" class="d-none">
                        <span class="Icon">
							  <i class="wpc-icon wpc-edit"></i>
							</span>
                        <span class="Text">Change Photo</span>
                    </label>
                </div>
                @include('customer-app.components.inputs.text', ["name" => "name", "value" =>  old('name', $user->name), 'label' => "Name"])
                @include('customer-app.components.inputs.text', ["name" => "designation", "value" =>  old('designation', $user->designation), 'label' => "Designation"])
                @include('customer-app.components.inputs.email', ["name" => "email", "value" =>  old('email', $user->email), 'label' => "Email", 'readonly' => 'readonly'])
                @include('customer-app.components.inputs.password', ["name" => "password", 'label' => "Password"])

                <button class="WpcButton WpcFilled">Save Changes</button>
            </form>
        </div>

@endsection

@push('scripts')
    <script>
        $(function (){

            $('#avatar').change(function (){
                let file = ($(this).prop('files') &&  $(this).prop('files')[0]) || null ;
                let url = URL.createObjectURL(file);
                // console.log(url)
                $('#profile_image').css("background-image", "url(" + url + ")")
            });
        });
    </script>
@endpush

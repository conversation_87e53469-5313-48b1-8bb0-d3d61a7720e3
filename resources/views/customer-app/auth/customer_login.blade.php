@extends('customer-app.layouts.auth')
@section('customer-content')
    <div class="wpc-body">
        <form action="{{ route('customer.post-login') }}" method="post">
            @csrf
            <div class="account__controller">
                <div class="account__access__content">
                    <div class="logo">
                        <img src="{{ url('CustomerApp/assets/img/site-logo.svg') }}" alt="">
                    </div>
                    @foreach($errors->all() as $message)
                        <div class="alert alert-danger">
                            <strong>{{ $message }}</strong>
                        </div>
                    @endforeach
                    <div class="account__access__form">
                        <div class="form__group">
                            <input type="text" class="form-control fancy__form" placeholder="Email" value="{{ old('email') }}" required name="email">
                        </div>
                        <div class="form__group">
                            <input type="password" class="form-control fancy__form" placeholder="Password"  required name="password">
                        </div>
                        <button class="WpcButton WpcFilled">Sign In</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

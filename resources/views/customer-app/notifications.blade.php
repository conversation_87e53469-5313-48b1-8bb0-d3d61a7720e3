@extends('customer-app.layouts.app')

@section('customer-content')

    <div class="page__header">
        <div class="left__content">
            <a href="{{ route('customer.dashboard') }}" class="back__button"><i class="wpc-icon wpc-angle-left"></i>
                Back</a>
        </div>
        <div class="page__title">
            <h5>Notifications</h5>
        </div>
        <div class="right__content">
            {{--            <a href="#" class="clear__button">Clear all</a>--}}
        </div>
    </div>

    <div class="section__header">
        <h3 class="section__header__title">Today</h3>
    </div>

    <div class="wpc-notificatons">
        <div class="accordion" id="accordionExample">
            @foreach($todayNotifications as $notification)
                 @include('customer-app.components.ui.notification', ["notification" => $notification])
            @endforeach
        </div>
    </div>

    <div class="section__header mt30">
        <h3 class="section__header__title">Previous Notifications</h3>
    </div>

    <div class="wpc-notificatons">
        <div class="accordion" id="accordionExample2">
            @foreach($prevNotifications as $notification)
                @include('customer-app.components.ui.notification', ["notification" => $notification])
            @endforeach
        </div>
    </div>
    {{ $prevNotifications->links('customer-app.layouts.paginator') }}

@endsection


		
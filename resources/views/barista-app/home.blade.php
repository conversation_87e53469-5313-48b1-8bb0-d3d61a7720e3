@extends('barista-app.layouts.app')
@section('barista-content')
    <div class="coupon__statistic">
        <div class="statistic__card">
            <div class="card__top">
                <div class="icon">
                    <i class="wpc-icon wpc-coffee-seeds"></i>
                </div>
                <h4>{{ $orders->where('status',\App\Constants\OrderStatus::PENDING)->count() }}</h4>
            </div>
            <p>Pending Requests</p>
        </div>
        <div class="statistic__card">
            <div class="card__top">
                <div class="icon">
                    <i class="wpc-icon wpc-coffee-cup"></i>
                </div>
                <h4>{{ $orders->where('status',\App\Constants\OrderStatus::COMPLETED)->count() }}</h4>
            </div>
            <p>Completed Requests</p>
        </div>
    </div>
    <audio src="{{ url('noti fication1.mp3') }}" id="audioObject"></audio>
    <div class="order__queue">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="pending-tab" style="cursor: pointer" onclick="getPendingOrders()">Pending Requests ({{ $orders->where('status',\App\Constants\OrderStatus::PENDING)->count() }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="processing-tab" style="cursor: pointer" onclick="getProcessingOrders()">Processing Requests ({{ $orders->where('status',\App\Constants\OrderStatus::PROCESSING)->count() }})</a>
            </li>
        </ul>
        <div class="tab-content" id="baristaApp">
            <div id="barista_order"></div>

        </div>
    </div>
@endsection
@push('barista-scripts')
    <script src="https://code.createjs.com/1.0.0/soundjs.min.js"></script>
    <script>
        window.__ORDERS__ = @json($orders);
        window.AUDIO_FILE = "{{ url('/notification.mp3') }}";
        var soundID = "notificationSound";
        function loadSound () {
            createjs.Sound.registerSound("assets/thunder.mp3", soundID);
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="{{ mix('js/barista-dashboard.js') }}"></script>
    <script>
        $(function (){
            getPendingOrders();
        });
        function getPendingOrders() {
            let url = "{{ route("barista.order.show-pending-order") }}";
            fetch(url).then(res => res.json()).then(({data}) => {
                $('#barista_order').html(data);
                $( "#processing-tab" ).removeClass( "active" );
                $( "#pending-tab" ).addClass( "active" );
                $( "#pending" ).addClass( "show" );
                $( "#processing" ).removeClass( "show" );

            });
        }
        function getProcessingOrders() {
            let url = "{{ route("barista.order.show-processing-order") }}";
            fetch(url).then(res => res.json()).then(({data}) => {
                $('#barista_order').html(data);
                $( "#pending-tab" ).removeClass( "active" );
                $( "#processing-tab" ).addClass( "active" );

                $( "#processing" ).addClass( "show" );
                $( "#pending" ).removeClass( "show" );
            });
        }
    </script>
@endpush
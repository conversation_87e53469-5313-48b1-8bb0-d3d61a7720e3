@extends('barista-app.layouts.app')
@section('barista-content')
    <div class="page__header">
        <div class="left__content">
            <a href="{{ route('barista.dashboard') }}" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
        </div>
        <div class="page__title">
            <h5>Custom Request</h5>
        </div>
        <div class="right__content"></div>
    </div>

        <div class="search__bar">
            <div class="form__group">
                <span class="search__icon"><i class="wpc-icon wpc-search"></i></span>
                <input type="text" placeholder="Search" name="search" value="{{$search}}" id="search_emp" class="form__control">
            </div>
        </div>
    <form action="{{ route('barista.order.place-order') }}" method="post">
        @csrf
        <div class="employer__suggestion">
            <div class="employer__suggestion__title">
                <p>Employee</p>
            </div>
            <div class="ajax_div_employee" id="ajax_div_employee"></div>
        </div>
        <div class="add__order">
            <p class="add__order__title">Type</p>
            <div class="add__order__form">
                <div class="wpc__select">
                    <span><i class="wpc-icon wpc-arrow-down"></i></span>
                    <select name="menu_id">
                        @foreach($allMenu as $menu)
                        <option value="{{ $menu->id }}">{{ $menu->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="item__quantity__wrap">
                    <span class="item__decrease"><i class="wpc-icon wpc-minus"></i></span>
                    <input id="quantity" class="item__vlaue" name="menu_quantity" type="number" readonly value="1" min="1"/>
                    <span class="item__increase"><i class="wpc-icon wpc-plus"></i></span>
                </div>
            </div>
        </div>
        <div class="mt-auto pl15 pr15 pb-4">
            <button type="submit" class="WpcButton WpcFilled">Place Order</button>
        </div>
    </form>
@endsection

@push('barista-scripts')
    <script>
        function showEmployee() {

            let search = $('#search_emp').val();
            let selected_emp = $('input:checked').map(function(){
                return $(this).val();
            }).get();

            $.ajax({
                url: '{{route('barista.order.custom-order.show-employee')}}',
                type: 'POST',
                data: {
                    _token: '{{csrf_token()}}',
                    search: search, selected_emp: selected_emp
                },
                success: function (data) {
                    $("#ajax_div_employee").html(data.data);
                }
            });
        }

        $(function (){
            showEmployee();

            $('.item__increase').on('click',function(e){
                e.preventDefault();
                var $qty=$(this).closest('.item__quantity__wrap').find('.item__vlaue');
                var currentVal = parseInt($qty.val());
                if (!isNaN(currentVal)) {
                    $qty.val(currentVal + 1);
                }
            });

            $('.item__decrease').on('click',function(e){
                e.preventDefault();
                var $qty=$(this).closest('.item__quantity__wrap').find('.item__vlaue');
                var currentVal = parseInt($qty.val());
                if (!isNaN(currentVal) && currentVal > 1) {
                    $qty.val(currentVal - 1);
                }
            });

            $('#search_emp').keyup(function (event){
                event.preventDefault();
                if (event.keyCode === 13) {
                    showEmployee();
                }

            });
        });
    </script>
@endpush
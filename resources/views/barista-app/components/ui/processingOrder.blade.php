<div class="tab-pane fade" id="processing" role="tabpanel" aria-labelledby="processing-tab">
    <div class="tap__to__order__page">
        <a href="{{route('barista.order.by-status', ['status' => \App\Constants\OrderStatus::PROCESSING])}}"><i class="wpc-icon wpc-menu"></i></a>
    </div>
    <div class="ordered__menu">
        @foreach($orders as $order)
        <div class="ordered__item">
            <div class="menu__details">
                <div class="ordered__item__thumb bgi__property" style="background-image: url('{{$order->menu->thumbnail}}')"></div>
                <div class="orderer__thumb bgi__property" style="background-image: url('{{$order->user->avatar}}')"></div>
                <div class="orderer__info">
                    <h5>{{ $order->user->name }}</h5>
                    <span>{{ $order->user->designation }}</span>
                    @if($order->for_guest)
                        <span class="for__guest">For Guest</span>
                    @endif
                </div>
            </div>
            <div class="order__info">
                <div class="order__info__details">
                    <h5>{{ $order->menu->name }}</h5>
                    <span class="order__id">#request-{{$order->id}}</span>
                </div>
                <div class="order__info__button">
                    <a href="{{ $order->reject_url }}" class="WpcButton mr-1">Cancel</a>
                    <a href="{{ $order->delivared_url }}" class="WpcButton WpcFilled">Mark as Delivered</a>
                </div>
            </div>
            @if($order->note)
            <div class="order__notes">
                    <p>{{ $order->note }}</p>
            </div>
            @endif
        </div>
        @endforeach
    </div>
</div>


@extends('barista-app.layouts.app')
@section('barista-content')

        <div class="page__header">
            <div class="left__content">
                <a href="{{ route('barista.dashboard') }}" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
            </div>
            <div class="page__title">
                <h5>Coffee Menu</h5>
            </div>
            <div class="right__content">

            </div>
        </div>

        <div>
            <form method="post" id="barista_menu_form" action="{{route('barista.coffee.update')}}">
                @csrf
                <input type="hidden" name="menu_id" id="menu_id" value="">
                <input type="hidden" name="is_active" id="is_active" value="">
                <div class="item__wrap flex__wrap">
                    @foreach($allMenu as $menu)
                        <div class="item">
                            <div class="item__thumb bgi__property" style="background-image: url('{{$menu->thumbnail}}')">
                            </div>
                            <div class="item__body">
                                <h3 class="item__name">{{$menu->name}}</h3>
                                <label>
                                    <input type="checkbox" name="" id="checkbox_{{$menu->id}}" onclick="submitBaristaMenuForm({{$menu->id}})" @if($menu->is_active == 1) checked @endif>
                                    <span>Available Now</span>
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
            </form>
        </div>

@endsection

@push('barista-scripts')
    <script>
        function submitBaristaMenuForm (id) {
            let value = $('#checkbox_'+id).is(':checked');
            $("#menu_id").val(id);
            $("#is_active").val(value ? 1 : 0);
            $('#barista_menu_form').submit();
        }
    </script>
@endpush
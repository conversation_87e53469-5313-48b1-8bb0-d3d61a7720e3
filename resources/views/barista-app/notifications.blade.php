@extends('barista-app.layouts.app')

@section('barista-content')

    <div class="page__header">
        <div class="left__content">
            <a href="{{ route('barista.dashboard') }}" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
        </div>
        <div class="page__title">
            <h5>Notifications</h5>
        </div>
        <div class="right__content">
{{--            <a href="#" class="clear__button">Clear all</a>--}}
        </div>
    </div>

    <div class="wpc-notificatons pb-4">
        <div class="accordion" id="accordionExample2">
            @foreach($notifications as $notification)
                <div class="notification__item">
                        <div class="card">
                            <div class="card-header" id="heading{{$notification->id}}">
                                <h2 class="mb-0">
                                    <p class="btn btn-block text-left">
                                        {{$notification->baristaMessage()}}

                                    </p>
                                </h2>
                            </div>
                        </div>
                        <p class="notification__time">{{$notification->created_at->diffForHumans()}}</p>
                    </div>
            @endforeach
        </div>
    </div>
    {{ $notifications->links('barista-app.layouts.paginator') }}

@endsection

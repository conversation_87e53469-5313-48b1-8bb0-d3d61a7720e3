<div class="header" style="background-image: url('/barista-h-bg.png'); background-size: cover">
    <div class="header__nav">
        <div class="nav__icon">
            <a href="#"><i class="wpc-icon wpc-bar"></i></a>
        </div>
        <div class="notification__icon">
            <a href="{{route('barista.notifications')}}"><i class="wpc-icon wpc-notification"></i></a>
        </div>
    </div>
    <div class="header__greeting">
        <p>
            @php
                $hour = \Carbon\Carbon::now()->hour;
                $user = \Illuminate\Support\Facades\Auth::User();
                $barista = \App\Models\User::where('role', \App\Constants\Role::BARISTA)->where('is_available', 1)->first();
                $checkBaristaStatus = (bool) $barista;
            @endphp
            @if($hour >= 0 && $hour < 6 )
                Good Night
            @elseif($hour >= 6 && $hour < 12 )
                Good Morning
            @elseif($hour >= 12 && $hour < 18 )
                Good Afternoon
            @elseif($hour >= 18 && $hour <= 23 )
                Good Evening
            @endif

        </p>
        <form method="post" id="barista_available_form" action="{{route('barista.status.update', ['user' => $user->id])}}">
            @csrf
            <input type="hidden" name="is_available" id="is_available" value="">
            <label>
                <input type="checkbox" name="" id="checkbox_{{$user->id}}" onclick="submitBaristaAvailableForm({{$user->id}})" @if($checkBaristaStatus) checked @endif>
                <span>Available Now</span>
            </label>
        </form>
        <h2>{{ ucfirst($user->name) }}</h2>
    </div>
</div>

@push('barista-scripts')
    <script>
        function submitBaristaAvailableForm (id) {
            let value = $('#checkbox_'+id).is(':checked');
            $("#is_available").val(value ? 1 : 0);
            $('#barista_available_form').submit();
        }
    </script>
@endpush
@extends('barista-app.layouts.app')
@section('barista-content')
    <div class="page__header">
        <div class="left__content">
            <a href="{{route('barista.dashboard')}}" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
        </div>
        <div class="page__title">
            <h5>Pending Requests</h5>
        </div>
        <div class="right__content">

        </div>
    </div>
    <div class="order__queue">
        <div class="ordered__menu">
            @foreach($orders as $order)
            <div class="ordered__item">
                <div class="menu__details">
                    <div class="ordered__item__thumb bgi__property" style="background-image: url('{{$order->menu->thumbnail}}')">
                    </div>
                    <div class="orderer__thumb bgi__property" style="background-image: url('{{$order->user->avatar}}')">
                    </div>
                    <div class="orderer__info">
                        <h5>{{ $order->user->name }}</h5>
                        <span>{{ $order->user->designation }}</span>
                    </div>
                </div>
                <div class="order__info">
                    <div class="order__info__details">
                        <h5>{{$order->menu->name}}</h5>
                        <span class="order__id">#request-{{ $order->id }}</span>
                    </div>
                    <div class="order__info__button">
                        <a href="{{ route('barista.order.status-change', ['orderId' => $order->id,'status' => \App\Constants\OrderStatus::REJECTED]) }}" class="WpcButton mr-1">Reject</a>
                        <a href="{{ route('barista.order.status-change', ['orderId' => $order->id,'status' => \App\Constants\OrderStatus::PROCESSING]) }}" class="WpcButton WpcFilled">Mark as processing</a>

                    </div>
                </div>
                @if($order->note)
                <div class="order__notes">
                        <p>{{ $order->note }}</p>
                </div>
                @endif
            </div>
            @endforeach
        </div>
    </div>
@endsection
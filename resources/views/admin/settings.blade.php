@extends('admin.layouts.app')

@section('content')
  @push('head')
    <link rel="stylesheet" type="text/css" href="{{ asset('css/bootstrap-datetimepicker.min.css')}}">
  @endpush
          <ul class="nav mb-4">
            <li class="mr-3">
              <a class="WpcTabButton active" data-toggle="pill" href="#pills-home"><span class="Text">Manage Coupon</span></a>
            </li>
            <li class="mr-3">
              <a class="WpcTabButton" data-toggle="pill" href="#pills-profile"><span class="Text">Manage Coffee</span></a>
            </li>
            <li>
              <a class="WpcTabButton" data-toggle="pill" href="#pills-permission"><span class="Text">Permission</span></a>
            </li>
          </ul>

          <div class="tab-content">
            <div class="tab-pane fade show active" id="pills-home">
              <section class="WpcAnalyticsSection">
                <div class="WpcSectionTitleWrap">
                  <h4 class="WpcSectionTitle">Manage Coupon</h4>
                </div>
                <form id="recurringGiftForm" action="{{ route('settings.coupon.recurring-gift-store') }}" method="post">
                  @csrf
                  <div class="row">
                    <div class="col-xl-7 col-lg-8">
                      <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Date</label>
                        <input type="text" id="datepicker" name="run_at" value="{{old('run_at', \Carbon\Carbon::parse(data_get($recurringSettings, 'run_at'))->format('Y-m-d'))}}"  class="form-control" />
                      </div>
                      <div class="WpcFormGroup">
                        <div class="WpcButtonGroup">
                          <div class="WpcFilterSelectorWrapper">
                            <label>Coffee</label>
                            <select id="menuOptions" class="WpcFilterSelector">
                              @foreach($allMenu as $menu)
                                @php
                                  $data = json_encode([
                                      'id' => $menu->id,
                                      'name' => $menu->name,
                                      'thumb' => $menu->thumbnail,
                                      'req_coupon' => $menu->required_coupon
                                  ], JSON_UNESCAPED_SLASHES);
                                @endphp
                                <option value="{{ $menu->id }}" data-menu="{{ $data }}">{{$menu->name}}</option>
                              @endforeach
                            </select>
                          </div>
                          <div class="WpcIncDecButtonGroup">
                            <button class="WpcDecButton"><i class="wpc-icon wpc-minus"></i></button>
                            <input id="quantity" class="WpcIncDecInput" type="number" value="1" min="1"/>
                            <button class="WpcIncButton"><i class="wpc-icon wpc-plus"></i></button>
                          </div>
                          <button id="addCoffee" class="WpcButton ml-auto">
                            <div class="Text">Add Coffee</div>
                          </button>

                        </div>

                      </div>
                      <div class="WpcFormGroup">
                        <div id="replaceableDiv" class="WpcCoffeePreviewWrapper"></div>
                      </div>
                      @error('items')
                      <div class="text-danger validation-error">{{ $message }}</div>
                      @enderror

                    </div>
                  </div>
                  <div class="WpcFormGroup align-items-baseline">
                    <button class="WpcButton WpcFilled mt-4">
                      <span class="Text">Save Changes</span>
                    </button>
                  </div>
                </form>
              </section>
            </div>
            <div class="tab-pane fade" id="pills-profile" >
              <div class="WpcSectionTitleWrap">
                <h4 class="WpcSectionTitle mr-2">Manage Coffee</h4>
              </div>
              <div class="WpcInfoBoxWrapper">
                <div class="row WpcHasColGap">
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee1.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee2.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee3.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee4.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee1.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee2.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee3.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button>
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="tab-pane fade" id="pills-permission" >
              <div class="WpcSectionTitleWrap">
                <h4 class="WpcSectionTitle mr-2">Permission</h4>
              </div>
              <div class="WpcInfoBoxWrapper">
                <div class="row WpcHasColGap">
                  @foreach($admins as $admin)
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                              class="WpcInfoBoxImage"
                              style="background-image: url({{$admin->avatar}})"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <h3 class="WpcInfoBoxTitle">{{$admin->name}}</h3>
                        <p class="WpcPostGridContent">
                          {{$admin->designation}}
                        </p>
                      </div>
                      <button id="permission-change" data-toggle="modal"  data-target="#WpcAddPermissionModal{{ $admin->id }}" class="WpcButton ml-auto">
                        <div class="Text">Change</div>
                      </button>
                    </div>
                  </div>
                  @endforeach
                </div>
              </div>
            </div>
          </div>
  @include('admin.layouts.modals.AdminPermission')
@endsection

@push('scripts')
  <script type="text/javascript" src="{{asset('js/bootstrap-datetimepicker.min.js')}}"></script>
  <script>
    $('#datepicker').datepicker({
      format: "yyyy-mm-dd"
    });
    $(function (){

      $('.WpcIncButton').on('click',function(e){
        e.preventDefault();
        var $qty=$(this).closest('.WpcIncDecButtonGroup').find('.WpcIncDecInput');
        var currentVal = parseInt($qty.val());
        if (!isNaN(currentVal)) {
          $qty.val(currentVal + 1);
        }
      });
      $('.WpcDecButton').on('click',function(e){
        e.preventDefault();
        var $qty=$(this).closest('.WpcIncDecButtonGroup').find('.WpcIncDecInput');
        var currentVal = parseInt($qty.val());
        if (!isNaN(currentVal) && currentVal > 1) {
          $qty.val(currentVal - 1);
        }
      });

      const menuItems = [];

      let addCoffeeBtn = $('#addCoffee'),
              itemWrapper = $('#replaceableDiv'),
              menuOptions = $('#menuOptions'),
              menuQty = $('#quantity');


      $(addCoffeeBtn).click(function (e){
        e.preventDefault();
        let menu = $(menuOptions).children('option:selected').attr('data-menu') || "";
        let qty = $(menuQty).val() || 1;
        addIteams(menu, qty);
      });

      const bindDelete = () => {
        $('.deleteItem').unbind().click(function (e){
          e.preventDefault();
          let index = $(this).attr('data-index');
          deleteItem(index);
        });
      }

      const addIteams = (menu, qty) => {
        menu = JSON.parse(menu);
        menu['qty'] = parseInt(qty);
        let itemIndex = menuItems.findIndex(item => item.id == menu.id);
        if (itemIndex > -1) {
          menuItems[itemIndex]['qty'] += menu.qty
        } else {
          menuItems.push(menu);
        }
        renderItems();
      }

      const deleteItem = (index) => {
        menuItems.splice(index, 1);
        renderItems();
      }

      const renderItems = () => {
        let temps = menuItems.map((item, index) => {
          return `
                        <div class="WpcCoffeePreview">
                            <button class="deleteItem" data-index="${index}"><i class="wpc-icon wpc-delete"></i></button>
                            <div class="WpcCoffeePreviewImage" style="background-image: url(${item.thumb})"></div>
                            <p class="WpcCoffeePreviewDetails"> ${item.name} : ${item.qty}</p>
                            <input type="hidden" name="items[${item.id}]" value='${JSON.stringify(item)}'>
                        </div>
                    `;
        });

        $(itemWrapper).html(temps.join(" "));

        bindDelete();

      }
      @if($recurringSettings)
        let data = <?php echo $recurringSettings->data ?>;
        $.each(data, function(key, val) {
          let obj = JSON.parse(val);
          addIteams(val, obj.qty);
        });
      @endif
    })
  </script>
@endpush

@extends('admin.layouts.app')
@section('head')

@endsection
@section('content')
    <section class="WpcMyPostGridSection">
        <div class="WpcSectionTitleWrap">
            <h4 class="WpcSectionTitle mr-2">Employees</h4>
            <a href="{{route('employees.list')}}"><button class="WpcButton WpcFilled">
                <span class="Text">List View</span>
            </button></a>
            <div class="WpcButtonGroup ml-auto">
                <form method="get" id="employee_page_form" action="{{route('employees')}}">
                <div class="WpcSearchForm">
                  <input type="search" class="form-control" name="employee_search" value="{{$employeeSearch}}" id="employee_search" placeholder="Search Here..." />
                    @if(blank($employeeSearch))
                        <button class="search_btn"><i class="wpc-icon wpc-search"></i></button>
                    @else
                        <button><i class="wpc-icon wpc-close clear_btn"></i></button>
                    @endif
                </div>
                </form>
                <button
                    class="WpcButton WpcFilled"
                    data-toggle="modal"
                    data-target=".WpcAddEmployeeModal"
                >
                    <span class="Text">Add Employee</span>
                </button>
            </div>
        </div>
        <div class="WpcPostGridWrapper">
            <div class="row WpcHasColGap">
                @foreach($users as $user)
                <div class="col-md-3">
                    <div class="WpcPostGrid">
                        <div class="WpcPostGridThumbnail" style="background-image: url('{{ $user->avatar }}')"></div>
                        <div class="WpcPostGridDetails">
                            <div class="dropdown WpcToggleDropdown">
                                <button
                                        class=""
                                        type="button"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                >
                                    <i class="wpc-icon wpc-dots-vertical"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-middle">
                                    <ul>
                                        <li>
                                            <a href="{{ route('employees', ['edit' => $user->id]) }}">
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
                                                <p class="WpcNavigationText">Edit</p>
                                            </a>
                                        </li>
                                        <li>
                                            <button
                                                    data-delete-url="{{ route('employees.delete', $user->id) }}"
                                                    data-toggle="modal"
                                                    data-target=".WpcDeleteConfirmationModal"

                                            >
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
                                                <p class="WpcNavigationText">Delete</p>

                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <h3 class="WpcPostGridTitle">{{ $user->name }}</h3>
                            <p class="WpcPostGridContent">
                                <span>Available Coupon: </span> {{$user->available_coupon_count}}
                            </p>
                            <a href="{{route('employees.show',$user->id)}}" class="WpcButton">
                                <span class="Text">View Details</span>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        {{ $users->links('layouts.paginator') }}
    </section>
    @include('admin.layouts.modals.addEmployee')
    @if($employeeEdit)
        @include('admin.layouts.modals.editEmployee')
    @endif
    @include('admin.layouts.modals.deleteConfirmation')

@endsection

@push('scripts')
    <script>
        @if($employeeEdit)
            $('#WpcEditEmployeeModal').modal('show');
        @endif

        $('.clear_btn').on('click',function(e){
            e.preventDefault();
            $('#employee_search').val(null);
            window.location.href = '{{ route('employees') }}'
        });
    </script>
@endpush

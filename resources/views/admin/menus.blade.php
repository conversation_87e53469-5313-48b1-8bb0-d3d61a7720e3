@extends('admin.layouts.app')
@section('content')
    <section class="WpcInfoBoxSection">
        <div class="WpcSectionTitleWrap">
            <h4 class="WpcSectionTitle mr-2">Coffee Menu</h4>
            <div class="WpcButtonGroup ml-auto">
                <form method="get" id="coffee_page_form" action="{{route('coffees.index')}}">
                  <div class="WpcSearchForm">
                    <input type="search" class="form-control" name="search" id="search_coffee" value="{{$search}}" placeholder="Search Here..." />
                      @if(blank($search))
                          <button class="search_btn"><i class="wpc-icon wpc-search"></i></button>
                      @else
                          <button><i class="wpc-icon wpc-close clear_btn"></i></button>
                      @endif
                  </div>
                </form>
            </div>
        </div>
        <div class="WpcInfoBoxWrapper">
            <div class="row WpcHasColGap">
                <div class="col-md-4">
                    <button
                            class="WpcInfoBox WpcAddItem"
                            data-toggle="modal"
                            data-target=".WpcAddCoffeeModal"
                    >
                        <h3 class="WpcInfoBoxTitle">Add New Coffee</h3>
                    </button>
                </div>
                @foreach($menus as $menu)
                    <div class="col-md-4">
                        <div class="WpcInfoBox">
                            @if($menu->is_active)
                                <span class="WpcRibbonStyle"><span>Available</span></span>
                            @endif
                            <div
                                class="WpcInfoBoxImage"
                                style="background-image: url('{{$menu->thumbnail}}')"
                            ></div>
                            <div class="WpcInfoBoxDetails">
                                <div class="dropdown WpcToggleDropdown">
                                    <button
                                            class=""
                                            type="button"
                                            data-toggle="dropdown"
                                            aria-haspopup="true"
                                            aria-expanded="false"
                                    >
                                        <i class="wpc-icon wpc-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-middle">
                                        <ul>
                                            <li>
                                                <a href="{{ route('coffees.index',['edit' => $menu->id]) }}">
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
                                                    <p class="WpcNavigationText">Edit</p>
                                                </a>
                                            </li>
                                            <li>

                                                <button
                                                        data-delete-url="{{ route('coffees.delete', $menu->id) }}"
                                                        data-toggle="modal"
                                                        data-target=".WpcDeleteConfirmationModal"
                                                >
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
                                                    <p class="WpcNavigationText">Delete</p>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <h3 class="WpcInfoBoxTitle mb-2">{{$menu->name}}</h3>
                                @if($menu->IngredientsWithBar)
                                <div class="mb-2">{{ $menu->IngredientsWithBar }}</div>
                                @endif
                                <p class="WpcPostGridContent">
                                    {{ $menu->description }}
                                </p>
                            </div>
                        </div>
                    </div>
                @endforeach

            </div>
        </div>
    </section>
    @include('admin.layouts.modals.addMenu')
    @if(!blank($coffee))
        @include('admin.layouts.modals.editMenu')
    @endif
    @include('admin.layouts.modals.deleteConfirmation')
@endsection

@push('scripts')
    <script>
        $('.clear_btn').on('click',function(e){
            e.preventDefault();
            $('#search_coffee').val(null);
            window.location.href = '{{ route('coffees.index') }}';
        });
    </script>
@endpush


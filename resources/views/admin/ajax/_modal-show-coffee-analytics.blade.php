<table class="WpcDataTable">
    <thead>
    <tr>
        <th><span>Coffee</span></th>
        <th style="text-align: center"><span>Requested</span></th>
        <th style="text-align: center"><span>Served</span></th>
        <th style="text-align: center"><span>Gift Distributed</span></th>
    </tr>
    </thead>
    <tbody>
    @foreach($coffeeStat as $coffee)
        @php
            $requiredCoupon = $coffee->required_coupon > 0 ? $coffee->required_coupon : 1;
        @endphp
        <tr>
            <td>
                <div class="WpcTableInfoBox">
                    <div
                            class="WpcTableInfoBoxImage"
                            style="background-image: url('{{$coffee->thumbnail}}')"
                    ></div>
                    <div class="WpcTableInfoBoxDetails">
                        <h3 class="WpcTableInfoBoxTitle">{{$coffee->name}}</h3>
                    </div>
                </div>
            </td>
            <td>
                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                    <span class="WpcCoffeeInfoText">{{ $coffee->total_requested }}</span>
                </div>
            </td>
            <td style="text-align: center">
                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                    <span class="WpcCoffeeInfoText">{{$coffee->request_completed}}</span>
                </div>
            </td>
            <td style="text-align: center">
                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                    <span class="WpcCoffeeInfoText">{{ round($coffee->coupon_distributed / $requiredCoupon, 1) }}</span>
                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
</table>


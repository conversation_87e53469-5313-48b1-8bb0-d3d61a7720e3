@extends('admin.layouts.app')
@section('content')
    <section class="WpcInfoBoxSection">
        <div class="WpcSectionTitleWrap">
            <h4 class="WpcSectionTitle mr-2">Teams</h4>
            <div class="WpcButtonGroup ml-auto">
                <form method="get" id="employee_page_form" action="{{route('teams.index')}}">
                  <div class="WpcSearchForm">
                    <input type="search" class="form-control" name="search" id="teams_search" value="{{$search}}" placeholder="Search Here..." />
                      @if(blank($search))
                          <button class="search_btn"><i class="wpc-icon wpc-search"></i></button>
                      @else
                          <button><i class="wpc-icon wpc-close clear_btn"></i></button>
                      @endif
                  </div>
                </form>
            </div>
        </div>
        <div class="WpcInfoBoxWrapper">
            <div class="row WpcHasColGap">
                <div class="col-md-4">
                    <button
                            class="WpcInfoBox WpcAddItem"
                            data-toggle="modal"
                            data-target=".WpcAddTeamModal"
                    >
                        <h3 class="WpcInfoBoxTitle">Add New Team</h3>
                    </button>
                </div>
                @foreach($teams as $team)
                    <div class="col-md-4">
                    <div class="WpcInfoBox">
                        <div
                                class="WpcInfoBoxImage"
                                style="background-image: url('{{$team->thumbnail}}')"
                        ></div>
                        <div class="WpcInfoBoxDetails">
                            <div class="dropdown WpcToggleDropdown">
                                <button
                                        class=""
                                        type="button"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                >
                                    <i class="wpc-icon wpc-dots-vertical"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-middle">
                                    <ul>
                                        <li>
                                            <a href="{{ route('teams.index', ['edit' => $team->id]) }}">
                                                <span class="WpcNavigationIcon"><i class="wpc-icon wpc-edit"></i></span>
                                                <p class="WpcNavigationText">Edit</p>
                                            </a>
                                        </li>
                                        <li>
                                            <button
                                                    data-delete-url="{{ route('team.delete', $team->id) }}"
                                                    data-toggle="modal"
                                                    data-target=".WpcDeleteConfirmationModal"
                                            >
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
                                                <p class="WpcNavigationText">Delete</p>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <h3 class="WpcInfoBoxTitle">{{$team->name}}</h3>
                            <p class="WpcPostGridContent">
                                {{$team->description}}
                            </p>
                        </div>
                    </div>
                </div>
                @endforeach

            </div>
        </div>
    </section>

    @include('admin.layouts.modals.addTeam')
    @if(!blank($editTeam))
        @include('admin.layouts.modals.editTeam')
    @endif
    @include('admin.layouts.modals.deleteConfirmation')
@endsection

@push('scripts')
    <script>
        @if($editTeam)
        $('#WpcEditTeamModal').modal('show');
        @endif

        $('.clear_btn').on('click',function(e){
            e.preventDefault();
            $('#teams_search').val(null);
            window.location.href = '{{ route('teams.index') }}';
        });
    </script>
@endpush
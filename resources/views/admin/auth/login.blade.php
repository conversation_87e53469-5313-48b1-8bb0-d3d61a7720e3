@extends('admin.layouts.auth')

@section('content')
    <div class="WpcLoginForm">
        <div class="WpcLoginTabWrapper">
{{--            <button class="WpcLoginTab"  onclick="window.location='{{ route('register') }}'">Register</button>--}}
            <button class="WpcLoginTab WpcActive">Login</button>
        </div>
        <form method="POST" action="{{ route('login') }}">
            @csrf
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Email Address</label>
                <input
                        id="email"
                        type="email"
                        class="form-control @error('email') is-invalid @enderror"
                        placeholder="<EMAIL>"
                        name="email"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                        autofocus

                />
                @error('email')
                <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                @enderror
            </div>
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Password</label>
                <input
                        id="password"
                        type="password"
                        class="form-control @error('password') is-invalid @enderror"
                        placeholder="********"
                        name="password"
                        required
                        autocomplete="current-password"
                />
                @error('password')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <label class="WpcCheckbox mb-4">
                <input type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}/>
                <span class="Text WpcFont500">Remember Me</span>
            </label>
            <button class="WpcButton WpcFilled w-100">
                <span class="Text">Login</span>
            </button>
{{--            @if (Route::has('password.request'))--}}
{{--                <div class="d-flex mt-4">--}}
{{--                    <a href="{{ route('password.request') }}" class="WpcFont500">Forgot Password?</a>--}}
{{--                    <a href="{{ route('password.request') }}" class="ml-auto WpcFont500"--}}
{{--                    >Resent Verification Link</a--}}
{{--                    >--}}
{{--                </div>--}}
{{--            @endif--}}
        </form>
    </div>
@endsection

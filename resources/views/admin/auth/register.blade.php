@extends('admin.layouts.auth')

@section('content')
    <div class="WpcLoginForm">
        <div class="WpcLoginTabWrapper">
            <button class="WpcLoginTab WpcActive">Register</button>
            <button class="WpcLoginTab" onclick="window.location='{{ route('login') }}'">Login</button>
        </div>
        <form method="POST" action="{{ route('register') }}">
            @csrf
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Add Image</label>
                <div class="d-flex align-item-center">
                    <div class="WpcImageSelector">
                        <label class="WpcImgBox">
                            <input type="file" name="avatar" id="avatar"/>
                            <span class="WpcDummyImage">
												<i class="wpc-icon wpc-camera"></i>
											</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Name</label>
                <input
                        type="text"
                        class="form-control @error('name') is-invalid @enderror" name="name"
                        value="{{ old('name') }}"
                        required
                        autocomplete="name"
                        autofocus
                        placeholder="Enter your name..."
                />
            </div>
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Designation</label>
                <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Designation..."
                />
            </div>
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Email Address</label>
                <input
                        type="email"
                        class="form-control @error('email') is-invalid @enderror"
                        placeholder="<EMAIL>"
                        name="email"
                        value="{{ old('email') }}"
                        required autocomplete="email"
                />
            </div>
            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Password</label>
                <input
                        type="password"
                        class="form-control @error('password') is-invalid @enderror"
                        name="password"
                        required
                        autocomplete="new-password"
                        placeholder="********"
                />
            </div>

            <div class="WpcFormGroup">
                <label class="WpcFormLabel">Confirm Password</label>
                <input
                        type="password"
                        class="form-control"
                        placeholder="********"
                        name="password_confirmation"
                        required
                        autocomplete="new-password"
                />
            </div>
            <button class="WpcButton WpcFilled w-100">
                <span class="Text">Sign Up</span>
            </button>
        </form>
    </div>
@endsection

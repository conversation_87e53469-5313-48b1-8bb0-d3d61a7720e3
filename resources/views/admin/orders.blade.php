@extends('admin.layouts.app')

@section('content')
    <section class="WpcAnalyticsSection">
        <div class="WpcSectionTitleWrap">
            <h4 class="WpcSectionTitle mr-2">Gift History</h4>
            <div class="WpcButtonGroup ml-auto">
                <form method="get" id="employee_page_form" action="{{route('orders')}}">
                    <div class="WpcSearchForm">
                        <input type="search" class="form-control" name="search" id="search_order" value="{{$search}}" placeholder="Search Here..."/>
                        @if(blank($search))
                            <button class="search_btn"><i class="wpc-icon wpc-search"></i></button>
                        @else
                            <button><i class="wpc-icon wpc-close clear_btn"></i></button>
                        @endif
                    </div>
                </form>
                <button
                        class="WpcButton WpcFilled"
                        data-toggle="modal"
                        data-target=".WpcGiftCouponModal"
                >
                    <span class="Text">Gift Coupon</span>
                </button>
            </div>
        </div>
        <div class="WpcDataTableWrapper">
            <table class="WpcDataTable">
                <thead>
                <tr>
                    <th><span>Sender</span></th>
                    <th><span>Receivers</span></th>
                    <th class="text-right"><span>Menus</span></th>
                </tr>
                </thead>
                <tbody>
                @foreach($data as $data_key => $sender)
                    <tr>
                        <td>
                            <div class="WpcTableInfoBox">
                                <div class="WpcTableInfoBoxImage"
                                     style="background-image: url('{{data_get($sender, "sender.avatar", '') }}')">
                                </div>
                                <div class="WpcTableInfoBoxDetails">
                                    <h3 class="WpcTableInfoBoxTitle">{{$sender->sender->name}}</h3>
                                    <h6 class="WpcTableInfoBoxTitle" style="font-size: 18px;">{{$sender->sender->designation}}</h6>
                                    <p class="WpcTableInfoBoxInfo" style="font-size: 14px;">{{ \Carbon\Carbon::parse($sender->created_at)->format('d F , Y')}}</p>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="WpcTableInfoBox">
                                <div class="WpcTableInfoBoxDetails WpcTableImageCollection">
                                @if(isset($sender->items) && count($sender->receivers) == 0)
                                                <div class="WpcTableInfoBoxImage"
                                                     style="
                                                     background-image: url('{{data_get($sender, "onereceiver.avatar", asset('assets/img/user4.png'))}}');
                                                     border-radius:66px"
                                                >
                                                </div>
                                    @else
                                    @foreach($sender->receivers as $key => $receiver)
                                    @if($key<5)
                                        <div class="WpcTableInfoBoxImage"
                                             style="background-image: url('{{data_get($receiver, "receiverDetails.avatar", "")}}');border-radius:66px">
                                        </div>
                                        @elseif($key ==5)
                                            <button class="WpdTableAddImageButton" data-toggle="modal"  data-target="#WpcGiftCouponDetailsModal{{$sender->id}}"><i class="wpc-icon wpc-plus"></i></button>
                                        @endif
                                    @endforeach
                                        @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="WpcTableInfoBox">
                                <button id="permission-change" data-toggle="modal"  data-target="#WpcGiftCouponDetailsModal{{$sender->id}}" class="WpcButton ml-auto">
                                    <div class="Text">Details</div>
                                </button>
                            </div>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        {{ $data->links('layouts.paginator') }}

       @include('admin.layouts.modals.couponDetails')
       @include('admin.layouts.modals.giftCoupon')
    </section>
@endsection

@push('scripts')
    <script>
        $('.clear_btn').on('click',function(e){
            e.preventDefault();
            $('#search_order').val(null);
            window.location.href = '{{ route('orders') }}';
        });
    </script>
@endpush

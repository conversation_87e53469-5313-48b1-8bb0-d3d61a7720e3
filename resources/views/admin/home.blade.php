@extends('admin.layouts.app')
@section('content')
    <div class="WpcDashboardContentWrapper">
        <section class="WpcAnalyticsSection">
            <form id="filter" action="{{route('home')}}" method="get">
                <div class="WpcSectionTitleWrap WpcSectionTitleWrap--flex">
                    <h4 class="WpcSectionTitle mr-2">Coffee Order Stats</h4>
                    <div class="timeline__filter">
                        <div class="form-control dateRangePicker" id="reportrange">
                            <span class="text" id="wired-span"></span>
                        </div>
                    </div>
                    <div>
                        <input type="hidden" id="start-date" name="start">
                        <input type="hidden" id="end-date" name="end">
                        <select class="form-control" name="coffeeFilter" id="coffee-picker">
                            <option value="all">All Coffees</option>
                            @foreach($coffeeStat as $coffee)
                                <option value="{{$coffee->id}}" {{isset($_GET['coffeeFilter']) && $_GET['coffeeFilter'] == $coffee->id ? 'selected': ''}}>{{$coffee->name}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="ml-4">
                        <div class="WpcFormGroup">
                            <button class="WpcButton WpcFilled d-none" id="submit-button">
                                <span class="Text"></span>
                            </button>
                        </div>
                    </div>
                </div>

            </form>
            <div class="card">
                <div class="card-body">
                    <div id="bar">
                    </div>
                </div>
            </div>

            <div class="WpcSectionTitleWrap mt-5">
                <h4 class="WpcSectionTitle mr-2">Coffee Usage Stats</h4>
            </div>
            <div class="card">
                <div class="card-body">
                    <div id="lineChart">
                    </div>
                </div>
            </div>
            <div class="WpcSectionTitleWrap mt-5">
                <h4 class="WpcSectionTitle mr-2">Coffee Analytics</h4>
            </div>
            <div class="WpcDataTableWrapper mb-5">
                <table class="WpcDataTable">
                    <thead>
                    <tr>
                        <th><span>Coffee</span></th>
                        <th style="text-align: center"><span>Requested</span></th>
                        <th style="text-align: center"><span>Served</span></th>
                        <th style="text-align: center"><span>Gift Distributed</span></th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($coffeeAnalyticsStat as $coffee)
                        @php
                            $requiredCoupon = $coffee->required_coupon > 0 ? $coffee->required_coupon : 1;
                        @endphp
                        <tr>

                            <td>
                                <div class="WpcTableInfoBox">
                                    <div
                                            class="WpcTableInfoBoxImage"
                                            style="background-image: url('{{$coffee->thumbnail}}')"
                                    ></div>
                                    <div class="WpcTableInfoBoxDetails">
                                        <h3 class="WpcTableInfoBoxTitle">{{$coffee->name}}</h3>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                                    <span class="WpcCoffeeInfoText">{{ $coffee->total_requested}}</span>
                                </div>
                            </td>
                            <td style="text-align: center">
                                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                                    <span class="WpcCoffeeInfoText">{{$coffee->request_completed}}</span>
                                </div>
                            </td>
                            <td style="text-align: center">
                                <div class="WpcCoffeeInfoCard" style="justify-content: center">
                                    <span class="WpcCoffeeInfoText">{{ round($coffee->coupon_distributed / $requiredCoupon, 1) }}</span>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </section>
        <section class="WpcCounterCardWrapper">
            <div class="WpcCounterCard Active">
                <div class="WpcCoffeePreview">
                    <div class="WpcCoffeePreviewImage"
                         style="background-image: url('{{$coffeeStat->first()->thumbnail}}')"></div>
                    <p class="WpcCoffeePreviewDetails">{{$coffeeStat->sortByDesc('orders_count')->first()->name}}</p>
                </div>
                <div class="WpcCounterContent">
                    <h4 class="WpcCounterCount">{{$coffeeStat->sortByDesc('orders_count')->first()->orders_count}}
                        <span>Times</span></h4>
                    <p class="WpcCounterTitle">Most Ordered Coffee</p>
                </div>
            </div>
            <div class="WpcCounterCard">
                <div class="WpcCounterContent" style="flex: 1">
                    <div style="align-self: flex-end; width: 180px; text-align: right">
                    </div>
                    <h4 class="WpcCounterCount">{{round($totalAvailableCoffee)}}</h4>
                    <p class="WpcCounterTitle">Total Available Coffee</p>
                </div>
            </div>
            <div class="WpcCounterCard">
                <div class="WpcCounterContent">
                    <h4 class="WpcCounterCount">{{$coffeeAnalytics['totalDistributed']}}</h4>
                    <p class="WpcCounterTitle">Total Distributed Coffee</p>
                </div>
            </div>
            <div class="WpcCounterCard">
                <div class="WpcCounterContent">
                    <h4 class="WpcCounterCount">{{$coffeeOrderTotal}}</h4>
                    <p class="WpcCounterTitle">Served Coffee</p>
                </div>
            </div>
            <div class="WpcCounterCard">
                <div class="WpcCounterContent">
                    <h4 class="WpcCounterCount">{{$coffeeOrderTotalForGuest}}</h4>
                    <p class="WpcCounterTitle">Served Coffee to Guests</p>
                </div>
            </div>
        </section>
    </div>
@endsection
@push('scripts')
    <script>
        let coffee = {!! json_encode($coffeeAnalyticsStat) !!};
        const series = {!! json_encode($series) !!};
        const categories = {!! json_encode($categories) !!};

        $(document).ready(function () {
            barChart();
            lineChart(coffee);
        });

        function barChart() {
            let series = {!! json_encode($series) !!};
            let categories = {!! json_encode($categories) !!};
            const chartColors = ['#597dfc', '#60ce83', '#ff9635', '#ff5f74', '#2fc1e1', '#ff61d3'];
            const chartLabelStyles = {
                colors: '#999',
                fontSize: '14px',
                fontWeight: 600,
            };

            var options = {
                series,
                chart: {
                    type: 'bar',
                    height: 650,
                    stacked: true,
                    toolbar: {
                        show: true,
                        tools:{
                            download:false
                        }
                    }
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        legend: {
                            position: 'bottom',
                            offsetX: -5,
                            offsetY: 0
                        }
                    }
                }],
                xaxis: {
                    labels: {
                        show: true,
                        rotate: -45,
                        rotateAlways: true,
                        style: {
                            ...chartLabelStyles,
                        },
                    },
                    categories
                },
                markers: {
                    size: 5,
                    colors: ['#ffffff'],
                    strokeWidth: 2,
                    strokeColors: chartColors,
                    hover: {
                        sizeOffset: 2,
                    }
                },
                legend: {
                    position: 'bottom'
                },
            };
            var barChart = new ApexCharts(document.querySelector("#bar"), options);
            barChart.render();
        }

        function lineChart(coffee) {
            let requiredCoupon;
            let requested = [];
            let served = [];
            let coupon_distributed = [];
            let categories = [];

            $.each(coffee, (index, val) => {
                requiredCoupon = val.required_coupon > 0 ? val.required_coupon : 1;
                categories.push(val.name);
                requested.push(val.total_requested);
                served.push(val.request_completed);
                coupon_distributed.push(Math.round(val.coupon_distributed / requiredCoupon));
            })
            const chartColors = ['#597dfc', '#60ce83', '#ff9635', '#ff5f74', '#2fc1e1', '#ff61d3'];
            const chartLabelStyles = {
                colors: '#999',
                fontSize: '14px',
                fontWeight: 600,
            };

            let series = [
                {
                    name: 'Requested',
                    data: requested
                },
                {
                    name: 'Served',
                    data: served
                },
                {
                    name: 'Gift Distributed',
                    data: coupon_distributed
                },
            ];

            let options = {
                chart: {
                    type: 'line',
                    zoom: {
                        enabled: false
                    },
                    toolbar: {
                        show: false
                    },
                    dropShadow: {
                        enabled: true,
                        top: 5,
                        left: 0,
                        blur: 5,
                        opacity: 0.2
                    }
                },
                legend: {
                    show: true,
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2,
                },
                series,
                xaxis: {
                    type: 'category',
                    categories,
                    labels: {
                        style: {
                            ...chartLabelStyles,
                        },
                    },
                },
                markers: {
                    size: 5,
                    colors: ['#ffffff'],
                    strokeWidth: 2,
                    strokeColors: chartColors,
                    hover: {
                        sizeOffset: 2,
                    }
                },
                tooltip: {
                    shared: true,
                    onDatasetHover: {
                        highlightDataSeries: true,
                    },
                    x: {
                        show: false,
                    },
                }
            }

            let lineChart = new ApexCharts(document.querySelector("#lineChart"), options);
            lineChart.render();
        }
        $('#coffeeFilter').on('change', function () {
            let drp = $('#reportrange').data('daterangepicker');
            findCoffeeAnalytics(drp.startDate.format('YYYY-MM-DD'), drp.endDate.format('YYYY-MM-DD'), false)
        });

        function findCoffeeAnalytics(start, end, isFirst = false) {
            $("#start-date").val(start)
            $("#end-date").val(end)
        }

        function orderServedCount(sart, end) {
            $.ajax({
                url: '{{route('orders.analytics')}}',
                type: 'POST',
                data: {
                    _token: '{{csrf_token()}}',
                    start: sart, end: end,
                },
                success: function (data) {
                    $("#completedOrderCount").html(data);
                }
            });
        }

        $(function () {

            let datOptions = ['Today', 'Yesterday', 'Last 7 Days', 'Last 30 Days', 'Last Month','custom range'];
            var start = moment("{{request()->get('start', \Carbon\Carbon::today()->subDays(6))}}");
            var end = moment("{{request()->get('end', \Carbon\Carbon::now())}}");
            findCoffeeAnalytics(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'), true);
            function cb(start, end) {

                let label = datOptions[0];
                let startDate = start.format('YYYY-MM-DD');
                let endDate = end.format('YYYY-MM-DD');

                if(startDate === endDate && startDate === moment().format('YYYY-MM-DD')) {
                    label = datOptions[0];
                } else if(startDate === endDate && startDate === moment().subtract(1, 'days').format('YYYY-MM-DD')) {
                    label = datOptions[1];
                } else if(startDate === moment().subtract(6, 'days').format('YYYY-MM-DD')) {
                    label = datOptions[2];
                } else if(startDate === moment().subtract(29, 'days').format('YYYY-MM-DD')) {
                    label = datOptions[3];
                } else if(startDate === moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD') &&
                    endDate === moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD')) {
                    label = datOptions[4];
                }
                else{
                    label = datOptions[5];
                }

                if (datOptions.includes(label)) {
                    $('#reportrange span').html(label);
                } else {
                    $('#reportrange span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                }
            }

            function countRange(start, end, label) {
                if (datOptions.includes(label)) {
                    $('#completeCountRange span').html(label);
                } else {
                    $('#completeCountRange span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                }
            }

            $('#reportrange').daterangepicker({
                startDate: start,
                endDate: end,
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            }, cb);
            cb(start, end);
            $("#coffee-picker").on('change',function(){
                $("#submit-button").click();
            })

            $('#reportrange').on('apply.daterangepicker', function (ev, picker) {
                findCoffeeAnalytics(picker.startDate.format('YYYY-MM-DD'), picker.endDate.format('YYYY-MM-DD'));
                $("#submit-button").click();
            });

        });
    </script>
@endpush

@extends('admin.layouts.app')
@section('head')

@endsection
@section('content')
        <section class="WpcAnalyticsSection">
            <div class="WpcSectionTitleWrap">
                <h4 class="WpcSectionTitle mr-2">Analytics</h4>
                <a href="{{route('employees')}}"><button class="WpcButton WpcFilled">
                        <span class="Text">Grid View</span>
                    </button></a>
                <a href="{{route('employees.list',['sort'=>'name'])}}"><button class="WpcButton WpcFilled ml-2">
                        <span class="Text">Sort By Name</span>
                    </button></a>
                <div class="WpcButtonGroup ml-auto">
                    <form method="get" id="employee_page_form" action="{{route('employees.list')}}">
                        <div class="WpcSearchForm">
                            <input type="search" class="form-control" name="search" id="search_emp" value="{{$search}}" placeholder="Search Here..."/>
                            @if(blank($search))
                                <button class="search_btn"><i class="wpc-icon wpc-search"></i></button>
                            @else
                                <button><i class="wpc-icon wpc-close clear_btn"></i></button>
                            @endif
                        </div>
                    </form>
                    <button
                            class="WpcButton WpcFilled"
                            data-toggle="modal"
                            data-target=".WpcGiftCouponModal"
                    >
                        <span class="Text">Gift Coupon</span>
                    </button>
                </div>
            </div>
            <div class="WpcDataTableWrapper">
                <table class="WpcDataTable">
                    <thead>
                    <tr>
                        <th><span>Employee</span></th>
                        <th><span>Available</span></th>
                        <th><span>Consumed</span></th>
                        <th><span>Most Ordered</span></th>
                        <th class="WpcTableActionHeader"></th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($employees as $user)
                        <tr>
                            <td>
                                <div class="WpcTableInfoBox">
                                    <div
                                            class="WpcTableInfoBoxImage"
                                            style="background-image: url('{{$user->avatar}}')"
                                    ></div>
                                    <div class="WpcTableInfoBoxDetails">
                                        <h3 class="WpcTableInfoBoxTitle">{{$user->name}}</h3>
                                        <p class="WpcTableInfoBoxInfo">{{$user->designation}}</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="WpcCoffeeInfoCard">
                          <span class="WpcCoffeeInfoIcon">
                            <i class="wpc-icon wpc-coffee-seeds"></i>
                          </span>
                                    <span class="WpcCoffeeInfoText">{{ $user->available_coupon_count }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="WpcCoffeeInfoCard">
                          <span class="WpcCoffeeInfoIcon">
                            <i class="wpc-icon wpc-coffee-cup"></i>
                          </span>
                                    <span class="WpcCoffeeInfoText">{{ $user->consumed_coupon_count }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="WpcCoffeePreview">
                                    @if(!blank($user->most_ordered))
                                        <div class="WpcCoffeePreviewImage"
                                             style="background-image: url('{{$user->most_ordered->thumbnail}}')">

                                        </div>
                                        <p class="WpcCoffeePreviewDetails">{{ $user->most_ordered->name }}</p>
                                    @else
                                        <div class="WpcCoffeePreviewImage"
                                             style="background-image: url('{{asset('assets/img/coffee1.png')}}')">

                                        </div>
                                        <p class="WpcCoffeePreviewDetails"></p>
                                    @endif

                                </div>
                            </td>
                            <td>
                        <span class="WpcTableActionWrapper">
                          <div class="dropdown WpcToggleDropdown">
                            <button
                                    class=""
                                    type="button"
                                    data-toggle="dropdown"
                                    aria-haspopup="true"
                                    aria-expanded="false"
                            >
                              <i class="wpc-icon wpc-dots-vertical"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-middle">
                              <ul>
																<li>
																	<button>
																		<span class="WpcNavigationIcon">
																			<i class="wpc-icon wpc-eye"></i>
																		</span>
																		<a href="{{route('employees.show',$user->id)}}">
                                                                            <span class="Text">Show</span>
                                                                        </a>
																	</button>
																</li>
{{--                                <li>--}}
                                  {{--                                  <button--}}
                                  {{--                                          data-toggle="modal"--}}
                                  {{--                                          data-target=".WpcEditEmployeeModal"--}}
                                  {{--                                  >--}}
                                  {{--                                    <span class="WpcNavigationIcon">--}}
                                  {{--                                      <i class="wpc-icon wpc-edit"></i>--}}
                                  {{--                                    </span>--}}
                                  {{--                                    <p class="WpcNavigationText">Edit</p>--}}
                                  {{--                                  </button>--}}
                                  {{--                                </li>--}}
                                  {{--                                <li>--}}
                                  {{--                                  <button--}}
                                  {{--                                          data-toggle="modal"--}}
                                  {{--                                          data-target=".WpcDeleteConfirmationModal"--}}
                                  {{--                                  >--}}
                                  {{--                                    <span class="WpcNavigationIcon">--}}
                                  {{--                                      <i class="wpc-icon wpc-delete"></i>--}}
                                  {{--                                    </span>--}}
                                  {{--                                    <p class="WpcNavigationText">Delete</p>--}}
                                  {{--                                  </button>--}}
                                  {{--                                </li>--}}
                              </ul>
                            </div>
                          </div>
                        </span>
                            </td>
                        </tr>
                    @endforeach

                    </tbody>
                </table>
            </div>
            {{ $employees->withQueryString()->links('layouts.paginator') }}
        </section>
    @include('admin.layouts.modals.giftCoupon')
    @isset($employeeEdit)
        @include('admin.layouts.modals.editEmployee')
    @endisset
    @include('admin.layouts.modals.deleteConfirmation')

@endsection
@push('scripts')
    <script>
        $('.clear_btn').on('click',function(e){
            e.preventDefault();
            $('#search_emp').val(null);
            window.location.href = '{{ route('employees.list') }}'
        });
    </script>
@endpush

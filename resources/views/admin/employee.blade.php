@extends('admin.layouts.app')
@section('content')
    <section class="WpcMyPostGridSection">
        <div class="WpcSectionTitleWrap">
            <h4 class="WpcSectionTitle mr-2">Employee Details</h4>
            <div class="WpcButtonGroup ml-auto">
                <button
                        class="WpcButton WpcFilled"
                        data-toggle="modal"
                        data-target=".WpcGiftCouponModal"
                >
                    <span class="Text">Gift Coupon</span>
                </button>
            </div>
        </div>
        <div class="WpcPostGridWrapper">
            <div class="row WpcHasColGap">
                <div class="col-md-3">
                    <div class="WpcPostGrid">
                        <div
                                class="WpcPostGridThumbnail"
                                style="background-image: url('{{asset($user->avatar)}}')"
                        ></div>
                        <div class="WpcPostGridDetails">
                            <h3 class="WpcPostGridTitle">{{$user->name}}</h3>
                            <p class="WpcPostGridContent">
                                <span>{{$user->designation}}</span>
                            </p>
                            <p class="WpcPostGridContent">
                                <span>Team: </span>{{$team->name ?? 'No Team Available'}}
                            </p>
                            <p class="WpcPostGridContent">
                                <span>Available Coupon: </span> {{$user->available_coupon_count}}
                            </p>
                            <button
                                    onclick="showEditEmployeeModal()"
                                    class="WpcButton"
{{--                                    data-toggle="modal"--}}
{{--                                    data-target=".WpcEditEmployeeModal"--}}
                            >
                                <span class="Text">Edit Profile</span>

                            </button>

{{--                            <a class="WpcButton" href="{{ route('employees',['edit' => $user->id])}}">--}}
{{--                                <span class="Text">Edit Profile</span>--}}
{{--                            </a>--}}
                        </div>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="row WpcHasColGap">
                        @foreach($userMenu as $menu)
                        <div class="col-md-4">
                            <div class="WpcPostGrid">
                                <div
                                        class="WpcPostGridThumbnail"
                                        style="
														background-image: url('{{$menu->thumbnail}}');
													"
                                ></div>
                                <div class="WpcPostGridDetails">
                                    <div class="dropdown WpcToggleDropdown">
                                        <button
                                                class=""
                                                type="button"
                                                data-toggle="dropdown"
                                                aria-haspopup="true"
                                                aria-expanded="false"
                                        >
                                            <i class="wpc-icon wpc-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-middle">
                                            <ul>
                                                <li>
                                                    <button
                                                            onclick="editCoupon({{$menu}}, {{$menu->availableCoupons($user)}})"
                                                    >
																		<span class="WpcNavigationIcon">
																			<i class="wpc-icon wpc-edit"></i>
																		</span>
                                                        <p class="WpcNavigationText">Edit</p>
                                                    </button>
                                                </li>
                                                <li>
                                                    <button
                                                            data-delete-url="{{ route('employees.coupon.delete', ['user' => $user->id, 'coffee' => $menu->id]) }}"
                                                            data-toggle="modal"
                                                            data-target=".WpcDeleteConfirmationModal"

                                                    >
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
                                                        <p class="WpcNavigationText">Delete</p>

                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <h3 class="WpcPostGridTitle">{{ $menu->name }}</h3>
                                    <p class="WpcPostGridContent">
                                        <span>Available : </span>{{ $menu->availableCoupons($user) }}
                                    </p>
                                </div>

                            </div>
                        </div>
                        @endforeach

                    </div>
                </div>
            </div>
        </div>
    </section>
{{--    @isset($employee)--}}
{{--        @include('admin.layouts.modals.editEmployee')--}}
{{--    @endif--}}

    @include('admin.layouts.modals.editEmployee', ['employeeEdit' => $user, 'teams' => $teams, 'errors' => $errors ?? null ])

    @include('admin.layouts.modals.giftCoupon', ['employee' => $user])
    @include('admin.layouts.modals.editCoupon')
    @include('admin.layouts.modals.deleteConfirmation')
@endsection

@push('scripts')
    <script>
        function showEditEmployeeModal() {
            let editModal = $('#WpcEditEmployeeModal');
            $(editModal).modal('show');
        }
    </script>
@endpush
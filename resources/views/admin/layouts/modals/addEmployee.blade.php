<div class="modal fade WpcAddEmployeeModal" id="WpcAddEmployeeModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">
                    Add New Employee
                </h5>
            </div>
            <div class="WpcModalBody">
                <form action="{{ route('employees.store') }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Add Image</label>
                        <div class="WpcImageSelector">
                            <label class="WpcImgBox" id="WpcImgBox">
                                <input type="file" name="avatar" id="avatar" accept="image/*"/>
                                <span class="WpcDummyImage"><i class="wpc-icon wpc-camera"></i></span>
                            </label>
                        </div>
                        <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                        @error('avatar')
                        <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    @include('admin.components.inputs.text', ["name" => "name", "value" =>  old('name'), 'label' => "Name"])

                    <div class="WpcFormGroup">
                        <div class="d-flex align-items-center">
                            <label class="WpcFormLabel">Team</label>
                            <a href="{{ route('teams.index') }}" class="WpcAddButton ml-auto">
                                <span class="Icon"><i class="wpc-icon wpc-plus"></i></span>
                                <span class="Text">Add New Team</span>
                            </a>
                        </div>
                        <select id="teams" name="teams[]" multiple="multiple" data-placeholder="Select teams">
                            @foreach($teams as $id => $title)
                            <option value="{{$id}}">{{ $title }}</option>
                            @endforeach
                        </select>
                        @error('teams')
                        <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    @include('admin.components.inputs.text', ["name" => "designation", "value" =>  old('designation'), 'label' => "Designation"])

                    @include('admin.components.inputs.text', ["name" => "email", "value" =>  old('email'), 'label' => "Email"])

                    @include('admin.components.inputs.password', ["name" => "password", 'label' => "Password"])

                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Role</label>
                        <select class="form-control" name="role">
                            @foreach(trans('constants.user.roles') as $role => $label)
                                <option value="{{ $role }}"> {{ $label }} </option>
                            @endforeach
                        </select>
                        @error('role')
                        <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="WpcFormGroup">
                        <button class="WpcButton WpcFilled" type="submit">
                            <span class="Text">Add Employee</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        $(function (){
            $('#teams').select2();
            @if(blank($employeeEdit) && $errors->any())
                $('#WpcAddEmployeeModal').modal('show');
            @endif
            let avatarWrapper = $('#WpcImgBox');
            let avatarSelector = $('#avatar');
            $(avatarSelector).change(function (){
                let file = ($(this).prop('files') &&  $(this).prop('files')[0]) || null ;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $(avatarSelector).val(null);
                    avatarWrapper.css({
                        'background-image' : `url(${null})`
                    });
                    return;
                }

                let url = URL.createObjectURL(file);
                avatarWrapper.css({
                    'background-image' : `url(${url})`
                })
            });
        });
    </script>
@endpush
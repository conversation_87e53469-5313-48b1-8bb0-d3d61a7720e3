<form enctype="multipart/form-data" action="{{ route('coffees.update', $coffee->id) }}" method="post">
<div
        class="modal fade WpcEditCoffeeModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
        id="WpcEditCoffeeModal"
>
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">Edit Coffee</h5>
                <label class="WpcCheckbox ml-auto">
                    <input type="checkbox" name="is_active" {{ $coffee->is_active ? "checked" : "" }} value="1"/>
                    <span class="Text">Available Now</span>
                </label>
            </div>
                @csrf
                <div class="WpcModalBody">
                    @include('admin.components.inputs.text', [ 'name' => 'name', 'value' => old('name', $coffee->name)])
                    @include('admin.components.inputs.number', [ 'min' => 1, 'name' => 'required_coupon', 'label' => 'Required Coupon', 'value' => old('required_coupon', $coffee->required_coupon)])
                    <div class="WpcFormGroup d-flex justify-content-between flex-row flex-wrap">
                        @foreach(trans('constants.ingredients') as $key => $val)
                            <label class="WpcCheckbox mb-2">
                                <input type="checkbox" name="ingredients[]" value="{{ $key }}" @if(in_array($key, old('ingredients', data_get($coffee,'ingredients',[])))) checked @endif/>
                                <span class="Text">{{ $val }}</span>
                            </label>
                        @endforeach
                    </div>
                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Add Image</label>
                        <div class="d-flex">
                            <div class="WpcImageSelector">
                                <label
                                        class="WpcImgBox"
                                        id="WpcImgBoxThumb"
                                        style="background-image: url('{{ $coffee->thumbnail }}')"
                                >
                                </label>
                            </div>
                            <textarea
                                    rows="2"
                                    placeholder="Ingredients"
                                    class="form-control"
                                    name="description"
                            >{{ old('name', $coffee->description) }}</textarea>

                        </div>
                    </div>
                    <div class="WpcFormGroup">
                        <div class="WpcButtonGroup">
                            <label class="WpcEditButton">
                                <input class="d-none" type="file" name="thumbnail" id="avatar_edit" accept="image/*"/>
                                <span class="Icon">
                          <i class="wpc-icon wpc-edit"></i>
                        </span>
                                <span class="Text">Change Photo</span>
                            </label>
{{--                            <a href="{{ route('coffees.delete', $coffee->id) }}" class="WpcEditButton">--}}
{{--                                <span class="Icon">--}}
{{--                                  <i class="wpc-icon wpc-delete"></i>--}}
{{--                                </span> --}}
{{--                                <span class="Text">Delete Coffee From Menu</span>--}}
{{--                            </a>--}}
                            <a data-delete-url="{{ route('coffees.delete', $coffee->id) }}"
                               data-toggle="modal"
                               data-target=".WpcDeleteConfirmationModal"
                               href="#" class="WpcEditButton">
										<span class="Icon">
											<i class="wpc-icon wpc-delete"></i>
										</span>
                                <span class="Text">Delete Coffee From Menu</span>
                            </a>
                        </div>
                        <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                        @error('thumbnail')
                        <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="WpcFormGroup">
                        <button class="WpcButton WpcFilled">
                            <span class="Text">Save Changes</span>
                        </button>
                    </div>
                </div>
        </div>
    </div>
</div>
</form>

@push('scripts')
    <script>
        $(function (){
            let editModal = $('#WpcEditCoffeeModal');
            $(editModal).modal('show');
            $(editModal).on('hidden.bs.modal', function (){
                window.location.href = "{{ route('coffees.index') }}";
            });

            $('#avatar_edit').change(function (){
                let file = ($(this).prop('files') &&  $(this).prop('files')[0]) || null ;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $("#avatar_edit").val(null);
                    $('#WpcImgBoxThumb').css({
                        'background-image' : `url(${null})`
                    })
                    return;
                }
                let url = URL.createObjectURL(file);
                $('#WpcImgBoxThumb').css({
                    'background-image' : `url(${url})`
                })
            });
        });
    </script>
@endpush
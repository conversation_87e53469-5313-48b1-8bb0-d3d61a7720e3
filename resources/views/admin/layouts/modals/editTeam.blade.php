<form action="{{ route('team.update', $editTeam->id) }}" method="post" enctype="multipart/form-data">
    @csrf
<div
        class="modal fade WpcEditTeamModal"
        id="WpcEditTeamModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">Edit Team</h5>

            </div>
            <div class="WpcModalBody">
                @include('admin.components.inputs.text', [ 'name' => 'name', 'value' => old('name', $editTeam->name), 'label' => 'Name'])
                <div class="WpcFormGroup">
                    <label class="WpcFormLabel">Add Image</label>
                    <div class="d-flex">
                        <div class="WpcImageSelector">
                            <label
                                    class="WpcImgBox"
                                    id="WpcImgBoxThumb"
                                    style="background-image: url('{{ $editTeam->thumbnail }}')"
                            >
                            </label>
                        </div>
                        <textarea rows="2" placeholder="Description" name="description" class="form-control">{{ $editTeam->description }}</textarea>
                    </div>
                </div>
                <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                @error('avatar')
                <div class="text-danger validation-error">{{ $message }}</div>
                @enderror

                <div class="WpcFormGroup">
                    <div class="WpcButtonGroup">
                      <label class="WpcEditButton">
                        <input type="file" name="thumbnail" id="thumbnail" class="d-none"/>
                        <span class="Icon">
                          <i class="wpc-icon wpc-edit"></i>
                        </span>
                        <span class="Text">Change Photo</span>
                      </label>
                        <a data-delete-url="{{ route('team.delete', $editTeam->id) }}"
                           data-toggle="modal"
                           data-target=".WpcDeleteConfirmationModal"
                           href="#" class="WpcEditButton">
									<span class="Icon">
										<i class="wpc-icon wpc-delete"></i>
									</span>
                            <span class="Text">Delete Team</span>
                        </a>
                    </div>

                </div>
                <div class="WpcFormGroup">
                    <button class="WpcButton WpcFilled">
                        <span class="Text">Save Changes</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

@push('scripts')
    <script>
        $(function (){
            let editModal = $('#WpcEditTeamModal');
            let editThumbInput = $('#thumbnail');
            let ediThumbWrapper = $('#WpcImgBoxThumb');

            // $(editModal).modal('show');

            @if($errors->any())
            $(editModal).modal('show');
            @endif

            $(editModal).on('hidden.bs.modal', function (){
                window.location.href = "{{ route('teams.index') }}";
            });

            $(editThumbInput).change(function () {
                let file = ($(this).prop('files') &&  $(this).prop('files')[0]) || null ;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $(editThumbInput).val(null);
                    $(ediThumbWrapper).css({
                        'background-image' : `url(${null})`
                    })
                    return;
                }
                let url = URL.createObjectURL(file);
                $(ediThumbWrapper).css({
                    'background-image' : `url(${url})`
                })
            });
        });
    </script>
@endpush
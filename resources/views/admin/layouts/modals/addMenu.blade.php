<div
        class="modal fade WpcAddCoffeeModal" id="WpcAddCoffeeModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md">
        <form action="{{ route('coffees.store') }}" method="post" enctype="multipart/form-data">
            @csrf
            <div class="modal-content">
                <div class="WpcModalHead">
                    <h5 class="WpcModalTitle" id="exampleModalLabel">Add Coffee</h5>
                    <label class="WpcCheckbox ml-auto">
                        <input type="checkbox" name="is_active" value="1" checked/>
                        <span class="Text">Available Now</span>
                    </label>
                </div>
                <div class="WpcModalBody">
                    <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                    @include('admin.components.inputs.text', ['name' => 'name', 'label' => 'Name', 'value' => old('name')])
                    @include('admin.components.inputs.number', ['min' => 1, 'name' => 'required_coupon', 'label' => 'Required Coupon', 'value' => old('required_coupon') ?? 1])
                    <div class="WpcFormGroup d-flex justify-content-between flex-row flex-wrap">
                        @foreach(trans('constants.ingredients') as $key => $val)
                            <label class="WpcCheckbox mb-2">
                                <input type="checkbox" name="ingredients[]" value="{{ $key }}" @if(in_array($key, old('ingredients',[]))) checked @endif/>
                                <span class="Text">{{ $val }}</span>
                            </label>
                        @endforeach
                    </div>
                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Add Image</label>
                        <div class="d-flex">
                            <div class="WpcImageSelector">
                                <label class="WpcImgBox" id="WpcImgBox">
                                    <input type="file" name="thumbnail" id="avatar" accept="image/*"/>
                                    <span class="WpcDummyImage">
											<i class="wpc-icon wpc-camera"></i>
										</span>
                                </label>
                            </div>
                            <textarea id="ingredients" name="description" rows="2" placeholder="Description" class="form-control">@if( old('description')){{ old('description')}}@endif</textarea>
                        </div>
                    </div>
                    @error('thumbnail')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                    @error('ingredients')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                    <div class="WpcFormGroup mb-1">
                        <button class="WpcButton WpcFilled">
                            <span class="Text">Add New Coffee</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@push('scripts')
    <script>
        $(function () {

            @if(blank($coffee) && $errors->any())
            $('#WpcAddCoffeeModal').modal('show');
            @endif
            let avatarWrapper = $('#WpcImgBox');
            let avatarSelector = $('#avatar');
            $(avatarSelector).change(function () {
                let file = ($(this).prop('files') && $(this).prop('files')[0]) || null;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $(avatarSelector).val(null);
                    avatarWrapper.css({
                        'background-image': `url(${null})`
                    })
                    return;
                }
                let url = URL.createObjectURL(file);
                avatarWrapper.css({
                    'background-image': `url(${url})`
                })
            });
        });
    </script>
@endpush
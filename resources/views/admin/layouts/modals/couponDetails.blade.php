@foreach($data as $key => $sender)
    <div class="modal fade WpcGiftCouponDetailsModal"
         id="WpcGiftCouponDetailsModal{{$sender->id}}"
         tabindex="-1"
         role="dialog"
         aria-labelledby="myLargeModalLabel"
         aria-hidden="true"
    >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="WpcModalHead">
                    <h5 class="WpcModalTitle align-center" id="exampleModalLabel">Gift Coupon</h5>
                </div>
                <div class="WpcModalBody">
                    <div class="row">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th scope="col" style="vertical-align: middle;text-align: center">Avatar</th>
                                <th scope="col" style="vertical-align: middle;text-align: center">User</th>
                                @if(isset($sender->items) && count($sender->items))
                                    @foreach($sender->items as $item)
                                        <th scope="col"
                                            style="vertical-align: middle;text-align: center">{{json_decode($item)->name}}</th>
                                    @endforeach
                                @endif
                            </tr>
                            </thead>
                            <tbody>
                            @if(isset($sender->items) && count($sender->receivers) == 0)
                                <tr>
                                    <th style="vertical-align: middle;text-align: center">
                                        <img style="border-radius:66px;height:70px;width:70px;min-width:20px;"
                                             src="{{$sender->onereceiver ? $sender->onereceiver : asset('assets/img/user4.png')}}" alt="">
                                    </th>
                                    <th style="vertical-align: middle;text-align: center">{{$sender->onereceiver ? $sender->onereceiver->name : ''}}</th>
                                    @foreach($sender->items  as $item)
                                        <th style="vertical-align: middle;text-align: center">{{json_decode($item)->qty}}</th>
                                    @endforeach
                                </tr>
                            @else
                            @foreach($sender->receivers as $data)
                                <tr>
                                    <th style="vertical-align: middle;text-align: center">
                                        <img style="border-radius:66px;height:70px;width:70px;min-width:20px;"
                                             src="{{data_get($data, "receiverDetails.avatar", "")}}" alt="">
                                    </th>
                                    <th style="vertical-align: middle;text-align: center">{{data_get($data, "receiverDetails.name", "")}}</th>
                                    @foreach($sender->items  as $item)
                                        <th style="vertical-align: middle;text-align: center">{{json_decode($item)->qty}}</th>
                                    @endforeach
                                </tr>
                            @endforeach
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endforeach

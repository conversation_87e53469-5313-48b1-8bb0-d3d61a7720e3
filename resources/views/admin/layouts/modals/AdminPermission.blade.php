@foreach($admins as $admin)
<div
        class="modal fade WpcAddPermissionModal" id="WpcAddPermissionModal{{$admin->id}}"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md">
        <form action="{{ route('settings.permission.store', $admin->id) }}" method="post" enctype="multipart/form-data">
            @csrf
            <div class="modal-content">
                <div class="WpcModalHead">
                    <h5 class="WpcModalTitle" id="exampleModalLabel">Permission</h5>
                </div>

                <div class="WpcModalBody">
                    <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                    @foreach($permissions as $permission)
                    <div class="WpcFormGroup d-flex justify-content-between flex-row flex-wrap">
                            <label class="WpcCheckbox mb-2">
                                <input type="checkbox" name="permission[]" value="{{ $permission }}" {{in_array($permission, $admin->permission ?? []) ? 'checked' : ''}}/>
                                <span class="Text">{{ Str::ucfirst($permission) }}</span>
                            </label>
                    </div>
                    @endforeach
                    <div class="WpcFormGroup mb-1">
                        <button class="WpcButton WpcFilled">
                            <span class="Text">Save Changes</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endforeach

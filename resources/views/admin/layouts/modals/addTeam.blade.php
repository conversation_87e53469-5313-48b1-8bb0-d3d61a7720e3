<div class="modal fade WpcAddTeamModal" id="WpcAddTeamModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md">
        <form method="POST" action="{{ route('teams.store') }}" enctype="multipart/form-data">
            @csrf
            <div class="modal-content">
                <div class="WpcModalHead">
                    <h5 class="WpcModalTitle" id="exampleModalLabel">Add Team</h5>
                </div>
                <div class="WpcModalBody">
                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Name</label>
                        <input name="name" id="name" type="text" class="form-control"/>
                        @error('name', 'addTeam')
                            <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Add Image</label>
                        <div class="d-flex">
                            <div class="WpcImageSelector">
                                <label class="WpcImgBox" id="WpcImgBox">
                                    <input type="file" name="avatar" id="avatar"/>
                                    <span class="WpcDummyImage">
											<i class="wpc-icon wpc-camera"></i>
										</span>
                                </label>
                            </div>
                            <textarea
                                    id="description"
                                    name="description"
                                    rows="2"
                                    placeholder="Description"
                                    class="form-control"
                              >@if( old('description')){{ old('description')}}@endif</textarea>
                        </div>
                         @error('description', 'addTeam')
                            <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                        <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                        @error('avatar', 'addTeam')
                            <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="WpcFormGroup">
                        <button class="WpcButton WpcFilled">
                            <span class="Text">Add New Team</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@push('scripts')
    <script>
        $(function () {
            @if($errors->addTeam->any())
                $('#WpcAddTeamModal').modal('show');
            @endif
            let avatarWrapper = $('#WpcImgBox');
            let avatarSelector = $('#avatar');
            $(avatarSelector).change(function () {
                let file = ($(this).prop('files') && $(this).prop('files')[0]) || null;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $(avatarSelector).val(null);
                    avatarWrapper.css({
                        'background-image': `url(${null})`
                    })
                    return;
                }
                let url = URL.createObjectURL(file);
                avatarWrapper.css({
                    'background-image': `url(${url})`
                })
            });
        });
    </script>
@endpush
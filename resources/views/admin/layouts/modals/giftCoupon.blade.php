@php
$allMenu = \App\Models\Menu::all();
$allUser = \App\Models\User::all();
$employee = $employee ?? null
@endphp
<div class="modal fade WpcGiftCouponModal"
     id="WpcGiftCouponModal"
     tabindex="-1"
     role="dialog"
     aria-labelledby="myLargeModalLabel"
     aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">Gift Coupon</h5>
            </div>
            <div class="WpcModalBody">
                <form id="giftForm" action="{{ route('gift.store') }}" method="post">
                    @csrf
                    <div class="WpcFormGroup">
                        <div class="WpcButtonGroup">
                            <div class="WpcFilterSelectorWrapper">
                                <label>Type</label>
                                <select id="menuOptions" class="WpcFilterSelector">
                                    @foreach($allMenu as $menu)
                                        @php
                                        $data = json_encode([
                                            'id' => $menu->id,
                                            'name' => $menu->name,
                                            'thumb' => $menu->thumbnail,
                                            'req_coupon' => $menu->required_coupon
                                        ], JSON_UNESCAPED_SLASHES);
                                        @endphp
                                        <option value="{{ $menu->id }}" data-menu="{{ $data }}">{{$menu->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="WpcIncDecButtonGroup">
                                <button class="WpcDecButton"><i class="wpc-icon wpc-minus"></i></button>
                                <input id="quantity" class="WpcIncDecInput" type="number" value="1" min="1"/>
                                <button class="WpcIncButton"><i class="wpc-icon wpc-plus"></i></button>
                            </div>
                            <button id="addCoffee" class="WpcButton ml-auto">
                                <div class="Text">Add Coffee</div>
                            </button>

                        </div>

                    </div>
                    <div class="WpcFormGroup">
                        <div id="replaceableDiv" class="WpcCoffeePreviewWrapper"></div>
                    </div>
                    @error('items', 'gift')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                    <div class="WpcFormGroup">
                        <select id="employee" name="user_id[]" data-placeholder="Select employee" multiple="multiple">
                            <option value="all">All Employee</option>
                            @foreach($allUser as  $key => $user)
                                <option @if($user->id == old('user_id', data_get($employee,'id'))) selected @endif value="{{$user->id}}">{{$user->name}}</option>
                            @endforeach
                        </select>
                        @error('user_id', 'gift')
                        <div class="text-danger validation-error">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="WpcFormGroup">
                        <textarea rows="6" class="form-control" id="message" name="message" placeholder="Message:"></textarea>
                    </div>
                    <div class="WpcFormGroup">
                        <button id="submitGift" class="WpcButton WpcFilled">
                            <span class="Text">Gift Coupon</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script>
        $(function (){
            $('#employee').select2();

            @if($errors->gift->any())
            $('#WpcGiftCouponModal').modal('show');
            @endif

            $('.WpcIncButton').on('click',function(e){
                e.preventDefault();
                var $qty=$(this).closest('.WpcIncDecButtonGroup').find('.WpcIncDecInput');
                var currentVal = parseInt($qty.val());
                if (!isNaN(currentVal)) {
                    $qty.val(currentVal + 1);
                }
            });
            $('.WpcDecButton').on('click',function(e){
                e.preventDefault();
                var $qty=$(this).closest('.WpcIncDecButtonGroup').find('.WpcIncDecInput');
                var currentVal = parseInt($qty.val());
                if (!isNaN(currentVal) && currentVal > 1) {
                    $qty.val(currentVal - 1);
                }
            });

            const menuItems = [];

            let addCoffeeBtn = $('#addCoffee'),
                itemWrapper = $('#replaceableDiv'),
                menuOptions = $('#menuOptions'),
                menuQty = $('#quantity');


            $(addCoffeeBtn).click(function (e){
                e.preventDefault();
                let menu = $(menuOptions).children('option:selected').attr('data-menu') || "";
                let qty = $(menuQty).val() || 1;
                addIteams(menu, qty);
            });

            const bindDelete = () => {
                $('.deleteItem').unbind().click(function (e){
                    e.preventDefault();
                    let index = $(this).attr('data-index');
                    deleteItem(index);
                });
            }

            const addIteams = (menu, qty) => {
                menu = JSON.parse(menu);
                menu['qty'] = parseInt(qty);
                let itemIndex = menuItems.findIndex(item => item.id == menu.id);
                if (itemIndex > -1) {
                    menuItems[itemIndex]['qty'] += menu.qty
                } else {
                    menuItems.push(menu);
                }
                renderItems();
            }

            const deleteItem = (index) => {
                menuItems.splice(index, 1);
                renderItems();
            }

            const renderItems = () => {
                let temps = menuItems.map((item, index) => {
                    return `
                        <div class="WpcCoffeePreview">
                            <button class="deleteItem" data-index="${index}"><i class="wpc-icon wpc-delete"></i></button>
                            <div class="WpcCoffeePreviewImage" style="background-image: url(${item.thumb})"></div>
                            <p class="WpcCoffeePreviewDetails"> ${item.name} : ${item.qty}</p>
                            <input type="hidden" name="items[${item.id}]" value='${JSON.stringify(item)}'>
                        </div>
                    `;
                });

                $(itemWrapper).html(temps.join(" "));

                bindDelete();

            }
        })
    </script>
@endpush

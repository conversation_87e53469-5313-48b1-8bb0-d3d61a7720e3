<form action="{{ route('employees.update', $employeeEdit->id) }}" method="post" enctype="multipart/form-data">
@csrf
<div
        class="modal fade WpcEditEmployeeModal"
        id="WpcEditEmployeeModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myLargeModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">Edit Profile</h5>
            </div>
            <div class="WpcModalBody">
                <div class="WpcFormGroup">
                    <label class="WpcFormLabel">Add Image</label>
                    <div class="d-flex align-item-center">
                        <div class="WpcImageSelector">
                            <label
                                    class="WpcImgBox"
                                    id="avatarWrapper"
                                    style="background-image: url('{{asset($employeeEdit->avatar)}}')"
                            >
                            </label>
                        </div>
                        <div class="WpcButtonGroup">
                        <label class="WpcEditButton">
                          <input type="file" name="avatar" id="editAvatar" class="d-none"/>
                          <span class="Icon">
                            <i class="wpc-icon wpc-edit"></i>
                          </span>
                          <span class="Text">Change Photo</span>
                        </label>
                            <a data-delete-url="{{ route('employees.delete', $user->id) }}"
                               data-toggle="modal"
                               data-target=".WpcDeleteConfirmationModal"
                               href="#" class="WpcEditButton">
										<span class="Icon">
											<i class="wpc-icon wpc-delete"></i>
										</span>
                                <span class="Text">Delete Profile</span>
                            </a>
                        </div>
                    </div>
                    <div class="text-danger validation-error image_size_error" id="image_size_error"></div>
                    @error('avatar', 'editEmployee')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                </div>
                @include('admin.components.inputs.text', ["name" => "name", "value" =>  old('name', $employeeEdit->name), 'label' => "Name", 'errorBag' => 'editEmployee' ])
                <div class="WpcFormGroup">
                    <div class="d-flex align-items-center">
                        <label class="WpcFormLabel">Team</label>
                        <a href="{{ route('teams.index') }}" class="WpcAddButton ml-auto">
                            <span class="Icon"><i class="wpc-icon wpc-plus"></i></span>
                            <span class="Text">Add New Team</span>
                        </a>
                    </div>
                    <select id="editTeams" name="teams[]" multiple="multiple" data-placeholder="Select teams">
                        @foreach($teams as $id => $title)
                            <option value="{{$id}}" {{ $employeeEdit->teams->pluck('name','id')->has($id) ? "selected" : ""}}>{{ $title }}</option>
                        @endforeach
                    </select>
                    @error('teams', 'editEmployee')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                </div>
                @include('admin.components.inputs.text', ["name" => "designation", "value" =>  old('designation', $employeeEdit->designation), 'label' => "Designation", 'errorBag' => 'editEmployee'])

                @include('admin.components.inputs.text', ["name" => "email", "value" =>  old('email', $employeeEdit->email), 'label' => "Email", 'errorBag' => 'editEmployee'])

                @include('admin.components.inputs.password', ["name" => "password", 'label' => "Password", 'errorBag' => 'editEmployee'])

                <div class="WpcFormGroup">
                    <label class="WpcFormLabel">Role</label>
                    <select class="form-control" name="role">
                        @foreach(trans('constants.user.roles') as $role => $label)
                            <option value="{{ $role }}" {{ $role == old('role', $employeeEdit->role) ? "selected" : ""}}> {{ $label }} </option>
                        @endforeach
                    </select>
                    @error('role', 'editEmployee')
                    <div class="text-danger validation-error">{{ $message }}</div>
                    @enderror
                </div>
                <div class="WpcFormGroup">
                    <button class="WpcButton WpcFilled">
                        <span class="Text">Save Changes</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>
@push('scripts')
    <script>
        $(function (){
            $('#editTeams').select2();
            let editModal = $('#WpcEditEmployeeModal');
            let editThumbInput = $('#editAvatar');
            let ediThumbWrapper = $('#avatarWrapper');

            // $(editModal).modal('show');

            @if($errors->editEmployee->any())
                $(editModal).modal('show');
            @endif

            $(editModal).on('hidden.bs.modal', function (){
                window.location = window.location.pathname;
            });

            $(editThumbInput).change(function (){
                let file = ($(this).prop('files') &&  $(this).prop('files')[0]) || null ;
                if (Math.round(file.size / 1024) > 1024) {
                    $('.image_size_error').html('Avatar size must be less than 1 MB');
                    $(editThumbInput).val(null);
                    $(ediThumbWrapper).css({
                        'background-image' : `url(${null})`
                    })
                    return;
                }
                let url = URL.createObjectURL(file);
                $(ediThumbWrapper).css({
                    'background-image' : `url(${url})`
                })
            });
        });
    </script>
@endpush
<form enctype="multipart/form-data"
      action="{{ route('employees.coupon.update', ['user' => $user->id, 'coffee' => ':menu_id:']) }}"
      id="coupon_edit_form"
      method="post">
    @csrf
<div
    class="modal fade WpcEditCouponModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="myLargeModalLabel"
    aria-hidden="true"
    id="WpcEditCouponModal"
>
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle" id="exampleModalLabel">Edit Coupon</h5>
            </div>
            <div class="WpcModalBody">
                <div class="WpcFormGroup">
                    <div class="WpcButtonGroup">
                        <div class="WpcCoffeePreview">
                            <div class="WpcCoffeePreviewImage"></div>
                            <p class="WpcCoffeePreviewDetails"></p>
                        </div>
                        <div class="WpcIncDecButtonGroup" id="coupon_edit_div">
                            <button class="WpcDecButton" id="coupon_edit_minus">
                                <i class="wpc-icon wpc-minus"></i>
                            </button>
                            <input class="WpcIncDecInput" type="text" name="coupon_count" id="coupon_edit_input" />
                            <button class="WpcIncButton" id="coupon_edit_plus">
                                <i class="wpc-icon wpc-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="WpcFormGroup">
                    <button class="WpcButton WpcFilled">
                        <span class="Text">Save Changes</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

@push('scripts')
<script>

    function editCoupon(menu, coupon_count) {

        $('.WpcCoffeePreviewImage').css({
            'background-image' : `url(${menu.thumbnail})`
        });
        $('.WpcCoffeePreviewDetails').html(`${menu.name} : ${coupon_count}`);
        $('.WpcIncDecInput').val(coupon_count);

        let url = $('#coupon_edit_form').attr('action');
        url = url.replace(':menu_id:',menu.id);

        $('#coupon_edit_form').attr('action', url);

        let editCouponModal = $('#WpcEditCouponModal');
        $(editCouponModal).modal('show');

    }

    $(function (){

        let editModal = $('#WpcEditCouponModal');

        $(editModal).on('hidden.bs.modal', function (){
            location.reload();
        });

    });
</script>
@endpush


<div class="modal fade WpcDeleteConfirmationModal"
     tabindex="-1"
     role="dialog"
     aria-labelledby="myLargeModalLabel"
     aria-hidden="true"
     id="WpcDeleteConfirmationModal"
     data-backdrop="false"
     style="background-color: rgba(0, 0, 0, 0.5);"
>
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="WpcModalHead">
                <h5 class="WpcModalTitle text-center w-100" id="exampleModalLabel">Are You Sure?</h5>
            </div>
            <div class="WpcModalBody">
                <div class="WpcFormGroup">
                    <p class="WpcColorSecondary WpcFont14 text-center">You are going to delete a record.</p>
                </div>
                <div class="WpcButtonGroup mt-4 d-flex justify-content-center">
                    <button class="WpcButton" type="button" data-dismiss="modal" aria-label="Close" id="deleteCancel">
                        <span class="Text" aria-hidden="true">Cancel</span>
                    </button>
                    <button class="WpcButton WpcFilled" id="deleteConfirmation">
                        <span class="Text">Delete</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

@push('scripts')
    <script>
        $(function (){
            let deleteAction = null,
                deleteConfirmationBtn = $("#deleteConfirmation"),
                deleteCancellation = $('#deleteCancel'),
                deleteModal = $('#WpcDeleteConfirmationModal');

            // $(deleteCancellation).click(function (){
            //     deleteAction = null;
            // });

            $(deleteConfirmationBtn).click(function (e){
               e.preventDefault();

               if (deleteAction === null) {
                   alert("No action found");
                   return false;
               }
                window.location.href = deleteAction;
            });


            $('[data-delete-url]').click(function (e){
               e.preventDefault();
               let target = e.currentTarget;
               let url = $(this).attr('data-delete-url');
               deleteAction = url;
            });

            $(deleteModal).on('hidden.bs.modal', function (){
                deleteAction = null;
            });
        });
    </script>
@endpush
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");
* {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

html {
  font-size: 16px;
}

body {
  font-family: 'DM Sans', sans-serif;
  line-height: 1.3;
  font-weight: 400;
  letter-spacing: 0;
  min-height: 100vh;
  background: #fff;
  -webkit-font-smoothing: antialiased !important;
  -moz-font-smoothing: antialiased !important;
  text-rendering: optimizeSpeed;
  scroll-behavior: smooth;
  -webkit-text-stroke: 0px !important;
}

body::-webkit-scrollbar {
  width: 8px;
}

body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(243, 192, 116, 0.1);
  background-image: -webkit-gradient(linear, left top, right bottom, from(rgba(243, 192, 116, 0.05)), to(rgba(243, 192, 116, 0.05)));
  background-image: linear-gradient(to right bottom, rgba(243, 192, 116, 0.05), rgba(243, 192, 116, 0.05));
}

body::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #efab45;
}

ul,
li {
  list-style: none;
}

a {
  color: #19224d;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

a:hover, a:focus {
  text-decoration: none;
  outline: none;
  color: #000;
}

label {
  margin-bottom: 0;
}

button {
  vertical-align: middle;
  background: unset;
}

button:hover, button:focus {
  outline: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin-bottom: 0;
}

ul,
ol {
  margin-bottom: 0;
}

.row {
  margin-left: -25px;
  margin-right: -25px;
}

.row *[class*='col-'] {
  padding-left: 25px;
  padding-right: 25px;
}

.WpcColorPrimary {
  color: #d17842 !important;
}

a.WpcColorPrimary:hover, a.WpcColorPrimary:focus {
  color: #efab45 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorSecondary {
  color: #83858e !important;
}

a.WpcColorSecondary:hover, a.WpcColorSecondary:focus {
  color: #6a6c74 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorSuccess {
  color: #16ad9f !important;
}

a.WpcColorSuccess:hover, a.WpcColorSuccess:focus {
  color: #4ac410 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorDanger {
  color: #ec008c !important;
}

a.WpcColorDanger:hover, a.WpcColorDanger:focus {
  color: #b9006e !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorInfo {
  color: #007cdb !important;
}

a.WpcColorInfo:hover, a.WpcColorInfo:focus {
  color: #005fa8 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorWarning {
  color: #e04f5f !important;
}

a.WpcColorWarning:hover, a.WpcColorWarning:focus {
  color: #d62639 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorDark {
  color: #19224d !important;
}

a.WpcColorDark:hover, a.WpcColorDark:focus {
  color: #0d1127 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorBlack {
  color: #000 !important;
}

.WpcColorLight {
  color: #e6e7e8 !important;
}

a.WpcColorLight:hover, a.WpcColorLight:focus {
  color: #cbced0 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcColorWhite {
  color: #fff !important;
}

a.WpcColorWhite:hover, a.WpcColorWhite:focus {
  color: #e6e6e6 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.WpcBackgroundPrimary {
  background-color: #d17842 !important;
}

.WpcBackgroundSecondary {
  background-color: #83858e !important;
}

.WpcBackgroundSuccess {
  background-color: #16ad9f !important;
}

.WpcBackgroundDanger {
  background-color: #ec008c !important;
}

.WpcBackgroundInfo {
  background-color: #007cdb !important;
}

.WpcBackgroundWarning {
  background-color: #e04f5f !important;
}

.WpcBackgroundDark {
  background-color: #19224d !important;
}

.WpcBackgroundBlack {
  background-color: #000 !important;
}

.WpcBackgroundLight {
  background-color: #e6e7e8 !important;
}

.WpcBackgroundWhite {
  background-color: #fff !important;
}

.GradientPrimary {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#d17842), to(#f9e0ba));
  background-image: linear-gradient(to bottom right, #d17842, #f9e0ba);
  color: #19224d;
}

.GradientSecondary {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#83858e), to(#abacb2));
  background-image: linear-gradient(to bottom right, #83858e, #abacb2);
  color: white;
}

.GradientSuccess {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#16ad9f), to(#90f262));
  background-image: linear-gradient(to bottom right, #16ad9f, #90f262);
  color: white;
}

.GradientDanger {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#ec008c), to(#ff3aaf));
  background-image: linear-gradient(to bottom right, #ec008c, #ff3aaf);
  color: white;
}

.GradientInfo {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#007cdb), to(#29a2ff));
  background-image: linear-gradient(to bottom right, #007cdb, #29a2ff);
  color: white;
}

.GradientWarning {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#e04f5f), to(#eb909a));
  background-image: linear-gradient(to bottom right, #e04f5f, #eb909a);
  color: white;
}

.GradientDark {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#19224d), to(#2c3c87));
  background-image: linear-gradient(to bottom right, #19224d, #2c3c87);
  color: white;
}

.GradientBlack {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#000), to(#262626));
  background-image: linear-gradient(to bottom right, #000, #262626);
  color: white;
}

.GradientLight {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#e6e7e8), to(white));
  background-image: linear-gradient(to bottom right, #e6e7e8, white);
  color: #19224d;
}

.GradientWhite {
  background-image: -webkit-gradient(linear, left top, right bottom, from(#fff), to(white));
  background-image: linear-gradient(to bottom right, #fff, white);
  color: #19224d;
}

.WpcButton {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 50px;
  width: 100%;
  padding: 1px;
  z-index: 1;
  border-radius: 15px;
  background: rgba(243, 192, 116, 0.1);
  border: 1px solid #d17842;
  font-weight: 500;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: background, box-shadow;
  -webkit-transition-property: background, -webkit-box-shadow;
  transition-property: background, -webkit-box-shadow;
  transition-property: background, box-shadow;
  transition-property: background, box-shadow, -webkit-box-shadow;
  position: relative;
}

.WpcButton .Icon {
  height: 24px;
  width: 24px;
  min-width: 24px;
  background: #19224d;
  color: #fff;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50px;
  font-size: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, filter;
  -webkit-transition-property: background, color, -webkit-filter;
  transition-property: background, color, -webkit-filter;
  transition-property: background, color, filter;
  transition-property: background, color, filter, -webkit-filter;
}

.WpcButton .Text {
  padding: 0 25px;
  font-size: 1rem;
  font-weight: 500;
}

.WpcButton .Text:not(:first-child) {
  padding-left: 10px;
}

.WpcButton .Text:not(:last-child) {
  padding-right: 10px;
}

.WpcButton:hover {
  background: #d17842;
  -webkit-box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
          box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
}

.WpcButton.WpcFilled {
  background: #d17842;
  color: #fff;
}

.WpcButton.WpcFilled:hover {
  background: #efab45;
  -webkit-box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
          box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
}

.WpcButton.WpcDisabled {
  background: transparent;
  border-color: rgba(131, 133, 142, 0.2);
  cursor: not-allowed;
}

.WpcButton.WpcDisabled .Text {
  color: #83858e;
}

.WpcButton.WpcDisabled:hover {
  background: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.WpcEditButton {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
}

.WpcEditButton .Icon {
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 10px;
  border: 1px solid rgba(25, 34, 77, 0.1);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1rem;
  color: #d17842;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: border-color, background-color, color;
  transition-property: border-color, background-color, color;
}

.WpcEditButton .Icon:not(:last-child) {
  margin-right: 10px;
}

.WpcEditButton .Text {
  font-size: 0.875rem;
  line-height: 1.125rem;
  color: #83858e;
}

.WpcEditButton:hover .Icon {
  border-color: #d17842;
  background-color: #d17842;
  color: #fff;
}

.WpcEditButton.WpcBigSize .Icon {
  height: 50px;
  width: 50px;
  min-width: 50px;
  border-radius: 12px;
  font-size: 1.25rem;
}

.WpcEditButton.WpcBigSize .Icon:not(:last-child) {
  margin-right: 12px;
}

.WpcEditButton.WpcBigSize .Text {
  font-size: 1rem;
}

.WpcEditButton.WpcFilled .Icon {
  border-color: #d17842;
  background: #d17842;
  color: #19224d;
}

.WpcEditButton.WpcFilled:hover .Icon {
  background: #efab45;
  -webkit-box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
          box-shadow: 0 20px 20px -10px rgba(243, 192, 116, 0.4);
}

.WpcEditButton.WpcDisabled .Icon {
  background: transparent;
  border-color: rgba(131, 133, 142, 0.2);
  color: rgba(131, 133, 142, 0.2);
  cursor: not-allowed;
}

.WpcEditButton.WpcDisabled .Text {
  color: #83858e;
}

.WpcEditButton.WpcDisabled:hover .Icon {
  background: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.WpcAddButton {
  margin-bottom: 10px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
}

.WpcAddButton .Icon {
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 20px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  color: #83858e;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 0.5rem;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: border-color, color;
  transition-property: border-color, color;
}

.WpcAddButton .Icon:not(:last-child) {
  margin-right: 10px;
}

.WpcAddButton .Text {
  font-size: 0.875rem;
  line-height: 1.25;
  color: #83858e;
  -webkit-transition: color 0.3s ease-in-out 0s;
  transition: color 0.3s ease-in-out 0s;
}

.WpcAddButton:hover .Icon {
  border-color: #83858e;
  color: #19224d;
}

.WpcAddButton:hover .Text {
  color: #19224d;
}

.WpcBackButton {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #19224d;
}

.WpcBackButton .Icon {
  font-size: 0.875rem;
  margin-right: 10px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #83858e;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.WpcBackButton .Text {
  font-size: 1.125rem;
  font-weight: 400;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: #83858e;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.WpcBackButton:hover .Icon {
  color: #19224d;
}

.WpcBackButton:hover .Text {
  color: #19224d;
}

.wpc-body {
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 100vh;
}

.mt30 {
  margin-top: 30px;
}

.WpcFormGroup {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.WpcFormGroup .WpcFormLabel {
  font-size: 1rem;
  color: #83858e;
  margin-bottom: 10px;
  margin-right: 10px;
  line-height: 1;
}

.WpcFormGroup:not(:last-child) {
  margin-bottom: 20px;
}

input.form-control {
  height: 50px;
  padding: 0 20px;
  font-size: 1rem;
  color: #19224d;
  background: #fff;
  border-color: rgba(131, 133, 142, 0.2);
  border-radius: 12px;
  border-width: 1px;
}

input.form-control:focus {
  -webkit-box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
          box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
}

input.form-control::-webkit-input-placeholder {
  color: #b8babf;
}

input.form-control:-ms-input-placeholder {
  color: #b8babf;
}

input.form-control::-ms-input-placeholder {
  color: #b8babf;
}

input.form-control::placeholder {
  color: #b8babf;
}

input.form-control:not(:last-child) {
  margin-bottom: 5px;
}

input.form-control.fancy__form {
  border-radius: 0;
  border: 0;
  height: 60px;
  border-bottom: 1px solid rgba(131, 133, 142, 0.2);
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
}

input.form-control[type="password"] {
  padding-right: 120px;
}

.user__thumb__change {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 40px;
}

.user__thumb__change .user__thumb {
  width: 80px;
  border-radius: 10px;
  overflow: hidden;
  margin-right: 15px;
}

.user__thumb__change .user__thumb img {
  width: 100%;
}

.form__group {
  position: relative;
  margin-bottom: 15px;
}

.form__group .forget__pass {
  font-size: 15px;
  font-weight: 400;
  color: #19224d;
  position: absolute;
  top: 20px;
  right: 0;
}

.form__group label {
  margin-bottom: 10px;
  display: inline-block;
  color: #fff;
}

.edit__profile__form {
  padding: 0 15px;
}

.account__controller {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  height: 100vh;
  padding: 15px;
}

.account__controller .site__welcome__logo {
  padding-top: 70%;
}

.account__controller .account__access__content {
  padding-top: 40%;
}

.account__controller .account__access__content h3 {
  font-size: 35px;
  font-weight: 500;
  color: #19224d;
  margin-bottom: 20px;
}

.account__controller .account__access__content p {
  font-size: 16px;
  font-weight: 400;
  color: #83858e;
}

.account__controller .account__access__content .account__access__form {
  margin-top: 70px;
}

.account__controller .alternet__access {
  margin-bottom: 35px;
}

.account__controller .alternet__access .WpcButton {
  margin-bottom: 25px;
}

.account__controller .alternet__access .alternet__text {
  color: #83858e;
  font-weight: 400;
}

.account__controller .alternet__access .alternet__text a {
  color: #19224d;
  font-weight: 500;
}

.header {
  background: #0c0f14;
  padding: 30px 15px 85px;
}

.header .header__nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0 15px 20px;
}

.header .header__nav .nav__icon {
  width: 50px;
}

.header .header__nav .nav__icon a {
  color: #fff;
  font-size: 22px;
}

.header .header__nav .notification__icon a {
  color: #d17842;
  font-size: 22px;
  position: relative;
}

.header .header__nav .notification__icon a:before {
  position: absolute;
  top: 0;
  right: 2px;
  height: 8px;
  width: 8px;
  background: #16ad9f;
  border-radius: 50%;
  content: '';
}

.header .header__greeting {
  padding: 15px;
}

.header .header__greeting p {
  font-size: 15px;
  color: #d17842;
  font-weight: 700;
}

.header .header__greeting h2 {
  font-size: 30px;
  font-weight: 700;
  color: #fff;
}

.section__header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 15px;
  margin-bottom: 15px;
}

.section__header .section__header__title {
  font-size: 24px;
  font-weight: 700;
}

.section__header .section__header__icon {
  height: 40px;
  width: 40px;
  border-radius: 10px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  line-height: 40px;
  text-align: center;
}

.section__header .section__header__icon i {
  color: #d17842;
}

.page__header {
  padding: 10px 15px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 40px;
}

.page__header .left__content,
.page__header .right__content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.page__header .left__content .back__button {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}

.page__header .page__title {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  text-align: center;
}

.page__header .page__title h5 {
  font-size: 16px;
  font-weight: 400;
  color: #19224d;
}

.page__header .right__content {
  text-align: right;
}

.page__header .right__content .clear__button {
  font-size: 14px;
  font-weight: 400;
  color: #19224d;
}

.coupon__statistic {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-top: -69px;
}

.coupon__statistic .statistic__card {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 7.5px);
          flex: 0 0 calc(50% - 7.5px);
  padding: 15px;
  background: #1e222a;
  border-radius: 20px;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.coupon__statistic .statistic__card:hover {
  background: #d17842;
}

.coupon__statistic .statistic__card:hover .card__top .icon {
  background: rgba(28, 10, 7, 0.08);
}

.coupon__statistic .statistic__card:hover p {
  color: #0c0f14;
}

.coupon__statistic .statistic__card .card__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
}

.coupon__statistic .statistic__card .card__top .icon {
  height: 54px;
  width: 54px;
  text-align: center;
  background: #d17842;
  border-radius: 15px;
  margin-right: 10px;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.coupon__statistic .statistic__card .card__top .icon i {
  color: #fff;
  line-height: 54px;
  font-size: 20px;
}

.coupon__statistic .statistic__card .card__top h4 {
  font-size: 30px;
  color: #0c0f14;
  font-weight: 400;
}

.coupon__statistic .statistic__card p {
  font-size: 14px;
  color: #83858e;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.item__wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 15px;
  overflow-x: auto;
}

.item__wrap.flex__wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.item__wrap.flex__wrap .item:nth-child(2n) {
  margin-right: 0;
}

.item__wrap.flex__wrap .item {
  margin-bottom: 15px;
}

.item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 7.5px);
          flex: 0 0 calc(50% - 7.5px);
  margin-right: 15px;
  border-radius: 20px;
  overflow: hidden;
}

.item:last-child {
  margin-right: 0;
}

.item .item__thumb img {
  width: 100%;
}

.item .item__body {
  padding: 20px 10px;
  text-align: center;
  border: 1px solid rgba(243, 192, 116, 0.2);
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
}

.item .item__body .item__name {
  font-size: 18px;
  color: #19224d;
  font-weight: 500;
}

.item .item__body p {
  font-size: 14px;
  font-weight: 400;
  color: #83858e;
  margin-top: 5px;
}

.item .item__body p span {
  color: #19224d;
}

.item--md {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(40% - 10px);
          flex: 0 0 calc(40% - 10px);
  margin-right: 10px;
  border-radius: 18px;
}

.item--md .item__body {
  padding: 10px;
  border-bottom-right-radius: 18px;
  border-bottom-left-radius: 18px;
}

.item--md .item__body .item__name {
  font-size: 16px;
  font-weight: 400;
}

.order__tracking__wrap {
  padding: 0 15px;
}

.order__tracking {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid #d17842;
  border-radius: 20px;
  padding: 5px;
  padding-right: 10px;
  background: #d17842;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.order__tracking:hover {
  background: #d17842;
}

.order__tracking:hover .order__tracking__body p {
  color: #19224d;
}

.order__tracking .order__tracking__thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100px;
          flex: 0 0 100px;
  min-width: 100px;
  margin-right: 15px;
  border-radius: 20px;
  overflow: hidden;
}

.order__tracking .order__tracking__thumb img {
  width: 100%;
}

.order__tracking .order__tracking__body h5 {
  font-size: 14px;
  font-weight: 400;
  color: #19224d;
  margin-bottom: 10px;
}

.order__tracking .order__tracking__body h3 {
  font-size: 18px;
  font-weight: 500;
  color: #19224d;
  margin-bottom: 2px;
}

.order__tracking .order__tracking__body p {
  font-size: 14px;
  font-weight: 400;
  color: #83858e;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.wpc-order__history {
  padding: 0 15px;
  margin: 0;
  list-style: none;
}

.wpc-order__history .item__ordered {
  border: 1px solid rgba(131, 133, 142, 0.1);
  border-radius: 10px;
  padding: 15px;
}

.wpc-order__history .item__ordered:not(:last-child) {
  margin-bottom: 15px;
}

.wpc-order__history .item__ordered .order__id {
  font-size: 13px;
  font-weight: 400;
  color: #d17842;
  margin-bottom: 10px;
}

.wpc-order__history .item__ordered .order__details {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpc-order__history .item__ordered .order__details .item__thumb {
  width: 50px;
  min-width: 50px;
  margin-right: 10px;
}

.wpc-order__history .item__ordered .order__details .item__thumb img {
  width: 100%;
  border-radius: 10px;
}

.wpc-order__history .item__ordered .order__details .item__body h5 {
  font-size: 16px;
  font-weight: 400;
  color: #19224d;
  margin-bottom: 5px;
}

.wpc-order__history .item__ordered .order__details .item__body .order__date {
  font-size: 13px;
  font-weight: 400;
  color: #83858e;
}

.order__queue {
  padding: 0 15px;
}

.wpc__item__modal.show {
  background: rgba(255, 255, 255, 0.7);
}

.wpc__item__modal .modal-dialog {
  min-width: 290px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) !important;
          transform: translate(-50%, -50%) !important;
  margin: 0;
}

.wpc__item__modal .modal-dialog .modal-content {
  background: transparent;
  border: none;
}

.wpc__item__modal .modal-body {
  padding: 0;
}

.wpc__item__modal .modal-body .item {
  border-radius: 20px;
  overflow: hidden;
  background: #1e222a;
}

.wpc__item__modal .modal-body .item .item__body {
  padding: 20px 18px 30px;
}

.wpc__item__modal .modal-body .item .item__body .item__name {
  font-size: 24px;
}

.wpc__item__modal .modal-body .item .item__body p {
  font-size: 18px;
}

.wpc__item__modal .modal-body .item .item__body .WpcButton {
  margin-top: 20px;
}

.wpc-notificatons {
  padding: 0 15px;
}

.wpc-notificatons .notification__item {
  border: 1px solid rgba(25, 34, 77, 0.1);
  border-radius: 10px;
  padding: 15px 25px;
}

.wpc-notificatons .notification__item:not(:last-child) {
  margin-bottom: 15px;
}

.wpc-notificatons .notification__item .notification__time {
  font-size: 13px;
  font-weight: 400;
  color: #83858e;
  line-height: 1.5;
}

.wpc-notificatons .notification__item .card {
  border: 0;
  background: transparent;
}

.wpc-notificatons .notification__item .card .card-header {
  padding: 0;
  background: transparent;
  border-bottom: 0;
}

.wpc-notificatons .notification__item .card .card-header h2 .btn {
  padding: 0;
  font-size: 16px;
  font-weight: 400;
  color: #19224d;
  text-decoration: none;
  position: relative;
}

.wpc-notificatons .notification__item .card .card-header h2 .btn span {
  position: absolute;
  top: 5px;
  right: 0;
  color: #DADADD;
  font-size: 10px;
  -webkit-transform: rotate(0);
          transform: rotate(0);
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.wpc-notificatons .notification__item .card .card-header h2 .btn.collapsed span {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}

.wpc-notificatons .notification__item .card .card-body {
  padding: 0;
  padding: 10px 0;
}

.wpc-notificatons .notification__item .card .card-body p {
  font-size: 14px;
  font-weight: 400;
  color: #83858e;
}

.wpc-navigation__drawer__wrap {
  background: #fff;
  position: fixed;
  top: 0;
  left: -100vw;
  width: 100vw;
  height: 100vh;
  z-index: 99;
  -webkit-transition: all .3s ease;
  transition: all .3s ease;
}

.wpc-navigation__drawer__wrap.show {
  left: 0;
}

.wpc-navigation__drawer {
  padding: 10px 25px 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: 100%;
}

.wpc-navigation__drawer .navigation__drawer__closer {
  margin-bottom: 30px;
  display: inline-block;
}

.wpc-navigation__drawer .navigation__drawer__header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 70px;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user__thumb {
  width: 50px;
  min-width: 50px;
  margin-right: 15px;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user__thumb img {
  width: 100%;
  border-radius: 12px;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user__body .user__name {
  font-size: 18px;
  font-weight: 700;
  color: #19224d;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user__body .designation {
  font-size: 13px;
  font-weight: 400;
  color: #83858e;
}

.wpc-navigation__drawer .navigation__drawer__header .navigation__drawer__user__edit a {
  height: 40px;
  width: 40px;
  border-radius: 10px;
  border: 1px solid rgba(131, 133, 142, 0.3);
  line-height: 40px;
  text-align: center;
  display: inline-block;
  color: #d17842;
  background-color: #242c39;
}

.wpc-navigation__drawer .navigation__menu .navigation__list {
  padding: 15px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.wpc-navigation__drawer .navigation__menu .navigation__list:not(:last-child) {
  border-bottom: 1px solid rgba(131, 133, 142, 0.1);
}

.wpc-navigation__drawer .navigation__menu .navigation__list .icon {
  height: 40px;
  width: 40px;
  line-height: 40px;
  text-align: center;
  border: 1px solid rgba(131, 133, 142, 0.1);
  color: #d17842;
  margin-right: 15px;
  display: inline-block;
  border-radius: 12px;
}

.wpc-navigation__drawer .navigation__menu .navigation__list .text {
  font-size: 18px;
  font-weight: 400;
  color: #19224d;
}

.wpc-navigation__drawer .navigation__menu .navigation__list .text span {
  color: #d17842;
}
/*# sourceMappingURL=style.css.map */
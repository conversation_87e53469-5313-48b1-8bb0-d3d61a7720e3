

.item__wrap {
    display: flex;
    padding: 0 15px 15px;
    overflow-x: auto;
    &.flex__wrap {
        flex-wrap: wrap;
        .item:nth-child(2n) {
            margin-right: 0;
        }
        .item {
            margin-bottom: 15px;
        }
    }
}

.item {
    flex: 0 0 calc(50% - 7.5px);
    margin-right: 15px;
    border-radius: 20px;
    overflow: hidden;
    background-color: $accent;
    padding: 15px;
    &:last-child {
        margin-right: 0;
    }
    .item__thumb {
        padding-top: 100%;
        border-radius: 20px;
    }
    .item__body {
        padding: 20px 10px;
        text-align: center;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        .item__name {
            font-size: 18px;
            color: $white;
            font-weight: 500;
        }
        p {
            font-size: 14px;
            font-weight: 400;
            color: $secondary;
            margin-top: 5px;
            span {
                color: $grey;
            }
        }
        label {
            margin-top: 5px;
            input {
                display: none;
                &:checked {
                    & ~ span {
                        &:before {
                            border-color: $success;
                            content: '\e906';
                            font-family: 'wpc-icon';
                            color: $success;
                            font-size: 10px;
                        }
                    }
                }
            }
            span {
                font-size: 14px;
                font-weight: 400;
                color: $secondary;
                position: relative;
                padding-left: 22px;
                &:before {
                    position: absolute;
                    top: 1px;
                    left: 0;
                    border: 2px solid rgba($secondary, .1);
                    border-radius: 3px;
                    height: 18px;
                    width: 18px;
                    content: '';
                    line-height: 15px;
                }
            }
        }
    }
    &--md {
        flex: 0 0 calc(40% - 10px);
        margin-right: 10px;
        border-radius: 18px;
        .item__body {
            padding: 10px;
            border-bottom-right-radius: 18px;
            border-bottom-left-radius: 18px;
            .item__name {
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

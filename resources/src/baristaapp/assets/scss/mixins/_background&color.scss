// content color
@mixin color-emphasis-variant($parent, $color) {
	#{$parent} {
		color: $color !important;
	}
	a#{$parent} {
		&:hover,
		&:focus {
			@if $color != #000000 {
				color: darken($color, 10%) !important;
				box-shadow: none !important;
			}
		}
	}
}

// content background
@mixin background-emphasis-variant($parent, $color) {
	#{$parent} {
		background-color: $color !important;
	}
}

// gradient background
@mixin gradient-emphasis-variant($parent, $color) {
	#{$parent} {
		background-image: linear-gradient(
			to bottom right,
			$color,
			lighten($color, 15%)
		);
		color: set-text-color($color);
	}
}

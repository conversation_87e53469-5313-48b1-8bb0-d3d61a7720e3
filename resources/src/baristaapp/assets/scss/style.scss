@import 'variables';
@import 'mixins/media';
@import 'reboot';
@import 'functions';
@import 'mixins/transition';
@import 'utilities/background&color';
@import 'utilities/button';
@import 'utilities/utilities';
@import 'utilities/form';
@import 'utilities/welcome';
@import 'utilities/header';
@import 'utilities/statistic';
@import 'utilities/item';
@import 'utilities/order';
@import 'utilities/modal';
@import 'utilities/notification';
@import 'utilities/drawer';
@import 'utilities/employee';


body {
    color: #f3f3f3;
}
// .wpc-body {
//     background-color: #fff;
// }


// .wpc-body {
// 	width: 360px;
// 	height: 780px;
// 	margin: 50px auto 0;
// 	padding-bottom: 30px;
// }
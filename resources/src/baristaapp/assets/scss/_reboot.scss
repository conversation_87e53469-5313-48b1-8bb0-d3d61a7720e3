// google font
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

// basic
* {
	margin: 0;
	padding: 0;
	border: none;
	outline: none;
	box-sizing: border-box;
}
html {
	font-size: 16px;
}
body {
	font-family: 'DM Sans', sans-serif;
	line-height: 1.3;
	font-weight: 400;
	letter-spacing: 0;
	min-height: 100vh;
	background: $white;
	-webkit-font-smoothing: antialiased !important;
	-moz-font-smoothing: antialiased !important;
	text-rendering: optimizeSpeed;
	scroll-behavior: smooth;
	-webkit-text-stroke: 0px !important;
	&::-webkit-scrollbar {
		width: 8px;
	}
	&::-webkit-scrollbar-track {
		-webkit-box-shadow: inset 0 0 6px rgba($primary, 0.1);
		background-image: linear-gradient(
			to right bottom,
			rgba($primary, 0.05),
			rgba($primary, 0.05)
		);
	}
	&::-webkit-scrollbar-thumb {
		border-radius: 3px;
		background: darken($primary, 10%);
	}
}

// component
ul,
li {
	list-style: none;
}
a {
	color: $dark;
	transition: all 0.3s linear;
	&:hover,
	&:focus {
		text-decoration: none;
		outline: none;
    color: $black;
	}
}
label {
	margin-bottom: 0;
}
button {
	vertical-align: middle;
	background: unset;
	&:hover,
	&:focus {
		outline: none;
	}
}
h1,
h2,
h3,
h4,
h5,
h6,
p {
	margin-bottom: 0;
}
ul,
ol {
	margin-bottom: 0;
}

.row {
	margin-left: -25px;
	margin-right: -25px;
	*[class*='col-'] {
		padding-left: 25px;
		padding-right: 25px;
	}
}

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>WPCafe</title>
		<link rel="stylesheet" href="assets/css/bootstrap/bootstrap.min.css" />
		<link rel="stylesheet" href="assets/css/wpc-icon/style.css" />
		<link rel="stylesheet" href="assets/css/style.min.css" />
	</head>

	<body>

		<div class="wrapper">

			<div class="wpc-body">
				<div class="page__header">
					<div class="left__content">
						<a href="#" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</a>
					</div>
					<div class="page__title">
						<h5>Notification</h5>
					</div>
					<div class="right__content">
						<a href="#" class="clear__button">Clear all</a>
					</div>
				</div>
	
				<div class="section__header">
					<h3 class="section__header__title">Today</h3>
				</div>
	
				<div class="wpc-notificatons">
					<div class="accordion" id="accordionExample">
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingOne">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
							
								<div id="collapseOne" class="collapse" aria-labelledby="headingOne" data-parent="#accordionExample">
								  <div class="card-body">
									<p>Some placeholder content for the first accordion panel. This panel is shown by default, thanks to the <code>.show</code> class.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">3 hour ago</p>
						</div>
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingTwo">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
								<div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionExample">
								  <div class="card-body">
									<p>Some placeholder content for the second accordion panel. This panel is hidden by default.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">5 hour ago</p>
						</div>
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingThree">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
								<div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#accordionExample">
								  <div class="card-body">
									<p>And lastly, the placeholder content for the third and final accordion panel. This panel is hidden by default.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">15 hour ago</p>
						</div>
					  </div>
				</div>
				
				<div class="section__header mt30">
					<h3 class="section__header__title">Yesterday</h3>
				</div>
	
				<div class="wpc-notificatons">
					<div class="accordion" id="accordionExample2">
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingOne1">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapseOne1" aria-expanded="true" aria-controls="collapseOne1">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
							
								<div id="collapseOne1" class="collapse show" aria-labelledby="headingOne1" data-parent="#accordionExample2">
								  <div class="card-body">
									<p>Some placeholder content for the first accordion panel. This panel is shown by default, thanks to the <code>.show</code> class.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">3 hour ago</p>
						</div>
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingTwo1">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo1" aria-expanded="false" aria-controls="collapseTwo1">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
								<div id="collapseTwo1" class="collapse" aria-labelledby="headingTwo1" data-parent="#accordionExample2">
								  <div class="card-body">
									<p>Some placeholder content for the second accordion panel. This panel is hidden by default.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">5 hour ago</p>
						</div>
						<div class="notification__item">
							<div class="card">
								<div class="card-header" id="headingThree1">
								  <h2 class="mb-0">
									<button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle="collapse" data-target="#collapseThree1" aria-expanded="false" aria-controls="collapseThree1">
										You have an order for Expresso
										<span><i class="wpc-icon wpc-arrow-down"></i></span>
									</button>
								  </h2>
								</div>
								<div id="collapseThree1" class="collapse" aria-labelledby="headingThree1" data-parent="#accordionExample2">
								  <div class="card-body">
									<p>And lastly, the placeholder content for the third and final accordion panel. This panel is hidden by default.</p>
								  </div>
								</div>
							  </div>
							  <p class="notification__time">15 hour ago</p>
						</div>
					  </div>
				</div>
				
			</div>
		</div>

		<script src="assets/js/jquery/jquery-3.4.1.min.js"></script>
		<script src="assets/js/bootstrap/bootstrap.bundle.min.js"></script>
		<script src="assets/js/custom.js"></script>
	</body>
</html>


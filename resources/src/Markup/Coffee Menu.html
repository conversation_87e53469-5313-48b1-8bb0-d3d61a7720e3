<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>WPCafe</title>
		<link rel="stylesheet" href="assets/css/bootstrap/bootstrap.min.css" />
		<link rel="stylesheet" href="assets/css/wpc-icon/style.css" />
		<link rel="stylesheet" href="../../../public/assets/css/style.min.css" />
	</head>
	<body>
		<div class="WpcPageBody">
			<!-- sidebar start -->
			<section class="WpcSidebar">
				<div class="WpcSidebarContent">
					<div class="WpcSidebarContentTop">
						<a href="#" class="WpcSiteLogo">
							<h2 class="WpcSiteTitle">WPCafe</h2>
						</a>
						<a href="#" class="WpcSiteLogoCollapsed">
							<h2 class="WpcSiteTitle">WPC</h2>
						</a>
					</div>
					<nav class="WpcSidebarContentMiddle">
						<ul class="WpcSidebarNav">
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-speedometer"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Dashboard</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-user"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Employees</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink Active">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-list"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Coffee Menu</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-order"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Order History</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-settings"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Settings</div>
								</a>
							</li>
						</ul>
					</nav>
				</div>
			</section>
			<!-- sidebar end -->

			<main class="WpcContentArea">
				<!-- site header -->
				<header class="WpcContentAreaHeader">
					<div class="WpcHeaderWrapper">
						<div
							class="
								WpcContentAreaHeaderRight
								d-flex
								align-items-center
								ml-auto
							"
						>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcNotificationIcon WpcHasNotification mr-4"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<span class="Icon">
										<i class="wpc-icon wpc-notification"></i>
									</span>
								</button>
								<div
									class="
										dropdown-menu dropdown-menu-middle
										WpcNotificationDropdown
									"
								>
									<ul>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
									</ul>
								</div>
							</div>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcProfileControlButton"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<div class="WpcProfileImage">
										<img
											src="assets/img/bruce-mars-AndE50aaHn4-unsplash%201.png"
											alt=""
										/>
									</div>
									<div class="WpcProfileDetails">
										<h4 class="WpcUserName">Jhonathan Doe</h4>
									</div>
								</button>
								<div
									class="dropdown-menu dropdown-menu-right WpcProfileDropdown"
								>
									<ul>
										<li>
											<a href="#" class="Active">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-user"></i>
												</span>
												<p class="WpcNavigationText">Profile</p>
											</a>
										</li>
										<li>
											<a href="#">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-logout"></i>
												</span>
												<p class="WpcNavigationText">Logout</p>
											</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</header>
				<div class="WpcContentAreaBody">
					<section class="WpcInfoBoxSection">
						<div class="WpcSectionTitleWrap">
							<h4 class="WpcSectionTitle mr-2">Coffee Menu</h4>
						</div>
						<div class="WpcInfoBoxWrapper">
							<div class="row WpcHasColGap">
								<div class="col-md-4">
									<button
										class="WpcInfoBox WpcAddItem"
										data-toggle="modal"
										data-target=".WpcAddCoffeeModal"
									>
										<h3 class="WpcInfoBoxTitle">Add New Coffee</h3>
									</button>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
                    <span class="WpcRibbonStyle"><span>Available</span></span>
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee1.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee2.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee3.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee4.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee1.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee2.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcEditCoffeeModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="WpcInfoBox">
										<div
											class="WpcInfoBoxImage"
											style="background-image: url('assets/img/coffee3.png')"
										></div>
										<div class="WpcInfoBoxDetails">
											<div class="dropdown WpcToggleDropdown">
												<button
													class=""
													type="button"
													data-toggle="dropdown"
													aria-haspopup="true"
													aria-expanded="false"
												>
													<i class="wpc-icon wpc-dots-vertical"></i>
												</button>
												<div class="dropdown-menu dropdown-menu-middle">
													<ul>
														<li>
															<button>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-edit"></i>
																</span>
																<p class="WpcNavigationText">Edit</p>
															</button>
														</li>
														<li>
															<button
																data-toggle="modal"
																data-target=".WpcDeleteConfirmationModal"
															>
																<span class="WpcNavigationIcon">
																	<i class="wpc-icon wpc-delete"></i>
																</span>
																<p class="WpcNavigationText">Delete</p>
															</button>
														</li>
													</ul>
												</div>
											</div>
											<h3 class="WpcInfoBoxTitle">Mocha</h3>
											<p class="WpcPostGridContent">
												Milk Foam, Steamed Milk, HotChocolate, Espresso
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</section>
				</div>
			</main>
		</div>
		<div
			class="modal fade WpcAddCoffeeModal"
			tabindex="-1"
			role="dialog"
			aria-labelledby="myLargeModalLabel"
			aria-hidden="true"
		>
			<div class="modal-dialog modal-md">
				<div class="modal-content">
					<div class="WpcModalHead">
						<h5 class="WpcModalTitle" id="exampleModalLabel">Add Coffee</h5>
						<label class="WpcCheckbox ml-auto">
							<input type="checkbox" />
							<span class="Text">Available Now</span>
						</label>
					</div>
					<div class="WpcModalBody">
						<div class="WpcFormGroup">
							<label class="WpcFormLabel">Title</label>
							<input type="text" class="form-control" />
						</div>
						<div class="WpcFormGroup">
							<label class="WpcFormLabel">Add Image</label>
							<div class="d-flex">
								<div class="WpcImageSelector">
									<label class="WpcImgBox">
										<input type="file" name="avatar" id="avatar" />
										<span class="WpcDummyImage">
											<i class="wpc-icon wpc-camera"></i>
										</span>
									</label>
								</div>
								<textarea
									rows="2"
									placeholder="Ingredients"
									class="form-control"
								></textarea>
							</div>
						</div>
						<div class="WpcFormGroup">
							<button class="WpcButton WpcFilled">
								<span class="Text">Add New Coffee</span>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div
			class="modal fade WpcEditCoffeeModal"
			tabindex="-1"
			role="dialog"
			aria-labelledby="myLargeModalLabel"
			aria-hidden="true"
		>
			<div class="modal-dialog modal-md">
				<div class="modal-content">
					<div class="WpcModalHead">
						<h5 class="WpcModalTitle" id="exampleModalLabel">Edit Coffee</h5>
						<label class="WpcCheckbox ml-auto">
							<input type="checkbox" />
							<span class="Text">Available Now</span>
						</label>
					</div>
					<div class="WpcModalBody">
						<div class="WpcFormGroup">
							<label class="WpcFormLabel">Title</label>
							<input type="text" class="form-control" />
						</div>
						<div class="WpcFormGroup">
							<label class="WpcFormLabel">Add Image</label>
							<div class="d-flex">
								<div class="WpcImageSelector">
									<label
										class="WpcImgBox"
										style="background-image: url('assets/img/coffee1.png')"
									>
									</label>
								</div>
								<textarea
									rows="2"
									placeholder="Ingredients"
									class="form-control"
								></textarea>
							</div>
						</div>
						<div class="WpcFormGroup">
							<div class="WpcButtonGroup">
								<label class="WpcEditButton">
                  <input type="file" name="" id="" class="d-none"/>
									<span class="Icon">
										<i class="wpc-icon wpc-edit"></i>
									</span>
									<span class="Text">Change Photo</span>
								</label>
								<button class="WpcEditButton">
									<span class="Icon">
										<i class="wpc-icon wpc-delete"></i>
									</span>
									<span class="Text">Delete Coffee From Menu</span>
								</button>
							</div>
						</div>
						<div class="WpcFormGroup">
							<button class="WpcButton WpcFilled">
								<span class="Text">Save Changes</span>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div
			class="modal fade WpcDeleteConfirmationModal"
			tabindex="-1"
			role="dialog"
			aria-labelledby="myLargeModalLabel"
			aria-hidden="true"
		>
			<div class="modal-dialog modal-md">
				<div class="modal-content">
					<div class="WpcModalHead">
						<h5 class="WpcModalTitle text-center w-100" id="exampleModalLabel">
							Are You Sure?
						</h5>
					</div>
					<div class="WpcModalBody">
						<div class="WpcFormGroup">
							<p class="WpcColorSecondary WpcFont14 text-center">
								Lorem Ipsum is simply dummy text of the printing and typesetting
								industry.
							</p>
						</div>
						<div class="WpcButtonGroup mt-4 d-flex justify-content-center">
							<button
								class="WpcButton"
								type="button"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span class="Text" aria-hidden="true">Cancel</span>
							</button>
							<button class="WpcButton WpcFilled">
								<span class="Text">Delete</span>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="assets/js/jquery/jquery-3.4.1.min.js"></script>
		<script src="assets/js/bootstrap/bootstrap.bundle.min.js"></script>
		<script src="assets/js/custom.js"></script>
	</body>
</html>

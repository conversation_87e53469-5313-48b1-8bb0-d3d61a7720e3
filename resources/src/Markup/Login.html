<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
		<link rel="stylesheet" href="assets/css/bootstrap/bootstrap.min.css" />
		<link rel="stylesheet" href="assets/css/Wpc-Icon/style.css" />
		<link rel="stylesheet" href="../../../public/assets/css/style.min.css" />
	</head>
	<body>
		<div
			class="WpcPageBody WpcLoginBody"
			style="background-image: url('assets/img/bg.svg')"
		>
			<div class="WpcLoginHeader">
				<h2 class="WpcSiteTitle">WPCafe</h2>
			</div>
			<div class="WpcLoginWrapper">
				<div class="WpcLoginContent">
					<div class="WpcLoginForm">
						<div class="WpcLoginTabWrapper">
							<button class="WpcLoginTab WpcActive">Register</button>
							<button class="WpcLoginTab">Login</button>
						</div>
						<form action="">
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Add Image</label>
								<div class="d-flex align-item-center">
									<div class="WpcImageSelector">
										<label class="WpcImgBox">
											<input type="file" name="avatar" id="avatar" />
											<span class="WpcDummyImage">
												<i class="wpc-icon wpc-camera"></i>
											</span>
										</label>
									</div>
								</div>
							</div>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Name</label>
								<input
									type="text"
									class="form-control"
									placeholder="Enter your name..."
								/>
							</div>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Designation</label>
								<input
									type="text"
									class="form-control"
									placeholder="Enter Designation..."
								/>
							</div>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Email Address</label>
								<input
									type="email"
									class="form-control"
									placeholder="<EMAIL>"
								/>
							</div>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Password</label>
								<input
									type="email"
									class="form-control"
									placeholder="********"
								/>
							</div>
							<button class="WpcButton WpcFilled w-100">
								<span class="Text">Sign Up</span>
							</button>
						</form>
					</div>
				</div>
				<div class="WpcLoginContent">
					<div class="WpcLoginForm">
						<div class="WpcLoginTabWrapper">
							<button class="WpcLoginTab">Register</button>
							<button class="WpcLoginTab WpcActive">Login</button>
						</div>
						<form action="">
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Email Address</label>
								<input
									type="email"
									class="form-control"
									placeholder="<EMAIL>"
								/>
							</div>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Password</label>
								<input
									type="email"
									class="form-control"
									placeholder="********"
								/>
							</div>
							<label class="WpcCheckbox mb-4">
								<input type="checkbox" />
								<span class="Text WpcFont500">Remember Me</span>
							</label>
							<button class="WpcButton WpcFilled w-100">
								<span class="Text">Login</span>
							</button>
							<div class="d-flex mt-4">
								<a href="#">Forgot Password?</a>
								<a href="#" class="ml-auto"
									>Resent Verification Link</a
								>
							</div>
						</form>
					</div>
				</div>
				<div class="WpcLoginContent">
					<div class="WpcLoginForm">
						<a href="#" class="WpcBackButton mb-4">
							<span class="Icon"><i class="wpc-icon wpc-arrow-left"></i></span>
							<span class="Text">Back to Login</span>
						</a>
						<h2 class="WpcLoginFormTitle">Forget Password?</h2>
						<form action="">
							<div class="WpcFormGroup mb-4">
								<label class="WpcFormLabel">Email Address</label>
								<input
									type="email"
									class="form-control"
									placeholder="<EMAIL>"
								/>
							</div>
							<button class="WpcButton WpcInfoButton WpcFilled w-100">
								<span class="Text">Send Password Reset Link</span>
							</button>
							<p class="mt-4 WpcColorSecondary">
								Don't Have an Account?
								<a href="#" class="WpcColorDark WpcFont500">Sign Up</a>
							</p>
						</form>
					</div>
				</div>
				<div class="WpcLoginContent">
					<div class="WpcLoginForm">
						<a href="#" class="WpcBackButton mb-4">
							<span class="Icon"><i class="wpc-icon wpc-arrow-left"></i></span>
							<span class="Text">Back to Login</span>
						</a>
						<h2 class="WpcLoginFormTitle">Reset Password</h2>
						<form action="">
							<div class="WpcFormGroup mb-4">
								<label class="WpcFormLabel">New Password</label>
								<input
									type="password"
									class="form-control"
									placeholder="********"
								/>
							</div>
							<div class="WpcFormGroup mb-4">
								<label class="WpcFormLabel">Confirm New Password</label>
								<input
									type="password"
									class="form-control"
									placeholder="********"
								/>
							</div>
							<button class="WpcButton WpcFilled w-100">
								<span class="Text">Reset Password</span>
							</button>
							<p class="mt-4 WpcColorSecondary">
								Don't Have an Account?
								<a href="#" class="WpcColorDark WpcFont500">Sign Up</a>
							</p>
						</form>
					</div>
				</div>
				<div class="WpcLoginContent">
					<div class="WpcLoginForm">
						<a href="#" class="WpcBackButton mb-4">
							<span class="Icon"><i class="wpc-icon wpc-arrow-left"></i></span>
							<span class="Text">Back to Login</span>
						</a>
						<h2 class="WpcLoginFormTitle">Resend Verification</h2>
						<form action="">
							<div class="WpcFormGroup mb-4">
								<label class="WpcFormLabel">Email Address</label>
								<input
									type="email"
									class="form-control"
									placeholder="<EMAIL>"
								/>
							</div>
							<button class="WpcButton WpcFilled w-100">
								<span class="Text">Send Password Reset Link </span>
							</button>
						</form>
					</div>
				</div>
			</div>
		</div>
		<script src="assets/js/jquery/jquery-3.4.1.min.js"></script>
		<script src="assets/js/bootstrap/bootstrap.bundle.min.js"></script>
		<script src="assets/js/custom.js"></script>
	</body>
</html>

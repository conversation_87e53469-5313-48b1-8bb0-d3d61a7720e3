@import 'variables';
@import 'mixins/media';
@import 'reboot';
@import 'functions';
@import 'mixins/transition';
@import 'utilities/background&color';
@import 'utilities/button';
@import 'utilities/dropdown';
@import 'utilities/utilities';
@import 'utilities/form';
@import 'utilities/pagination';
@import 'utilities/login';
@import 'utilities/modal';

//------------------------
//////body structure//////
//------------------------
.WpcPageBody {
	display: flex;
	overflow-x: hidden;
	@import 'utilities/navbar';
	.WpcContentArea {
		display: flex;
		flex-direction: column;
		background: $white;
		min-height: 100vh;
		flex-grow: 1;
		@import 'utilities/header';
		@include respond-above(xl) {
			min-width: calc(100% - 250px);
		}
    @include respond-below(xl) {
      min-width: calc(100% - 100px);
    }
		.WpcContentAreaBody {
			flex: 1;
			padding: 0 50px 100px;
			display: flex;
			flex-direction: column;
      @include respond-below(md) {
        padding: 0 30px 50px;
      }
      @include respond-below(sm) {
        padding: 0 15px 30px;
      }
		}
	}
}

//-------------------------
///////Every Section///////
//-------------------------

.WpcDashboardContentWrapper {
	display: flex;
    @include respond-below(xxl) {
      flex-wrap: wrap-reverse;
    }
	.WpcAnalyticsSection {
		flex: 1;
		margin-right: 50px;
    width: 100%;
    @include respond-below(hl) {
      margin-right: 20px;
    }
    @include respond-below(xxl) {
      margin-right: 0;
      margin-top: 50px;
    }
	}
}

// sidebar design
@include respond-above(xl) {
	.WpcSidebar {
		width: 250px;
		min-width: 250px;
		transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    @include transition-properties(width, min-width);
		.WpcSidebarContent {
			width: 250px;
			min-width: 250px;
			position: fixed;
			top: 0;
			left: 0;
			bottom: 0;
			background: $sidebar;
			padding: 30px 20px;
			transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
      @include transition-properties(width, min-width, overflow);
			// displaying
			display: flex;
			flex-direction: column;
			-webkit-box-orient: vertical;
			z-index: 1010;
			overflow-x: hidden;
			overflow-y: auto;
			&::-webkit-scrollbar {
				width: 6px;
			}
			&::-webkit-scrollbar-track {
				-webkit-box-shadow: inset 0 0 6px rgba($secondary, 0.1);
				background-image: linear-gradient(
					to right bottom,
					rgba($primary, 0.05),
					rgba($info, 0.05)
				);
			}
			&::-webkit-scrollbar-thumb {
				border-radius: 3px;
				background-image: linear-gradient(
					to left top,
					rgba($primary, 0.5),
					rgba($primary, 0.3)
				);
			}
			.WpcSidebarContentTop {
				margin-bottom: 40px;
				.WpcSiteLogo {
					max-width: 170px;
					margin-left: 10px;
					display: flex;
					visibility: visible;
					opacity: 1;
					transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1) 0.1s;
          @include transition-properties(visibility, opacity);
					cursor: pointer;
					img,
					svg {
						display: flex;
						height: 30px;
					}
					.WpcSiteTitle {
						color: $white;
						font-size: 1.875rem;
						font-weight: 500;
						line-height: 1.25;
						display: flex;
						letter-spacing: -0.06em;
					}
				}
				.WpcSiteLogoCollapsed {
					max-width: 45px;
					display: flex;
					visibility: hidden;
					opacity: 0;
					margin-left: 1px;
					transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
          @include transition-properties(visibility, opacity);
					cursor: pointer;
					align-items: center;
					img,
					svg {
						display: none;
						height: 30px;
					}
					.WpcSiteTitle {
						color: $white;
						font-size: 1.875rem;
						font-weight: 500;
						line-height: 1.25;
						display: none;
						letter-spacing: -0.15em;
					}
				}
			}
			&Middle {
				flex: 1;
				margin-bottom: 30px;
			}
			.WpcSidebarNav {
				display: flex;
				flex-direction: column;
				height: 100%;
				&Item {
					&Link {
						padding: 9px;
						text-decoration: none;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						line-height: 1.3rem;
						color: $white;
						border: 1px solid transparent;
						border-radius: 16px;
						cursor: pointer;
						@include transition(all, 0.3s, ease-in-out);
						&Icon {
							width: 40px;
							min-width: 40px;
							height: 40px;
							font-size: 1.125rem;
							display: flex;
							align-items: center;
							justify-content: center;
							text-align: center;
							color: rgba($white, 0.5);
							background: rgba($white, 0.1);
							border: 1px solid rgba($white, 0.05);
							border-radius: 12px;
							@include transition(all, 0.3s, ease-in-out);
						}
						&Text {
							margin-left: 15px;
							font-size: 1.125rem;
							color: rgba($white, 0.5);
							font-weight: 400;
							min-width: 150px;
							transition: all 0.1s ease-in-out;
							transform-origin: left;
							visibility: visible;
							@include transition(all, 0.3s, ease-in-out);
						}
						&:hover,
						&.Active {
							border-color: rgba($white, 0.15);
							.WpcSidebarNavItemLinkIcon {
								background: $primary;
								border-color: $primary;
								color: $white;
							}
							.WpcSidebarNavItemLinkText {
								color: $white;
							}
						}
					}
					&:not(:last-child) {
						margin-bottom: 20px;
					}
				}
			}
		}
		&.Toggled:not(:hover) {
			width: 100px;
			min-width: 100px;
			.WpcSidebarContent {
				width: 100px;
				min-width: 100px;
				overflow: hidden;
				&Top {
					.WpcSiteLogo {
						visibility: hidden;
						opacity: 0;
						.WpcSiteTitle,
						img,
						svg {
							display: none;
						}
					}
					.WpcSiteLogoCollapsed {
						visibility: visible;
						opacity: 1;
						.WpcSiteTitle,
						img,
						svg {
							display: block;
						}
					}
				}
				.WpcSidebarNav {
					.WpcSidebarNavItem {
						.WpcSidebarNavItemLink {
							.WpcSidebarNavItemLinkText {
								margin-left: 0;
								transform: rotateY(90deg);
								visibility: hidden;
								opacity: 0;
							}
						}
					}
				}
			}
		}
	}
}
@include respond-below(xl) {
	.WpcSidebar {
		width: 250px;
		min-width: 250px;
		transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    @include transition-properties(width, min-width);
		.WpcSidebarContent {
			width: 250px;
			min-width: 250px;
			position: fixed;
			top: 0;
			left: 0;
			bottom: 0;
			background: $sidebar;
			padding: 30px 20px;
			transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
      @include transition-properties(width, min-width, overflow);
			// displaying
			display: flex;
			flex-direction: column;
			-webkit-box-orient: vertical;
			z-index: 1010;
			overflow-x: hidden;
			overflow-y: auto;
			&::-webkit-scrollbar {
				width: 6px;
			}
			&::-webkit-scrollbar-track {
				-webkit-box-shadow: inset 0 0 6px rgba($secondary, 0.1);
				background-image: linear-gradient(
					to right bottom,
					rgba($primary, 0.05),
					rgba($info, 0.05)
				);
			}
			&::-webkit-scrollbar-thumb {
				border-radius: 3px;
				background-image: linear-gradient(
					to left top,
					rgba($primary, 0.5),
					rgba($primary, 0.3)
				);
			}
			.WpcSidebarContentTop {
				margin-bottom: 40px;
				.WpcSiteLogo {
					max-width: 170px;
					margin-left: 10px;
					display: flex;
					visibility: visible;
					opacity: 1;
					transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1) 0.1s;
          @include transition-properties(visibility, opacity);
					cursor: pointer;
					img,
					svg {
						display: flex;
						height: 30px;
					}
					.WpcSiteTitle {
						color: $white;
						font-size: 1.875rem;
						font-weight: 500;
						line-height: 1.25;
						display: flex;
						letter-spacing: -0.06em;
					}
				}
				.WpcSiteLogoCollapsed {
					max-width: 45px;
					display: flex;
					visibility: hidden;
					opacity: 0;
					margin-left: 1px;
					transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
          @include transition-properties(visibility, opacity);
					cursor: pointer;
					align-items: center;
					img,
					svg {
						display: none;
						height: 30px;
					}
					.WpcSiteTitle {
						color: $white;
						font-size: 1.875rem;
						font-weight: 500;
						line-height: 1.25;
						display: none;
						letter-spacing: -0.15em;
					}
				}
			}
			&Middle {
				flex: 1;
				margin-bottom: 30px;
			}
			.WpcSidebarNav {
				display: flex;
				flex-direction: column;
				height: 100%;
				&Item {
					&Link {
						padding: 9px;
						text-decoration: none;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						line-height: 1.3rem;
						color: $white;
						border: 1px solid transparent;
						border-radius: 16px;
						cursor: pointer;
						@include transition(all, 0.3s, ease-in-out);
						&Icon {
							width: 40px;
							min-width: 40px;
							height: 40px;
							font-size: 1.125rem;
							display: flex;
							align-items: center;
							justify-content: center;
							text-align: center;
							color: rgba($white, 0.5);
							background: rgba($white, 0.1);
							border: 1px solid rgba($white, 0.05);
							border-radius: 12px;
							@include transition(all, 0.3s, ease-in-out);
						}
						&Text {
							margin-left: 15px;
							font-size: 1.125rem;
							color: rgba($white, 0.5);
							font-weight: 400;
							min-width: 150px;
							transition: all 0.1s ease-in-out;
							transform-origin: left;
							visibility: visible;
							@include transition(all, 0.3s, ease-in-out);
						}
						&:hover,
						&.Active {
							border-color: rgba($white, 0.15);
							.WpcSidebarNavItemLinkIcon {
								background: $primary;
								border-color: $primary;
								color: $white;
							}
							.WpcSidebarNavItemLinkText {
								color: $white;
							}
						}
					}
					&:not(:last-child) {
						margin-bottom: 20px;
					}
				}
			}
		}
    &:not(.Toggled) {
			width: 100px;
			min-width: 100px;
			.WpcSidebarContent {
				width: 100px;
				min-width: 100px;
				overflow: hidden;
				&Top {
					.WpcSiteLogo {
						visibility: hidden;
						opacity: 0;
						.WpcSiteTitle,
						img,
						svg {
							display: none;
						}
					}
					.WpcSiteLogoCollapsed {
						visibility: visible;
						opacity: 1;
						.WpcSiteTitle,
						img,
						svg {
							display: block;
						}
					}
				}
				.WpcSidebarNav {
					.WpcSidebarNavItem {
						.WpcSidebarNavItemLink {
							.WpcSidebarNavItemLinkText {
								margin-left: 0;
								transform: rotateY(90deg);
								visibility: hidden;
								opacity: 0;
							}
						}
					}
				}
			}
		}
	}
}
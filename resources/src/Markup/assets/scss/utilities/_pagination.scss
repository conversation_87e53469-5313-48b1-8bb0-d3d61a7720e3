.WpcPaginationWrapper {
	margin-top: 50px;
  @include respond-below(md) {
    margin-top: 30px;
  }
  @include respond-below(sm) {
    margin-top: 20px;
  }
	.pagination {
		.page-item {
			.page-link {
				height: 40px;
				min-width: 40px;
				border: 1px solid rgba($secondary, 0.3);
				border-radius: 10px;
				color: #c4c4c4;
				display: inline-flex;
				justify-content: center;
				align-items: center;
				padding: 2px;
				font-size: 1rem;
				line-height: 1;
				letter-spacing: -0.03em;
				text-align: center;
				@include transition();
				@include transition-properties(border-color, color);
				&.WpcPrev,
				&.WpcNext {
					font-size: 0.6875rem;
				}
				&:focus,
				&:hover,
				&.WpcActive {
					border-color: $primary;
					color: $primary;
					background: transparent;
					box-shadow: none;
				}
			}
			&:not(:last-child) {
				margin-right: 10px;
			}
		}
	}
}

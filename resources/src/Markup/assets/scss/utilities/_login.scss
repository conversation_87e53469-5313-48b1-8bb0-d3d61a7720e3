.WpcLoginBody {
	min-height: 100vh;
	width: 100%;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	background-attachment: fixed;
	background-color: $sidebar;
	.WpcLoginHeader {
		position: absolute;
		top: 50px;
		left: 50px;
		right: 50px;
		.WpcSiteTitle {
			color: $white;
			font-size: 1.875rem;
			font-weight: 500;
			line-height: 1.25;
			display: flex;
			letter-spacing: -0.06em;
		}
	}
	.WpcLoginWrapper {
		max-width: 600px;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		margin-left: 30px;
		margin-right: 30px;
		.WpcLoginContent {
			flex: 0 0 100%;
			max-width: 100%;
			padding: 120px 0 50px;
			display: flex;
			align-items: center;
			@include transition;
			.WpcLoginForm {
				background: $white;
				padding: 40px;
				width: 100%;
				border-radius: 35px;
				.WpcLoginTab<PERSON>rapper {
					display: flex;
					align-items: center;
					margin-bottom: 40px;
					.WpcLoginTab {
						cursor: pointer;
						font-size: 1.5rem;
						line-height: 1;
						font-weight: 500;
						letter-spacing: -0.03em;
						color: lighten($secondary, 20%);
						&.WpcActive {
							color: $dark;
						}
						&:not(:last-child) {
							margin-right: 30px;
						}
					}
				}
				.WpcLoginFormTitle {
					font-size: 1.5rem;
					font-weight: 500;
					color: $dark;
					margin-bottom: 30px;
				}
			}
		}
	}
}

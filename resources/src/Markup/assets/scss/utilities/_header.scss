// body header
.WpcContentAreaHeader {
	z-index: 1005;
	.WpcHeaderWrapper {
		padding: 50px;
		display: flex;
		align-items: center;
    @include respond-below(md) {
      padding: 30px;
    }
    @include respond-below(sm) {
      padding: 15px;
    }
		.WpcContentAreaHeaderRight {
			.WpcNotificationIcon {
				height: 74px;
				width: 74px;
				min-width: 74px;
				border: 1px solid rgba($primary, 0.3);
				border-radius: 25px;
				display: inline-flex;
				justify-content: center;
				align-items: center;
				font-size: 1.625rem;
				color: $primary;
				&.WpcHasNotification {
					position: relative;
					&:before {
						content: '';
						position: absolute;
						top: 0;
						right: 0;
						height: 15px;
						width: 15px;
						background: $success;
						border-radius: 10px;
					}
				}
			}
			.WpcProfileControl {
				&Button {
					display: flex;
					align-items: center;
					position: relative;
					padding: 11px;
					padding-right: 50px;
					border: none;
					cursor: pointer;
					border: 1px solid rgba($primary, 0.3);
					border-radius: 25px;
					.WpcProfileImage {
						height: 50px;
						width: 50px;
						min-width: 50px;
						display: flex;
						border-radius: 18px;
						background: rgba($primary, 0.3);
						img {
							display: inline-flex;
							padding: 1px;
							width: 100%;
							height: 100%;
							border-radius: 16px;
						}
					}
					.WpcProfileDetails {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
						margin-left: 15px;
						.WpcUserName {
							font-size: 1.25rem;
							font-weight: 400;
							color: $dark;
              @include respond-below(sm) {
                display: none;
              }
						}
					}
					&:after {
						font-family: 'Wpc-icon';
						content: '\e90f';
						position: absolute;
						border: none !important;
						top: 50%;
						right: 20px;
						transform: translateY(-50%);
						font-size: 0.75rem;
						color: $primary;
					}
				}
			}
		}
	}
}

// profile control dropdown
.WpcProfileDropdown {
	padding: 20px;
	border: none;
	min-width: 200px !important;
	max-width: 250px !important;
	box-shadow: 0 10px 50px rgba($dark, 0.07);
	margin-top: 5px;
	border-radius: 12px;
	ul {
		li {
			a,
			button {
				display: flex;
				line-height: 1;
				padding: 0;
				align-items: center;
				cursor: pointer;
				.WpcNavigationIcon {
					width: 40px;
					min-width: 40px;
					height: 40px;
					border: 1px solid rgba($dark, 0.1);
					border-radius: 12px;
					font-size: 0.875rem;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					color: $primary;
					@include transition($duration: 0.2s);
					@include transition-properties(background, color, border);
				}
				.WpcNavigationText {
					margin-left: 15px;
					color: $secondary;
					font-size: 1.125rem;
					line-height: 1;
					@include transition($duration: 0.2s);
					@include transition-properties(background, color, border);
				}
				&.Active,
				&:hover {
					.WpcNavigationIcon {
						background: $primary;
						border-color: $primary;
						color: $white;
					}
					.WpcNavigationText {
						color: $primary;
					}
				}
			}
			&:not(:last-child) {
				margin-bottom: 15px;
			}
		}
	}
}
.WpcNotificationDropdown {
	padding: 20px;
	border: none;
	min-width: 300px !important;
	box-shadow: 0 10px 50px rgba($dark, 0.07);
	margin-top: 5px;
	border-radius: 12px;
	ul {
		li {
			.WpcNotification {
				display: flex;
				flex-direction: column;
				padding: 0;
				.WpcNotificationTitle {
					font-size: 1rem;
					line-height: 1;
					color: $dark;
					&:not(:last-child) {
						margin-bottom: 15px;
					}
				}
				.WpcNotificationTime {
					font-size: 0.8275rem;
					color: $secondary;
				}
			}
			&:not(:first-child) {
				padding-top: 15px;
			}
			&:not(:last-child) {
				padding-bottom: 15px;
				border-bottom: 1px solid rgba($light, 0.6);
			}
		}
	}
}

.dropdown {
	.dropdown-menu-middle {
		left: 50% !important;
		transform: translateX(-50%) !important;
		top: 100% !important;
	}
}

.WpcToggleDropdown {
	.dropdown-menu {
		padding: 10px;
		border: none;
		min-width: 120px !important;
		max-width: 150px !important;
		box-shadow: 0 10px 50px rgba($dark, 0.07);
		margin-top: 5px;
		ul {
			li {
				a,
				button {
					display: flex;
					line-height: 1;
					padding: 0;
					align-items: center;
					cursor: pointer;
					.WpcNavigationIcon {
						width: 24px;
						min-width: 24px;
						height: 24px;
						border: 1px solid rgba($secondary, 0.2);
						border-radius: 5px;
						font-size: 0.75rem;
						display: inline-flex;
						align-items: center;
						justify-content: center;
						color: $secondary;
						@include transition($duration: 0.2s);
						@include transition-properties(background, color, border);
					}
					.WpcNavigationText {
						margin-left: 15px;
						color: $secondary;
						font-size: 0.9375rem;
						line-height: 1.3;
						@include transition($duration: 0.2s);
						@include transition-properties(background, color, border);
					}
					&.Active,
					&:hover {
						.WpcNavigationIcon {
							background: $primary;
							border-color: $primary;
							color: $white;
						}
						.WpcNavigationText {
							color: $primary;
						}
					}
				}
				&:not(:last-child) {
					margin-bottom: 10px;
				}
			}
		}
	}
}

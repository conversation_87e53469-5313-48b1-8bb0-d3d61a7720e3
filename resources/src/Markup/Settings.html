<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>WPCafe</title>
		<link rel="stylesheet" href="assets/css/bootstrap/bootstrap.min.css" />
		<link rel="stylesheet" href="assets/css/wpc-icon/style.css" />
		<link rel="stylesheet" href="../../../public/assets/css/style.min.css" />
	</head>
	<body>
		<div class="WpcPageBody">
			<!-- sidebar start -->
			<section class="WpcSidebar">
				<div class="WpcSidebarContent">
					<div class="WpcSidebarContentTop">
						<a href="#" class="WpcSiteLogo">
							<h2 class="WpcSiteTitle">WPCafe</h2>
						</a>
						<a href="#" class="WpcSiteLogoCollapsed">
							<h2 class="WpcSiteTitle">WPC</h2>
						</a>
					</div>
					<nav class="WpcSidebarContentMiddle">
						<ul class="WpcSidebarNav">
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink Active">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-speedometer"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Dashboard</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-user"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Employees</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-list"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Coffee Menu</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-order"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Order History</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-settings"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Settings</div>
								</a>
							</li>
						</ul>
					</nav>
				</div>
			</section>
			<!-- sidebar end -->

			<main class="WpcContentArea">
				<!-- site header -->
				<header class="WpcContentAreaHeader">
					<div class="WpcHeaderWrapper">
						<div
							class="
								WpcContentAreaHeaderRight
								d-flex
								align-items-center
								ml-auto
							"
						>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcNotificationIcon WpcHasNotification mr-4"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<span class="Icon">
										<i class="wpc-icon wpc-notification"></i>
									</span>
								</button>
								<div
									class="
										dropdown-menu dropdown-menu-middle
										WpcNotificationDropdown
									"
								>
									<ul>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
									</ul>
								</div>
							</div>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcProfileControlButton"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<div class="WpcProfileImage">
										<img
											src="assets/img/bruce-mars-AndE50aaHn4-unsplash%201.png"
											alt=""
										/>
									</div>
									<div class="WpcProfileDetails">
										<h4 class="WpcUserName">Jhonathan Doe</h4>
									</div>
								</button>
								<div
									class="dropdown-menu dropdown-menu-right WpcProfileDropdown"
								>
									<ul>
										<li>
											<a href="#">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-settings"></i>
												</span>
												<p class="WpcNavigationText">Settings</p>
											</a>
										</li>
										<li>
											<a href="#">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-logout"></i>
												</span>
												<p class="WpcNavigationText">Logout</p>
											</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</header>
				<div class="WpcContentAreaBody">
          <ul class="nav mb-4">
            <li class="mr-3">
              <button class="WpcTabButton active" data-bs-toggle="pill" data-bs-target="#pills-home"><span class="Text">Manage Coupon</span></button>
            </li>
            <li>
              <button class="WpcTabButton" data-bs-toggle="pill" data-bs-target="#pills-profile"><span class="Text">Manage Coffee</span></button>
            </li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane fade show active" id="pills-home" >
              <section class="WpcAnalyticsSection">
                <div class="WpcSectionTitleWrap">
                  <h4 class="WpcSectionTitle">Manage Coupon</h4>
                </div>
                <form>
                  <div class="row">
                    <div class="col-xl-7 col-lg-8">
                      <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Duration</label>
                        <input type="text" class="form-control" />
                      </div>
                      <div class="WpcFormGroup">
                        <label class="WpcFormLabel">Expiry Date</label>
                        <input type="email" class="form-control" />
                      </div>
                      <div class="WpcFormGroup">
                        <div class="WpcButtonGroup">
                          <div class="WpcFilterSelectorWrapper">
                            <label>Type</label>
                            <select class="WpcFilterSelector">
                              <option selected>Expresso</option>
                              <option value="1">Latte</option>
                              <option value="2">Cappuccino</option>
                              <option value="3">Americano</option>
                              <option value="3">Mocha</option>
                            </select>
                          </div>
                          <div class="WpcIncDecButtonGroup">
                            <button class="WpcDecButton">
                              <i class="wpc-icon wpc-minus"></i>
                            </button>
                            <input class="WpcIncDecInput" type="text" value="0" />
                            <button class="WpcIncButton">
                              <i class="wpc-icon wpc-plus"></i>
                            </button>
                          </div>
                          <button class="WpcButton ml-auto">
                            <div class="Text">Add Coffee</div>
                          </button>
                        </div>
                      </div>
                      <div class="WpcFormGroup">
                        <div class="WpcCoffeePreviewWrapper">
                          <div class="WpcCoffeePreview">
                            <div
                              class="WpcCoffeePreviewImage"
                              style="background-image: url('assets/img/coffee1.png')"
                            ></div>
                            <p class="WpcCoffeePreviewDetails">Expresso : 7</p>
                          </div>
                          <div class="WpcCoffeePreview">
                            <div
                              class="WpcCoffeePreviewImage"
                              style="background-image: url('assets/img/coffee2.png')"
                            ></div>
                            <p class="WpcCoffeePreviewDetails">Expresso : 7</p>
                          </div>
                          <div class="WpcCoffeePreview">
                            <div
                              class="WpcCoffeePreviewImage"
                              style="background-image: url('assets/img/coffee3.png')"
                            ></div>
                            <p class="WpcCoffeePreviewDetails">Expresso : 7</p>
                          </div>
                          <div class="WpcCoffeePreview">
                            <div
                              class="WpcCoffeePreviewImage"
                              style="background-image: url('assets/img/coffee4.png')"
                            ></div>
                            <p class="WpcCoffeePreviewDetails">Expresso : 7</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="WpcFormGroup align-items-baseline">
                    <button class="WpcButton WpcFilled mt-4">
                      <span class="Text">Save Changes</span>
                    </button>
                  </div>
                </form>
              </section>
            </div>
            <div class="tab-pane fade" id="pills-profile" >
              <div class="WpcSectionTitleWrap">
                <h4 class="WpcSectionTitle mr-2">Manage Coffee</h4>
              </div>
              <div class="WpcInfoBoxWrapper">
                <div class="row WpcHasColGap">
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee1.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee2.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee3.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee4.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee1.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee2.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcEditCoffeeModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                  <div class="col-xl-6">
                    <div class="WpcInfoBox">
                      <div
                        class="WpcInfoBoxImage"
                        style="background-image: url('assets/img/coffee3.png')"
                      ></div>
                      <div class="WpcInfoBoxDetails">
                        <div class="dropdown WpcToggleDropdown">
                          <button
                            class=""
                            type="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          >
                            <i class="wpc-icon wpc-dots-vertical"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-middle">
                            <ul>
                              <li>
                                <button>
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-edit"></i>
                                  </span>
                                  <p class="WpcNavigationText">Edit</p>
                                </button>
                              </li>
                              <li>
                                <button
                                  data-toggle="modal"
                                  data-target=".WpcDeleteConfirmationModal"
                                >
                                  <span class="WpcNavigationIcon">
                                    <i class="wpc-icon wpc-delete"></i>
                                  </span>
                                  <p class="WpcNavigationText">Delete</p>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <h3 class="WpcInfoBoxTitle">Mocha</h3>
                        <p class="WpcPostGridContent">
                          Milk Foam, Steamed Milk, HotChocolate, Espresso
                        </p>
                      </div>
                      <span class="WpcAvailableCount">2</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
				</div>
			</main>
		</div>
		<script src="assets/js/jquery/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-gtEjrD/SeCtmISkJkNUaaKMoLD0//ElJ19smozuHV6z3Iehds+3Ulb9Bn9Plx0x4" crossorigin="anonymous"></script>
		<script src="assets/js/custom.js"></script>
	</body>
</html>

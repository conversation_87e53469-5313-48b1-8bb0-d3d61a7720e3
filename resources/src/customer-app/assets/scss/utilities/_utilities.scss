
body {
	background: #f3f3f3;
}

.wrapper {
	height: 100vh;
	overflow: hidden;
	position: relative;
	max-width: 550px;
	margin: 0 auto;
}

.wpc-body {
	overflow-y: auto;
	overflow-x: hidden;
	height: 100vh;
	overflow-y: auto;
	display: flex;
	flex-direction: column;
	max-width: 550px;
	margin: 0 auto;
	background: #0c0f14;
}

.mt30 {
	margin-top: 30px;
}

.footer {
	background: $sidebar;
	padding: 15px;
	text-align: center;
	margin-top: auto;
	p {
		color: $white;
		font-size: 14px;
		font-weight: 400;
	}
	a {
		color: $white;
	}
}

.bgi__property {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.wpc-checkbox input {
	display: none;
 }
 .wpc-checkbox input:checked ~ .check__box:before {
	background: #d17842;
 }
 .wpc-checkbox {
	display: flex;
	align-items: center;
 }
 .wpc-checkbox .check__box {
	position: relative;
	height: 16px;
	width: 16px;
	margin-right: 8px;
	display: inline-block;
 }
 .wpc-checkbox .check__box:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	height: 16px;
	width: 16px;
	border-radius: 3px;
	border: 1px solid rgba(82, 85, 90, 0.15);
	background: #fff;
 }
 .wpc-checkbox .check__box:after {
	content: '\e906';
	font-family: 'wpc-icon';
	position: absolute;
	top: 2px;
	left: 3px;
	font-size: 10px;
	color: #fff;
 }
 .wpc-checkbox .text {
	color: #fff;
 }
 
// import files
@import '../mixins/background&color';

// each function for content color
@each $parent, $color in $template-colors {
	@include color-emphasis-variant('.WpcColor#{$parent}', $color);
}

// each function for background color
@each $parent, $color in $template-colors {
	@include background-emphasis-variant('.WpcBackground#{$parent}', $color);
}

// each function for background color
@each $parent, $color in $template-colors {
	@include gradient-emphasis-variant('.Gradient#{$parent}', $color);
}

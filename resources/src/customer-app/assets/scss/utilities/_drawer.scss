.wpc-navigation__drawer__wrap {
    background: $accent;
    position: absolute;
    top: 0;
    left: -100vw;
    width: 100%;
    height: 100%;
    z-index: 99;
    transition: all .3s ease;
    &.show {
        left: 0;
    }
}

.wpc-navigation__drawer {
    padding: 20px 25px 40px;
    display: flex;
    flex-direction: column;
    height: 100%;
    .navigation__drawer__closer {
        margin-bottom: 30px;
        display: inline-block;
        color: $white;
    }
    .navigation__drawer__header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 70px;
        .navigation__drawer__user {
            display: flex;
            align-items: center;
            &__thumb {
                width: 50px;
                height: 50px;
                min-width: 50px;
                margin-right: 15px;
                border-radius: 12px;
            }
            &__body {
                .user__name {
                    font-size: 18px;
                    font-weight: 700;
                    color: $white;
                }
                .designation {
                    font-size: 13px;
                    font-weight: 400;
                    color: $grey;
                }
            }
            &__edit {
                a {
                    height: 40px;
                    width: 40px;
                    border-radius: 10px;
                    border: 1px solid rgba($secondary, .3);
                    line-height: 40px;
                    text-align: center;
                    display: inline-block;
                    color: $primary;
                }
            }
        }

    }
    .navigation__menu {
        .navigation__list {
            padding: 15px 0;
            display: flex;
            &:not(:last-child) {
                border-bottom: 1px solid rgba($secondary, .1);
            }
            .icon {
                height: 40px;
                width: 40px;
                line-height: 40px;
                text-align: center;
                border: 1px solid rgba($secondary, .1);
                color: $primary;
                margin-right: 10px;
                display: inline-block;
                border-radius: 12px;
                background-color: #242c39;
            }
            .text {
                font-size: 18px;
                font-weight: 400;
                color: $white;
                span {
                    color: $primary;
                }
            }
        }
    }
}
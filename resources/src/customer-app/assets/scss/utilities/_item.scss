

.item__wrap {
    display: flex;
    padding: 0 15px;
    overflow-x: auto;
    &.flex__wrap {
        flex-wrap: wrap;
        .item:nth-child(2n) {
            margin-right: 0;
        }
        .item {
            margin-bottom: 15px;
        }
    }
}

.item {
    flex: 0 0 calc(50% - 7.5px);
    margin-right: 15px;
    border-radius: 20px;
    overflow: hidden;
    background-color: $accent;
    padding: 15px;
    &:last-child {
        margin-right: 0;
    }
    .item__thumb {
        padding-top: 100%;
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        span {
            position: absolute;
            top: 28px;
            left: -60px;
            transform: rotate(-45deg);
            background: $primary;
            color: $white;
            font-size: 11px;
            padding: 5px 15px;
            width: 200px;
            text-align: center;
            text-transform: uppercase;
            line-height: 1;
        }
        .item__available__count {
            background-color: rgba($accent, .8);
            color: $white;
            font-weight: 600;
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            padding: 6px 12px;
            border-bottom-left-radius: 20px;
            .wpc-icon {
                color: $primary;
                margin-right: 5px;
            }
        }
    }
    .item__body {
        padding: 20px 10px 5px;
        text-align: center;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        .item__name {
            font-size: 18px;
            color: #eeecec;
            font-weight: 500;
            letter-spacing: 0.035rem;
        }
        p {
            font-size: 14px;
            font-weight: 400;
            color: $grey;
            margin-top: 5px;
            span {
                color: $grey;
            }
        }
        h5 {
            border: 1px solid $primary;
            border-radius: 8px;
            padding: 10px;
            color: $primary;
            font-weight: 400;
            margin: 10px 0 5px;
            font-size: 14px;
            letter-spacing: .05rem;
        }
        .item__not__available{
            border-color: $grey;
            color: $grey;
        }
    }
    &--md {
        flex: 0 0 calc(40% - 10px);
        margin-right: 10px;
        border-radius: 18px;
        .item__body {
            padding: 10px;
            border-bottom-right-radius: 18px;
            border-bottom-left-radius: 18px;
            .item__name {
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

.coffee__convertion {
    padding: 15px;
    max-width: 400px;
    margin: 0 auto;
    .switch__sec {
        margin-bottom: 20px;
        span {
            text-align: center;
            display: block;
        }
        img {
            height: 40px;
            margin: 0 auto;
        }
    }
}
.convertion {
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    .convertion__header {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 20px;
        p {
            flex: 0 0 60px;
            color: $white;
        }
        .WpcFilterSelectorWrapper {
            flex: 1 1 auto;
            margin-right: 0;
            .WpcFilterSelector {
                width: 100%;
            }
        }
        .WpcButton {

            min-height: 40px;
            width: auto;
            padding: 1px 25px;
        }
    }
    .selected__item {
        .item {
            flex: 0 0 100%;
            display: block;
            display: flex;
            margin-right: 0;
            .item__thumb {
                flex: 0 0 64px;
                width: 64px;
                padding-top: 0;
                height: 64px;
                border-radius: 12px;
            }
            .item__body {
                padding: 0 0 0 15px;
                flex: 1 1 auto;
                h3 {
                    font-size: 16px;
                    text-align: left;
                }
                .item__body__bottom {
                    display: flex;
                    width: 100%;
                    margin-top: 10px;
                    justify-content: space-between;
                }
                .item__available__count {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 15px;
                    background: #1e222a;
                    border: 1px solid rgba($secondary, 0.2);
                    align-items: center;
                    height: 35px;
                    width: 45px;
                    border-radius: 12px;
                    .icon {
                        font-size: 14px;
                        margin-right: 5px;
                        i {
                            color: $primary;
                        }
                    }
                    p {
                        margin-top: 0;
                    }
                }
            }
        }
    }
}

.WpcFilterSelectorWrapper {
	display: inline-flex;
	align-items: center;
	position: relative;
    margin-right: 20px;
	.WpcFilterSelector {
		height: 40px;
		padding: 0 45px 0 20px;
		background: #1e222a;
		border: 1px solid rgba($secondary, 0.2);
		border-radius: 12px;
		font-size: 14px;
		line-height: 1;
		color: $white;
		-webkit-appearance: none;
	}
	&:before {
		font-family: 'Wpc-icon';
		content: '\e90f';
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
		font-size: 0.6875rem;
		color: rgba($secondary, 0.2);
	}
}

.WpcIncDecButtonGroup {
	display: inline-flex;
	align-items: center;
	.WpcIncDecInput::-webkit-outer-spin-button,
    .WpcIncDecInput::-webkit-inner-spin-button {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: textfield;
    } 
	.WpcIncDecInput,
	.WpcIncButton,
	.WpcDecButton {
		height: 35px;
		width: 35px;
		min-width: 35px;
		border: 1px solid rgba($secondary, 0.2);
		border-radius: 10px;
		padding: 2px;
		display: inline-flex;
		font-size: 14px;
		align-items: center;
		justify-content: center;
		text-align: center;
		color: $white;
        background: #1e222a;
		&:not(:last-child) {
			margin-right: 10px;
		}
	}
	.WpcIncButton,
	.WpcDecButton {
		font-size: 0.75rem;
		color: $secondary;
	}
	&:not(:last-child) {
		margin-right: 10px;
	}
}
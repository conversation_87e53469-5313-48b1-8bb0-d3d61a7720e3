
.wpc__item__modal {
    &.show {
        background: rgba($overlayColor, .7);
    }
    .modal-dialog {
        min-width: 290px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) !important;
        margin: 0;
        .modal-content {
            background: transparent;
        }
    }
    .modal-body {
        padding: 0;
        .item {
            border-radius: 20px;
            overflow: hidden;
            background: $accent;
            .item__body {
                padding: 20px 18px 30px;
                .item__name {
                    font-size: 24px;
                }
                p {
                    font-size: 18px;
                }
                .WpcButton {
                    margin-top: 20px;
                }
            }
        }
    }
}

.wpc-item__details {
    background: $sidebar;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    opacity: 0;
    overflow: auto;
    display: none;
    &.show {
        display: block;
        opacity: 1;
    }
    .item__thumb {
        width: 100%;
        height: 50%;
        max-height: 450px;
        position: relative;
        border-bottom-right-radius: 25px;
        border-bottom-left-radius: 25px;
        overflow: hidden;
        > img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }
        .item__fav,
        .item__back {
            position: absolute;
            top: 25px;
            height: 45px;
            width: 45px;
            border-radius: 12px;
            background: $sidebar;
            color: rgba($white, .5);
            line-height: 45px;
            text-align: center;
        }
        .item__fav {
            right: 25px;
        }
        .item__back {
            left: 25px;
        }
        .item__info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba($sidebar, .5);
            backdrop-filter: blur(20px);
            padding: 15px 25px;
            .left__block {
                flex: 1 1 auto;
                margin-right: auto;
                h3 {
                    font-size: 22px;
                    font-weight: 700;
                    color: #e4e2e1;
                    margin-bottom: 5px;
                    @media all and (max-width: 340px) {
                        font-size: 22px;
                    }
                }
                .item__elem {
                    font-size: 14px;
                    font-weight: 400;
                    color: $grey;
                    margin-bottom: 10px;
                    @media all and (max-width: 340px) {
                        font-size: 16px;
                    }
                }
                .token__available {
                    font-size: 14px;
                    font-weight: 400;
                    color: $white;
                    @media all and (max-width: 340px) {
                        font-size: 14px;
                    }
                    i {
                        color: $primary;
                        margin-right: 5px;
                    }
                }
            }
            .making__process {
                display: inline-flex;
                flex-wrap: wrap;
                flex: 0 1 auto;
                justify-content: flex-end;
                .making__elem {
                    flex: 0 0 auto;
                    min-width: 45px;
                    margin-top: 5px;
                    margin-bottom: 5px;
                    margin-left: 10px;
                    border-radius: 15px;
                    background: $sidebar;
                    height: 45px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    padding: 2px 6px;
                    &:last-child {
                        margin-right: 0;
                    }
                    i {
                        color: $primary;
                        font-size: 14px;
                    }
                    span {
                        color: rgba($white, .5);
                        font-size: 12px;
                        font-weight: 400;
                        margin-top: 3px;
                    }
                    p {
                        color: rgba($white, .5);
                        font-size: 12px;
                        font-weight: 400;
                    }
                }
            }
        }
    }
    .item__body {
        padding: 25px 20px;
        flex: 1 0 auto;
        display: flex;
        flex-direction: column;
        .title {
            font-size: 15px;
            font-weight: 400;
            color: #828284;
            margin-bottom: 10px;
        }
        .description {
            color: $light;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.7;
            letter-spacing: 0.035rem;
            margin-bottom: 30px;
            .read__more {
                color: $primary;
            }
        }
        .item__instruction {
            margin-bottom: 20px;
            textarea {
                padding: 12px 15px;
                background: #141921;
                width: 100%;
                height: 80px;
                border-radius: 10px;
                resize: none;
                font-size: 15px;
                color: #e4e2e1;
                border: 1px solid rgba(82, 85, 90, .15);
                &:focus {
                    border-color: rgba(82, 85, 90, .75);
                    box-shadow: none;
                }
            }
        }
        .order__item {
            background: $primary;
            color: $white;
            font-size: 20px;
            font-weight: 700;
            padding: 15px;
            margin-top: auto;
        }
    }
}

.item__sc {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}


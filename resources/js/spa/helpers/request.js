import axios from "axios";
import { getToken } from './auth'
import app from "../app";


const BASE_URL = process.env.MIX_API_BASE_URL || "http://wpcafe.test/api/v1";

export const request = axios.create({
    baseURL: BASE_URL
});

request.interceptors.request.use((config) => {
    const TOKEN = getToken() || null;

    config.headers = {
        "Accept": 'application/json',
        "Authorization": `Bearer ${TOKEN}`,
    };

    return config;
});

request.interceptors.response.use(function (response) {
    return response;
}, function (error) {
    if (error.response.status === 401) {
        app.LOGOUT();
        app.$router.push({name: 'login'});
    }
    return Promise.reject(error);
});

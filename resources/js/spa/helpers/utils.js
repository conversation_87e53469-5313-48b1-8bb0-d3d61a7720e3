/**
 * Utility helper functions for the application
 */

/**
 * Safely encode a URL to handle special characters in filenames
 * This is more thorough than encodeURI as it handles all special characters
 * that might cause issues in CSS url() functions
 * @param {string} url - The URL to encode
 * @returns {string} - The encoded URL
 */
export function escapeCssUrl(url) {
  if (!url) return '';
  return url.replace(/[\s"'(){}[\]|\\^`<>#%]/g, function (char) {
    return '%' + char.charCodeAt(0).toString(16).toUpperCase();
  });
}

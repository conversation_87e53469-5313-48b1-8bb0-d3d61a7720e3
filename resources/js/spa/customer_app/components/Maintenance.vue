<template>
  <div class="container maintenance-container">
    <div>
      <div class="card border-secondary text-secondary maintenance-card">
        <div class="maintenance-card-body card-body">
          <h4 class="card-title maintenance-card-title">The coffee service is under maintenance.</h4>
          <h6 class="card-subtitle maintenance-card-subtitle mb-2">You'll be informed as soon as service will be ready. Meantime you can try instant tea & coffee.</h6>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Maintenance",
};
</script>

<style>
.maintenance-container {
  margin: 20px 0px 0px 0px;
}

.maintenance-card {
  margin-bottom: 2rem;
  background: #1e222a;
  /* height: 170px */
}

.maintenance-card-title {
  text-align: center;
  color: white;
  font-weight: 700;
}

.maintenance-card-subtitle {
  text-align: center;
  color: white;
  font-weight: 700;
  padding: inherit;
}

.maintenance-card-body {
  box-shadow: inset 0 0 0 1000px rgb(16 20 25 / 65%);
  background-image: url(/CustomerApp/assets/img/maintenance.jpeg);
  background-size: cover;
  /* height: 82px; */
}
</style>

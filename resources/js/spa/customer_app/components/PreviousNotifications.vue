<template>
    <div>
        <div class="section__header mt30">
            <h3 class="section__header__title">Previous Notifications</h3>
        </div>

        <div class="wpc-notificatons pb-4">
            <div class="accordion" id="accordionExample2">
                <div class="notification__item" v-for="(prev,index) in previousNotifications.data" :key="index">

                    <div v-if="prev.action_type === 'GIFT'" class="card">
                        <div class="card-header" :id="'prev-heading'+index">
                            <h2 class="mb-0">
                                <button class="btn btn-link btn-block text-left collapsed" type="button"
                                        data-toggle="collapse"
                                        :data-target="'#prev-collapse'+index" aria-expanded="true"
                                        :aria-controls="'prev-collapse'+index">
                                    {{ prev.message }}
                                    <span><i class="wpc-icon wpc-arrow-down"></i></span>
                                </button>
                            </h2>
                        </div>
                        <div :id="'prev-collapse'+index" class="collapse" :aria-labelledby="'prev-heading'+index"
                             data-parent="#accordionExample">
                            <div class="card-body">
                                <p v-html="prev.new_message"></p>
                            </div>
                        </div>
                    </div>

                    <div v-else class="card">
                        <div class="card-header" id="headingOne1">
                            <h2 class="mb-0">
                                <button class="btn btn-link btn-block text-left collapsed" type="button"
                                        data-toggle="collapse"
                                        :data-target="'collapseOne1'+index" aria-expanded="true"
                                        aria-controls="collapseOne1">
                                    {{ prev.new_message }}
                                </button>
                            </h2>
                        </div>

                        <div :id="'collapseOne1'+index" class="collapse" aria-labelledby="headingOne1"
                             data-parent="accordionExample2">
                        </div>
                    </div>
                  <div class="notification__date__time">
                    <p class="notification__time">{{ notificationTime(prev.created_at) }}</p>
                    <p class="notification__time">{{ notificationDateTime(prev.created_at) }}</p>
                  </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="w-50 mx-auto">
                <nav class="WpcPaginationWrapper">
                    <ul class="pagination">
                        <pagination :data="previousNotifications" :limit="3" @pagination-change-page="list" />
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import pagination from 'laravel-vue-pagination'
import {request} from "../../helpers/request";

export default {
    name: "PreviousNotifications",
    components: {
        pagination
    },

    data() {
        return {
            previousNotifications: {},
        }
    },
    mounted: function () {
        this.list();
    },
    methods: {
        notificationTime(date) {
            return moment(date).fromNow();
        },
        notificationDateTime(date) {
          return moment(date).format("YYYY-MM-DD hh:mm:ss A");
        },

        async list(page = 1) {
            let loader = this.$loading.show({
                loader: 'spinner',
                container: document.getElementById("notify"),
                canCancel: true,
                onCancel: this.onCancel,
                color: '#d17842',
                backgroundColor: '#000000',
                height: 80,
                width: 80
            });
            await request.get(`/prev-notifications?page=${page}`).then(({data: {data}}) => {
                this.previousNotifications = data;
                loader.hide();
            }).catch(({response}) => {
                console.error(response)
            })
        }
    }
}
</script>

<style scoped>
.wpc-notificatons .notification__item .notification__date__time {
  display: flex;
  justify-content: space-between;
}
</style>

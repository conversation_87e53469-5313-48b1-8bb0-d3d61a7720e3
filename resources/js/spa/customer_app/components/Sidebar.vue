<template>
  <div>
    <div class="wpc-navigation__drawer__wrap" v-bind:class="{ show: sidebar }">
      <div class="wpc-navigation__drawer">
        <a
          href="#"
          @click.prevent="sidebarClose(false)"
          class="navigation__drawer__closer"
        >
          <i class="wpc-icon wpc-times"></i>
        </a>
        <div class="navigation__drawer__header">
          <div class="navigation__drawer__user">
            <div
              class="navigation__drawer__user__thumb bgi__property"
              :style="{ backgroundImage: 'url(' + escapeCssUrl(getUser.avatar) + ')' }"
            ></div>
            <div class="navigation__drawer__user__body">
              <h4 class="user__name">{{ getUser.name }}</h4>
              <p class="designation">{{ getUser.designation }}</p>
            </div>
          </div>
          <div class="navigation__drawer__user__edit">
            <router-link to="/profile"
              ><i class="wpc-icon wpc-edit"></i
            ></router-link>
          </div>
        </div>
        <ul class="navigation__menu">
          <li class="navigation__list">
            <router-link to="/notification" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-notification"></i>
              </span>
              <span class="text">Notifications</span>
            </router-link>
          </li>
          <li class="navigation__list">
            <router-link to="/coffee" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-menu"></i>
              </span>
              <span class="text">Coffee Menu</span>
            </router-link>
          </li>
          <li class="navigation__list">
            <router-link to="/coffee-conversion" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-exchange"></i>
              </span>
              <span class="text">Coffee Conversion</span>
            </router-link>
          </li>
          <li class="navigation__list">
            <router-link to="/coffee-gift" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-transfer"></i>
              </span>
              <span class="text">Coffee Transfer</span>
            </router-link>
          </li>
          <li class="navigation__list">
            <router-link to="/order-history" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-order"></i>
              </span>
              <span class="text">Request History</span>
            </router-link>
          </li>
          <li class="navigation__list">
            <router-link to="/order-analytics" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-menu"></i>
									</span>
              <span class="text">Order Analytics</span>
            </router-link>
          </li>

          <li class="navigation__list">
            <router-link
              :to="{ name: 'ChangePassword' }"
              class="navigation__item"
            >
              <span class="icon">
                <i class="wpc-icon wpc-settings"></i>
              </span>
              <span class="text">Change Password</span>
            </router-link>
          </li>

          <li
            class="navigation__list"
            v-if="getUser.role == 'barista' || getUser.role == 'admin'"
          >
            <router-link to="/barista" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-swap"></i>
              </span>
              <span class="text">Switch to Barista</span>
            </router-link>
          </li>
        </ul>
        <ul class="navigation__menu mt-auto">
          <li class="navigation__list">
            <router-link :to="{ name: 'Logout' }" class="navigation__item">
              <span class="icon">
                <i class="wpc-icon wpc-logout"></i>
              </span>
              <span class="text">Log Out</span>
            </router-link>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style>
  .wpc-navigation__drawer {
    overflow-y: auto;
  }
</style>

<script>
import { LOGOUT, TOGGLE_SIDEBAR } from "../../store/actions";
import { request } from "../../helpers/request";
import { mapActions, mapState } from "vuex";
import { escapeCssUrl } from "../../helpers/utils";

export default {
  name: "SidebarComponent",
  data() {
    return {
      userClear: null,
    };
  },
  computed: {
    ...mapState(["userSidebarVisible"]),
    ...mapState(["user"]),
    sidebar() {
      return this.userSidebarVisible;
    },
    getUser() {
      return this.user;
    },
  },
  methods: {
    escapeCssUrl,
    ...mapActions([TOGGLE_SIDEBAR, LOGOUT]),
    sidebarClose(data) {
      return this.TOGGLE_SIDEBAR(data);
    },
  },
};
</script>

<style scoped></style>

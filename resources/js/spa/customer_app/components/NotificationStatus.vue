<template>
    <div class="wpc__notifications__wrapper">
        <div class="section__header">
            <h3 class="section__header__title">Available Notifications</h3>
        </div>
        <div class="wpc__notifications__status">
          <div class="notification__status__card">
            <div class="card__top">
              <div class="icon">
                <i class="wpc-icon">
                  <img src="/assets/img/coffee-maintenance-white.svg"  alt="coffee-maintenance-icon"/>
                </i>
              </div>
              <div class="WpcFilterNotificationToggleWrapper">
                <label>
                  <input type="checkbox" name="maintenance_mode" :checked="maintenanceMode"
                         @change="maintenanceModeNotificationChanged($event.target.checked)">
                  <span></span>
                </label>
              </div>
            </div>
            <p>Maintenance mode</p>
          </div>
          <div class="notification__status__card">
            <div class="card__top">
              <div class="icon">
                <i class="wpc-icon wpc-coffee-cup"></i>
              </div>
              <div class="WpcFilterNotificationToggleWrapper">
                <label>
                  <input type="checkbox" :checked="orderStatus" name="order_status"
                         @change="orderStatusNotificationChanged($event.target.checked)">
                  <span></span>
                </label>
              </div>
            </div>
            <p>Order status</p>
          </div>
        </div>
    </div>
</template>

<script>


import {request} from "../../helpers/request";
import {mapState} from "vuex";

export default {
    name: "NotificationStatus",
    data() {
        return {
          maintenanceMode: false,
          orderStatus: false
        }
    },
  computed: {
    ...mapState(['user']),
  },
    mounted: function () {
      this.availableNotifications();
    },
    methods: {
      async availableNotifications() {
        await request.get(`/user-notification-channels`).then(({data: {data}}) => {
          this.maintenanceMode = data?.maintenance_mode ?? false;
          this.orderStatus = data?.order_status ?? false;
        }).catch(({response}) => {
          console.error(response)
        })
      },
      orderStatusNotificationChanged(isChecked) {
        request.post(`user-notification-channels/update`, {
          order_status: isChecked
        }).then(({ data: { data } }) => {
          this.$toastr.s("success", `Order status notification has been ${isChecked ? "enabled" : "disabled"}`);
        }).catch((error) => {
          console.error(error);
          this.$toastr.e("error", 'Failed to update status');
        });
      },

      maintenanceModeNotificationChanged(isChecked) {
        request.post(`user-notification-channels/update`, {
          maintenance_mode: isChecked
        }).then(({ data: { data } }) => {
          this.$toastr.s("success", `Maintenance mode notification has been ${isChecked ? "enabled" : "disabled"}`);

        }).catch((error) => {
          console.error(error);
          this.$toastr.e("error", 'Failed to update status');
        });
      },
    }
}
</script>

<style scoped>

.WpcFilterNotificationToggleWrapper label {
  position: relative;
}
.WpcFilterNotificationToggleWrapper label span {
  position: absolute;
  top: -5px;
  left: -27px;
  height: 15px;
  width: 25px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.9);
  content: "";
  cursor: pointer;
}
.WpcFilterNotificationToggleWrapper label span:after {
  position: absolute;
  top: 2px;
  left: 2px;
  height: 11px;
  width: 11px;
  background: #83858e;
  border-radius: 50%;
  content: "";
  transition: all 0.3s ease;
}

.WpcFilterNotificationToggleWrapper label input:checked+span:after {
  left: 12px;
  background: #16ad9f;
}

.WpcFilterNotificationToggleWrapper label input {
  display: none;
}
.wpc__notifications__wrapper {
  margin-bottom: 20px;
}
.wpc__notifications__status {
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
}
.wpc__notifications__status .notification__status__card {
  flex: 0 0 calc(50% - 7.5px);
  padding: 15px;
  background: rgba(30, 34, 42, 0.8);
  border-radius: 20px;
  transition: all 0.3s ease;
}
.wpc__notifications__status .notification__status__card .card__top {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;
}

.wpc__notifications__status .notification__status__card .card__top .icon {
  height: 54px;
  width: 54px;
  text-align: center;
  background: #d17842;
  border-radius: 15px;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.wpc__notifications__status .notification__status__card .card__top .icon i {
  color: #fff;
  line-height: 54px;
  font-size: 20px;
}

.wpc__notifications__status .notification__status__card p {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  transition: all 0.3s ease;
}
</style>

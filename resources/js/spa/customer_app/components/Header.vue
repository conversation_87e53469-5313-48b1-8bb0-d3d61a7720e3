<template>
    <div>
        <div class="header" style="background-image: url('/wpcafe-app-header-bg.jpg')">
            <div class="header__nav">
                <div class="nav__icon">
                    <a href="#" @click.prevent="sidebarOpen(true)"><i class="wpc-icon wpc-bar"></i></a>
                </div>
                <div class="notification__icon">
                    <router-link to="/notification"><i class="wpc-icon wpc-notification"></i></router-link>
                </div>
            </div>
            <div class="header__greeting">
                <p>{{ isMorning() }}</p>
                <span v-if="!is_available" class="barista_available_status"> Barista is offline</span>
                <span v-else class="barista_available_status isOnline"> Barista is Online </span>
                <h2>{{ user.name }}</h2>
            </div>
        </div>

    </div>
</template>

<script>
import {TOGGLE_SIDEBAR} from "../../store/actions"
import {mapActions, mapState} from "vuex";
import {request} from "../../helpers/request";

export default {
    name: "HeaderComponent",
    data() {
        return {
            message: 'Good Morning!',
            'is_available': ''
        }
    },

    mounted() {
        this.getBaristaStatus();
    },
    methods: {
        ...mapActions([TOGGLE_SIDEBAR]),
        sidebarOpen(data) {
            return this.TOGGLE_SIDEBAR(data)
        },

        getBaristaStatus() {
            request.get(`/barista-status`).then(({data: {data}}) => {
                this.is_available = data.barista_status;
            }).catch((error) => {
                console.log(error);
            })
        },

        isMorning() {
            let date = new Date();
            let time = date.getHours();

            if (time < 12) {
                return 'Good Morning'
            }
            if (time > 12 && time < 17) {
                return 'Good Afternoon'
            } else {
                return 'Good Evening'
            }
        }

    },
    computed: {
        ...mapState(["user"]),
    }
}
</script>

<style scoped>

</style>

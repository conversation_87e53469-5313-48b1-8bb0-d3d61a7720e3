<template>
  <div class="home__content" id="home-page">
    <CouponCounter/>
    <OrderTracking :maintenanceMode="maintenanceMode"/>
    <CoffeeTransferTracking :maintenanceMode="maintenanceMode"/>
    <MenuArea :maintenanceMode="maintenanceMode"/>
  </div>
</template>
<script>
import CouponCounter from "./CouponCounter";
import OrderTracking from "./OrderTracking";
import CoffeeTransferTracking from "./CoffeeTransferTracking";
import MenuArea from "./MenuArea"
import {UPDATE_CUSTOMER_COFFEE_COUNTER_STATE, UPDATE_CUSTOMER_PAGE_STATE} from "../../../store/actions";
import {mapActions} from "vuex";
import {request} from "../../../helpers/request";
import {mapState} from "vuex";
import login from "../auth/Login";

export default {
  name: "HomeComponent",
    data(){
        return {
            maintenanceMode : false
      }
    },
  components: {
    CouponCounter,
    OrderTracking,
    MenuArea,
    CoffeeTransferTracking
  },
  methods: {
    ...mapActions([UPDATE_CUSTOMER_PAGE_STATE]),
    ...mapActions([UPDATE_CUSTOMER_COFFEE_COUNTER_STATE]),
  },
  mounted() {

    let loader = this.$loading.show({
      loader: 'spinner',
      container: document.getElementById("home-page"),
      canCancel: true,
      onCancel: this.onCancel,
      color: '#d17842',
      backgroundColor: '#000000',
      height: 80,
      width: 80
    });

    request.get('/menu').then(({data}) => {
        this.maintenanceMode = data.is_maintenance;
        return data;
    })
    .then((data) => {
        this.UPDATE_CUSTOMER_PAGE_STATE(data.data);
    })
    .catch((error) => {
        console.log(error);
    });

    request.get('/coupons').then(({data: {data}}) => {
      this.UPDATE_CUSTOMER_COFFEE_COUNTER_STATE(data);
    }).catch((error) => {
      console.log(error);
    }).finally(() => {
      loader.hide();
    });
  },
}
</script>

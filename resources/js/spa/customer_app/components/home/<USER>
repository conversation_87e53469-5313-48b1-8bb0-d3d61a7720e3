<template>
  <div class="order__tracking__wrap" id="tracking">
    <div class="order__tracking__wrap">
      <div v-if="!maintenanceMode"  class="section__header mt30">
        <h2 class="section__header__title">Enjoy a coffee :) </h2>
        <router-link to="/coffee" class="section__header__icon">
          <i class="wpc-icon wpc-menu"></i>
        </router-link>
      </div>
    </div>

    <div class="order__tracking" v-for="(order,index) in pendingOrders">
      <div class="order__tracking__menu__wrapper">
        <div class="order__tracking__thumb bgi__property" :style="`background-image: url('${order.menu.thumbnail}')`">
        </div>
        <div class="order__tracking__menu__name">
          <h5>{{ order.menu.name }}</h5>
        </div>
      </div>
      <div class="order__tracking__body flex justify-content-between">
        <div class="flex justify-content-between flex-column">
          <h3>Request is {{ order.status }}</h3>
          <div v-if="order.status === 'pending'">
          <a href="#" v-if="defaultButtonCancel" class="cancel__order btn btn-sm btn-danger" @click="cancelOrder(order.id,order.menu_id,index)">Cancel</a>
          <a href="#" v-else-if="clickedButton === index" type="button" disabled class="cancel__order btn btn-sm btn-danger">
            <span class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>&nbsp;Cancel
          </a>
            <a href="#" v-else class="cancel__order btn btn-sm btn-danger" @click="cancelOrder(order.id,order.menu_id,index)">Cancel</a>
          </div>
            <p class="order__id font-weight-bold">Serial number :  #{{ order.serial_number }}</p>
            <p v-if="order.for_guest"><b>(For Guest)</b></p>
            <p v-if="order.is_espresso_double_shot" class="double-shot-badge">Espresso- Double Shots</p>
        <div class="customer__order__notes" v-if="order.note">
            <p>{{ order.note }}</p>
        </div>
        <p class="text-sm-right">{{ order.time_changed_format }}</p>
      </div>
    </div>
    </div>
  </div>
</template>
<script>
import {request} from "../../../helpers/request";
import {mapActions, mapState} from "vuex";
import {UPDATE_CUSTOMER_PAGE_STATE} from "../../../store/actions";

export default {


  data() {
    return {
      pendingOrders: [],
      loadCoupons: false,
      loadMenus: false,
      defaultButtonCancel: true,
      clickedButton: undefined,
    }
  },

  props : ['maintenanceMode'],

  computed: {
    ...mapState(['customerPageState', 'customerCoffeeCounterState']),
    menus() {
      return this.customerPageState.menus;
    }
  },
  methods: {
      ...mapActions([UPDATE_CUSTOMER_PAGE_STATE]),

    getPendingOrders() {
      request.get('/pending-orders').then((response) => {
        this.pendingOrders = response.data.data;
      }).catch((error) => {
        console.log(error);
      })
    },
    cancelOrder(id, menu_id,index) {
      this.clickedButton = index;
      this.defaultButtonCancel = false;
      let url = `/order/${id}/cancel`
      request.get(url).then((response) => {
        this.defaultButtonCancel = true
        this.clickedButton = undefined;
        this.getPendingOrders();

        let loader = this.$loading.show({
          loader: 'spinner',
          container: document.getElementById('tracking'),
          canCancel: true,
          onCancel: this.onCancel,
          color: '#d17842',
          backgroundColor: '#000000',
          height: 80,
          width: 80
        });

        request.get('/menu').then(({data: {data}}) => {
            this.UPDATE_CUSTOMER_PAGE_STATE(data);
        });

        request.get('/coupons').then(({data: {data}}) => {
          this.customerCoffeeCounterState.available = data.available;
          this.customerCoffeeCounterState.consumed = data.consumed;
        });

        loader.hide();
        this.$toastr.s("Success", response.data.message)
      }).catch((error) => {
        console.log(error);
      })
    },

    selectMenu(item) {
      this.selectedItem = item.is_active === 1 ? item : false;
    },

    modalClosed() {
      this.selectedItem = false;
    }
  },

  mounted: function () {
    this.getPendingOrders();
  },

}
</script>

<style scoped>
.double-shot-badge {
  display: inline-block;
  background-color: rgba(30, 34, 42, 0.8);
  color: white;
  font-size: 0.75rem;
  padding: 3px 6px;
  border-radius: 8px;
  vertical-align: middle;
  font-weight: 500;
  margin-top: 5px;
}
.customer__order__notes {
    padding: 15px 0;
    position: relative;
}

.order__tracking .order__tracking__body {
    position: static;
    flex: 1;
}
.order__tracking .order__tracking__body .cancel__order {
    top: 5px;
    right: 5px;
}
.customer__order__notes p {
    font-size: 14px;
    color: #f3f3f3;
    padding-left: 14px;
    font-style: italic;
    position: relative;
    font-weight: bolder;
    word-break: break-word;
}

.customer__order__notes p::before {
    content: "“";
    position: absolute;
    top: -8px;
    left: 0;
    font-size: 26px;
    color: #f3f3f3;
}
.time-note {
    margin-top: 12px;
}
@media all and (max-width: 450px) {
    .order__tracking .order__tracking__body h3 {
        font-size: 16px;
    }
    .order__tracking .order__tracking__body p {
        font-size: 13px;
    }
}
</style>

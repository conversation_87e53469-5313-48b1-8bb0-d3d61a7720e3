<template>
    <div class="pb-4">
        <div class="item__wrap flex__wrap">
          <maintenance v-if="maintenanceMode"/>
        </div>
      <div class="item__wrap flex__wrap">
            <router-link :to="{name:'order',params:{id:menu.id,coupon:menu.availableCoupon}}"
                         v-for="(menu, index) in menus"
                         :key="index"
                         :class="['item',{'is__disabled': maintenanceMode || !menu.is_active}]"
            >
                <div class="item__thumb bgi__property"
                     :style="{backgroundImage: 'url(' + escapeCssUrl(menu.thumbnail) + ')'}">
                    <div class="item__available__count">
                        <div class="icon"><i class="wpc-icon wpc-coffee-cup"></i>
                        </div>
                        <p>{{ menu.availableCoupon }}</p>
                    </div>
                  <div class="item__required__token__count">
                    <img src="/assets/img/coffee-coupon.svg"  alt="coffee-coupon-icon"/>
                    <p>{{ menu.required_coupon }}</p>
                  </div>
                </div>
                <div class="item__body">
                    <h3 class="item__name">{{ menu.name }}</h3>
                </div>
            </router-link>
        </div>
    </div>
</template>
<script>

import {mapState} from "vuex";
import Maintenance from "../Maintenance"
import { escapeCssUrl } from "../../../helpers/utils";

export default {
    data() {
        return {
            availableMenus: [],
            selectedItem: false,
        }
    },
    props: ['maintenanceMode'],
    methods: {
        getMenus() {
            this.$store.state.userSidebarVisible = false;
        },
      escapeCssUrl
    },
    computed: {
        ...mapState(['customerPageState']),
        menus() {
            return this.customerPageState[0]?.menus || [];
        }
    },
    components: {
        Maintenance,
    }
}
</script>

<style scoped>
  .item.is__disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  .item .item__thumb .item__required__token__count {
    background-color: rgba(30, 34, 42, 0.8);
    color: #fff;
    font-weight: 600;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    padding: 6px 12px;
    border-bottom-right-radius: 20px;
  }
  .item .item__thumb .item__required__token__count img {
    color: #d17842;
    margin-right: 5px;
  }
</style>

<template>
  <div class="coupon__statistic">
    <div class="statistic__card">
      <div class="card__top">
        <div class="icon">
          <i class="wpc-icon wpc-coffee-seeds"></i>
        </div>
        <h4>{{ availableCoffee }}</h4>
      </div>
      <p>Available Coffee</p>
    </div>
    <div class="statistic__card">
      <div class="card__top">
        <div class="icon">
          <i class="wpc-icon wpc-coffee-cup"></i>
        </div>
        <h4>{{ consumedCoffee }}</h4>
      </div>
      <p>Consumed Coffee</p>
    </div>
  </div>
</template>
<script>
import {mapState} from "vuex";

export default {
  computed: {
    ...mapState(['customerCoffeeCounterState']),
    availableCoffee() {
      return this.customerCoffeeCounterState.available;
    },
    consumedCoffee() {
      return this.customerCoffeeCounterState.consumed;
    }
  },
}
</script>

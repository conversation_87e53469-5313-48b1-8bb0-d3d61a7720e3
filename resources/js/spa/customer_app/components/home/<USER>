<template>
  <div v-if="pendingTransfers.length" class="order__tracking__wrap order__gift" id="tracking" style="margin-bottom: 25px">
    <div class="order__tracking" v-for="(transfer, index) in pendingTransfers">
      <div class="order__tracking__menu__wrapper">
        <div class="order__tracking__thumb bgi__property" :style="`background-image: url('${transfer.items.thumb}')`"></div>
        <div class="order__tracking__menu__name">
          <h5>{{ transfer.items.name }}</h5>
        </div>
      </div>
      <div class="order__tracking__body flex justify-content-between">
        <div class="flex justify-content-between flex-column">
          <h3>Gift is {{ transfer.status }}</h3>
          <div v-if="transfer.status === 'pending'" style="display: flex; overflow: hidden; white-space: nowrap">
            <a href="#" v-if="defaultButtonCancel" class="cancel__order btn btn-sm btn-danger" @click="cancelTransfer(transfer.id)">Reject</a>
            <a href="#" v-else-if="clickedButton === index" type="button" disabled class="cancel__order btn btn-sm btn-danger">
              <span class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>&nbsp;Reject
            </a>
            <a href="#" v-else class="cancel__order btn btn-sm btn-danger" @click="cancelTransfer(transfer.id)">Reject</a>
            <a href="#" v-if="defaultButtonAccept" class="accept__btn btn btn-sm btn-success" @click="acceptTransfer(transfer.id, index)">Accept</a>
            <a href="#" v-else-if="clickedButtonAccept === index" type="button" disabled class="accept__btn btn btn-sm btn-success">
              <span class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>
              Accept
            </a>
            <a href="#" v-else class="accept__btn btn btn-sm btn-success" @click="acceptTransfer(transfer.id, index)">Accept</a>
          </div>
          <div class="customer__order__notes">
            <p>{{ transfer.message }}</p>
          </div>
          <p class="text-sm-right">{{ transfer.time_changed_format }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { request } from "../../../helpers/request";
import { mapActions, mapState } from "vuex";
import { UPDATE_CUSTOMER_PAGE_STATE } from "../../../store/actions";

export default {
  name: "CoffeeTransferTracking",

  data() {
    return {
      pendingTransfers: [],
      defaultButtonCancel: true,
      defaultButtonAccept: true,
      clickedButton: undefined,
      clickedButtonAccept: undefined,
    };
  },

  props: ["maintenanceMode"],

  computed: {
    ...mapState(["customerPageState", "customerCoffeeCounterState"]),
    menus() {
      return this.customerPageState.menus;
    },
  },
  methods: {
    ...mapActions([UPDATE_CUSTOMER_PAGE_STATE]),

    getPendingOrders() {
      request
        .get("/coffee/transfer/pending")
        .then((response) => {
          this.pendingTransfers = response.data.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    cancelTransfer(id, menu_id, index) {
      this.clickedButton = index;
      this.defaultButtonCancel = false;
      let url = `/coffee/transfer/${id}/cancel`;
      request
        .get(url)
        .then((response) => {
          this.defaultButtonCancel = true;
          this.clickedButton = undefined;
          this.getPendingOrders();

          let loader = this.$loading.show({
            loader: "spinner",
            container: document.getElementById("tracking"),
            canCancel: true,
            onCancel: this.onCancel,
            color: "#d17842",
            backgroundColor: "#000000",
            height: 80,
            width: 80,
          });

          request.get("/menu").then(({ data: { data } }) => {
            this.UPDATE_CUSTOMER_PAGE_STATE(data);
          });

          request.get("/coupons").then(({ data: { data } }) => {
            this.customerCoffeeCounterState.available = data.available;
            this.customerCoffeeCounterState.consumed = data.consumed;
          });

          loader.hide();
          this.$toastr.s("Success", response.data.message);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    acceptTransfer(id, menu_id, index) {
      this.clickedButtonAccept = index;
      this.defaultButtonAccept = false;
      let url = `/coffee/transfer/${id}/accept`;
      request
        .get(url)
        .then((response) => {
          this.defaultButtonAccept = true;
          this.clickedButtonAccept = undefined;
          this.getPendingOrders();

          let loader = this.$loading.show({
            loader: "spinner",
            container: document.getElementById("tracking"),
            canCancel: true,
            onCancel: this.onCancel,
            color: "#d17842",
            backgroundColor: "#000000",
            height: 80,
            width: 80,
          });

          request.get("/menu").then(({ data: { data } }) => {
            this.UPDATE_CUSTOMER_PAGE_STATE(data);
          });

          request.get("/coupons").then(({ data: { data } }) => {
            this.customerCoffeeCounterState.available = data.available;
            this.customerCoffeeCounterState.consumed = data.consumed;
          });

          loader.hide();
          this.$toastr.s("Success", response.data.message);
        })
        .catch((error) => {
          console.log(error);
        });
    },

    selectMenu(item) {
      this.selectedItem = item.is_active === 1 ? item : false;
    },

    modalClosed() {
      this.selectedItem = false;
    },
  },

  mounted: function () {
    this.getPendingOrders();
  },
};
</script>

<style scoped>
.customer__order__notes {
  padding: 15px 0;
  position: relative;
}

.order__tracking .order__tracking__body {
  position: static;
  flex: 1;
}
.order__tracking .order__tracking__body .cancel__order {
  top: 10px;
  right: 10px;
}
.customer__order__notes p {
  font-size: 14px;
  color: #f3f3f3;
  padding-left: 14px;
  font-style: italic;
  position: relative;
  font-weight: bolder;
  word-break: break-word;
}

.customer__order__notes p::before {
  content: "“";
  position: absolute;
  top: -8px;
  left: 0;
  font-size: 26px;
  color: #f3f3f3;
}
.time-note {
  margin-top: 12px;
}
@media all and (max-width: 450px) {
  .order__tracking .order__tracking__body h3 {
    font-size: 16px;
  }
  .order__tracking .order__tracking__body p {
    font-size: 13px;
  }
}
.order__gift .cancel__order {
  right: 75px !important;
}
.order__gift .accept__btn {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 12px;
  border-radius: 10px;
}
.order__tracking__wrap.order__gift .order__tracking {
  background: #26262A;
  padding: 15px;
  background: linear-gradient(#26262A, #26262A) padding-box,
  linear-gradient(90deg, #FF9661 0.05%, #FFC9B4 50%, #4F4F5C 99.33%) border-box;
  border-radius: 20px;
  border: 1px solid transparent;
}
</style>



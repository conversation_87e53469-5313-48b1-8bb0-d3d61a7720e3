<template>
  <div style="top:30px;">
    <transition name="fade" mode="out-in">
      <router-view/>
    </transition>
  </div>
</template>

<script>
export default {
  name: "MainComponent",

  watch: {
    '$route'(to, from) {
      document.title = to.meta.title || 'Wpcafe'
      this.$store.state.userSidebarVisible = false;
      this.$store.state.baristaSidebarVisible= false;
    }
  },
}
</script>

<style scoped>
@import "https://cdn.jsdelivr.net/npm/animate.css@3.5.1";

.page {
  position: absolute;
  top: 30px;

}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s ease;
}


.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

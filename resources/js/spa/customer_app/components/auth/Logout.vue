<template>

</template>

<script>
import { request } from "../../../helpers/request";
import { mapActions } from "vuex";
import { LOGOUT, TOGGLE_SIDEBAR } from "../../../store/actions";

export default {
    name: "Logout",
    methods: {
        ...mapActions([TOGGLE_SIDEBAR, LOGOUT]),

        logout(){
            request.post('/logout').then(response=>{
                window.localStorage.removeItem('wpCafeAuthToken');
                this.LOGOUT();
                this.$router.push({name: 'login'});
            }).catch(error=>{
                console.log(error);
            });
        }
    },
    mounted(){
        this.logout();
    }
}
</script>

<style scoped>

</style>

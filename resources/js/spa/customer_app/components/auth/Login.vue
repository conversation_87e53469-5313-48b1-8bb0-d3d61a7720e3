<template>
  <div>
    <div class="account__controller">
      <div class="account__access__content">
        <div class="logo">
          <img src="assets/img/site-logo.svg" alt="">
        </div>
        <div class="account__access__form">
          <form action="" method="post">
            <div class="form__group">
              <input type="text" class="form-control fancy__form" v-model="formData.email" placeholder="Email">
            </div>
            <div class="form__group">
              <input :type="showPass ? 'text' : 'password'" class="form-control fancy__form" v-model="formData.password" placeholder="Password">
              <!-- <a href="#" class="forget__pass">Forget Password</a> -->
            </div>
<!--              <div class="show-hide-checkbox">-->
<!--                  <label>show password</label>-->
<!--                  <input type="checkbox" :checked="showPass" @change="toggleShowPass()" />-->
<!--              </div>-->
            <button :disabled="loading" class="WpcButton WpcFilled" @click.prevent="login()">
                <span v-if="loading" class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>&nbsp;
                Sign In
            </button>
              <div class="alternet__access">
                  <RouterLink class="alternet__text" :to="{name:'ForgetPassword'}" href="wpcafe.test">Forgot Password? </RouterLink>
              </div>
          </form>
        </div>
      </div>
      <!-- <div class="alternet__access">

        <p class="alternet__text">Don’t have an account? <a href="#">Create account</a></p>
      </div> -->
    </div>
  </div>
</template>

<script>

import {LOGIN} from "../../../store/actions";
import {request} from "../../../helpers/request";
import {mapActions} from 'vuex'

export default {
  name: "login",

  data(){
    return{
      formData:{
        email:'',
        password:''
      },
        showPass : false,
        loading: false,
    }
  },

  methods:{
    ...mapActions([LOGIN]),


    login(){
        this.loading = true;
        request.post('/login', this.formData).then(({data : {data}}) => {
          this.LOGIN(data);
          if (data.role === 'barista'){
            this.$router.push({name: 'barista'});
          }else {
            this.$router.push({name: 'home'});
          }
        }).catch(error=>{
          this.$toastr.e("error", error.response.data.message);
        })
        .finally(()=>{
            this.loading = false;
        })
    },
      toggleShowPass(){
          this.showPass = !this.showPass;
      }
  },
}
</script>

<style scoped>
.alternet__access .alternet__text {
    color: #ce7878 !important;
}
.alternet__access .alternet__text:hover {
    color: white !important;
}
.alternet__access{
    margin-top: 20px;
    text-align: left;
}
.show-hide-checkbox {
    margin: 10px;
    font-size: 17px;
}
</style>

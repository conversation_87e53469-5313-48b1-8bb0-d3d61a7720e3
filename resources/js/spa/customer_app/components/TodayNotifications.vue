<template>
    <div>
        <div class="section__header">
            <h3 class="section__header__title">Today</h3>
        </div>

        <div class="wpc-notificatons">
            <div class="accordion" id="accordionExample">

                <div class="notification__item" v-for="(today,index) in todayNotifications" :key="index">
                    <div v-if="today.action_type === 'GIFT'" class="card">
                        <div class="card-header" :id="'heading'+index">
                            <h2 class="mb-0">
                                <button class="btn btn-link btn-block text-left collapsed" type="button"
                                        data-toggle="collapse"
                                        :data-target="'#collapse'+index" aria-expanded="true"
                                        :aria-controls="'collapse'+index">
                                    {{ today.message }}
                                    <span><i class="wpc-icon wpc-arrow-down"></i></span>
                                </button>
                            </h2>
                        </div>
                        <div :id="'collapse'+index" class="collapse" :aria-labelledby="'heading'+index"
                             data-parent="#accordionExample">
                            <div class="card-body">
                                <p v-html="today.new_message"></p>
                            </div>
                        </div>
                    </div>
                    <div class="card" v-else>
                        <div class="card-header">
                            <h2 class="mb-0">
                                <p class="btn btn-block text-left">
                                    {{ today.new_message }}
                                </p>
                            </h2>
                        </div>
                    </div>
                  <div class="notification__date__time">
                    <p class="notification__time">{{ notificationTime(today.created_at) }}</p>
                    <p class="notification__time">{{ notificationDateTime(today.created_at) }}</p>
                  </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import {request} from "../../helpers/request";

export default {
    name: "TodayNotifications",
    data() {
        return {
            todayNotifications: {},
        }
    },
    mounted: function () {
        this.list();
    },
    methods: {
        notificationTime(date) {
            return moment(date).fromNow();
        },
        notificationDateTime(date) {
            return moment(date).format("YYYY-MM-DD hh:mm:ss A");
        },

        async list(page = 1) {
            let loader = this.$loading.show({
                loader: 'spinner',
                container: document.getElementById("notify"),
                canCancel: true,
                onCancel: this.onCancel,
                color: '#d17842',
                backgroundColor: '#000000',
                height: 80,
                width: 80
            });
            await request.get(`/today-notifications?page=${page}`).then(({data: {data}}) => {
                this.todayNotifications = data;
                loader.hide();
            }).catch(({response}) => {
                console.error(response)
            })
        }
    }
}
</script>

<style scoped>
.wpc-notificatons .notification__item .notification__date__time {
  display: flex;
  justify-content: space-between;
}
</style>

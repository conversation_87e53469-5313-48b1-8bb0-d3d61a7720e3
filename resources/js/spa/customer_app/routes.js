import Home from './pages/Home';
import Notification from './pages/Notification';
import Coffee from "./pages/CoffeeMenu";
import CoffeeConversion from "./pages/CoffeeConversion";
import OrderHistory from "./pages/OrderHistory";
import OrderAnalytics from "./pages/Analytics.vue";
import Profile from "./pages/Profile";
import OrderRequest from "./pages/OrderRequest";
import Login from "./components/auth/Login";
import ChangePassword from "./pages/ChangePassword";
import ForgetPassword from "./pages/ForgetPassword";
import ResetPassword from "./pages/ResetPassword";
import CoffeeGift from "./pages/CoffeeGift";
import Logout from "./components/auth/Logout";

const routes = [
    {
        path:'/login',
        component:Login,
        name:'login',
        meta: {
            title: 'Login',
            isGuest: true
        }
    },
    {
        path:'/logout',
        component:Logout,
        name:'Logout',
        meta: {
            title: 'Logout',
            requiresAuth: true
        }
    },
    {
        path:'/',
        component:Home,
        name:'home',
        meta: {
            title: 'Home',
            requiresAuth: true,
            customer:true
        }
    },
    {
        path:'/notification',
        component:Notification,
        name:'notification',
        meta: {
            title: 'Notification',
            requiresAuth: true,
            customer:true,

        }
    },
    {
        path:'/coffee',
        component:Coffee,
        name:'coffee',
        meta: {
            title: 'Coffee Menu',
            requiresAuth: true

        }
    },
    {
        path:'/coffee-conversion',
        name:'conversionPage',
        component:CoffeeConversion,
        meta: {
            title: 'Coffee Conversion',
            requiresAuth: true,

        }
    },
    {
        path:'/coffee-gift',
        name:'conversionGiftPage',
        component:CoffeeGift,
        meta: {
            title: 'Coffee Gift',
            requiresAuth: true,

        }
    },
    {
        path:'/order-history',
        component:OrderHistory,
        name:'orderHistory',
        meta: {
            title: 'Order History',
            requiresAuth: true,

        }
    },
    {
        path:'/order-analytics',
        component:OrderAnalytics,
        name:'orderAnalytics',
        meta: {
            title: 'Order Analytics',
            requiresAuth: true,

        }
    },
    {
        path:'/profile',
        component:Profile,
        name:'profile',
        meta: {
            title: 'Profile',
            requiresAuth: true,

        }
    },
    {
        path:'/menu/:id',
        component:OrderRequest,
        meta: {
            title: 'Order',
            requiresAuth: true,

        },
        name:'order'
    },
    {
        path:'/change-password',
        component:ChangePassword,
        meta: {
            title: 'Change Password',
            requiresAuth: true,

        },
        name:'ChangePassword'
    },
    {
        path:'/forget-password',
        component:ForgetPassword,
        meta: {
            title: 'Forget Password',
            requiresAuth: false,

        },
        name:'ForgetPassword'
    },
    {
        path:'/reset-password',
        component:ResetPassword,
        meta: {
            title: 'Reset Password',
            requiresAuth: false,

        },
        name:'ResetPassword'
    },


];

export default routes;

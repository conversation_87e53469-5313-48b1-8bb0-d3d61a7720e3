<template>
  <div>
    <Header />
    <Sidebar />
    <HomeContent />
    <Footer/>
  </div>
</template>

<script>
import Header from "../components/Sidebar";
import Sidebar from "../components/Header.vue";
import Footer from "../components/Footer";
import HomeContent from "../components/home/<USER>";

export default {
  components: {
    Header,
    Sidebar,
    Footer,
    HomeContent
  },

}

</script>

<style scoped>

</style>





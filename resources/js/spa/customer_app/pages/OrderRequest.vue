<template>
    <div>
        <div class="wpc-item__details show">
            <div class="page__header" v-if="maintenance">
                <div class="left__content">
                    <router-link to="/" class="back__button">
                        <i class="wpc-icon wpc-angle-left"></i> Back
                    </router-link>
                </div>
                <div class="page__title"><h5>Menu</h5></div>
                <div class="right__content"></div>
            </div>
            <Maintenance v-if="maintenance"/>
            <form v-else method="post" @submit.prevent="order">
                <input type="hidden" name="_token" value="bJBxuciLqs1hHn2hVmvleksTv1br9XHEt78VNpPW">
                <div class="item__sc">
                    <div class="item__thumb"><img :src="escapeCssUrl(data.thumbnail)" alt="coffee_image">
                        <router-link to="/" class="item__back"><i class="wpc-icon wpc-angle-left"></i></router-link>
                        <div class="item__info">
                            <div class="left__block"><h3>{{ data.name }}</h3>
                                <p class="token__available"><i class="wpc-icon wpc-coffee-cup"></i>{{ coupon }}</p>
                            </div>
                            <div class="making__process">
                                <div class="making__elem" v-for="ingredient in data.ingredients"><i class="wpc-icon"
                                                                                                    :class="`wpc-${ingredient}`"></i>
                                    <span v-if="ingredient=='hot_water'">Hot Water</span>
                                    <span v-else-if="ingredient=='flavor_syrup'">Flavor Syrup</span>
                                    <span v-else>{{ capitalizeFirstLetter(ingredient) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="item__body"><p class="title" style="font-size: 16px; font-weight: 600;">Description</p>
                        <p class="description">
                            {{ data.description }}
                        </p>
                        <div class="order__filter">
                            <div class="espresso-shot-selection mb-3" v-if="data.ingredients && Array.isArray(data.ingredients) && data.ingredients.includes('coffee')">
                                <p class="title">Espresso Shot</p>
                                <div class="shot-counter">
                                    <div class="shot-label">{{ shotCountLabel }}</div>
                                    <div class="counter-controls">
                                        <button type="button" class="counter-btn" @click="decrementShot" :disabled="shotCount === 1">
                                            <i class="wpc-icon wpc-minus"></i>
                                        </button>
                                        <span class="counter-value">{{ shotCount }}</span>
                                        <button type="button" class="counter-btn" @click="incrementShot" :disabled="shotCount === 2 || !availableEspressoCoffees?.availableCoupon > 0">
                                            <i class="wpc-icon wpc-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="shot-note" v-if="shotCount === 2">
                                    <i class="wpc-icon wpc-info"></i>
                                    <span>Double shot will require an extra Espresso coupon</span>
                                </div>
                                <div class="shot-note" v-if="showCouponWarning">
                                    <i class="wpc-icon wpc-info"></i>
                                    <span>You don't have any espresso coffee available for double shot</span>
                                </div>
                            </div>
<!--                          <div class="warning-message" v-if="showCouponWarning">-->
<!--                            <div class="alert alert-warning">-->
<!--                              <i class="wpc-icon wpc-alert-triangle"></i>-->
<!--                              <div class="warning-content">-->
<!--                                <strong>No Espresso Available</strong>-->
<!--                                <p>You don't have any espresso coffee available for double shot.</p>-->
<!--                              </div>-->
<!--                            </div>-->
<!--                          </div>-->
                        </div>
                        <div class="item__instruction">
              <textarea rows="2" id="note" maxlength="80" v-model="formData.note" name="note"
                        placeholder="Request instruction (i.e. No Sugar)" class="form-control"></textarea>
                        </div>
                        <div class="mb-4">
                            <label class="wpc-checkbox">
                                <input type="checkbox" v-model="formData.guest" name="for_guest">
                                <span class="check__box"></span>
                                <span class="text">For Guest</span>
                            </label>
                        </div>
                        <button type="submit" v-if="defaultButton" class="WpcButton WpcFilled" :disabled="coupon==0">
                            Request
                            Coffee
                        </button>
                        <button type="submit" v-else disabled class="WpcButton WpcFilled">
                            <span class="spinner-border spinner-border-sm text-white" role="status"
                                  aria-hidden="true"></span>&nbsp;
                            Request Coffee
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
import {request} from "../../helpers/request";
import Maintenance from "../components/Maintenance";
import { escapeCssUrl } from "../../helpers/utils";

export default {
    name: "OrderRequestComponent",

    data() {
        return {
            data: '',
            coupon: this.$route.params.coupon,
            defaultButton: true,
            formData: {
                menu_id: this.$route.params.id,
                note: '',
                guest: '',
                espresso_shot: 'single', // Default to single shot
            },
            maintenance: false,
            showCouponWarning: false,
            availableEspressoCoffees: {},
            shotCount: 1,
        }
    },

    components: {
        Maintenance,
    },

    computed: {
        shotCountLabel() {
            return this.shotCount === 1 ? 'Single' : 'Double';
        }
    },

    methods: {
      escapeCssUrl,
        incrementShot() {
            if (this.shotCount < 2 && this.availableEspressoCoffees?.availableCoupon > 0) {
                this.shotCount = 2;
                this.formData.espresso_shot = 'double';
            }
        },

        decrementShot() {
            if (this.shotCount > 1) {
                this.shotCount = 1;
                this.formData.espresso_shot = 'single';
            }
        },

        order() {
            this.defaultButton = false;
            request.post('/order', this.formData).then((response) => {
                this.defaultButton = true;
                this.$router.push({name: 'home'})
                if (response.data.code == 201) {
                    this.$toastr.e("error", response.data.message);
                } else {
                    this.$toastr.s("success", response.data.message);
                }
            }).catch((error) => {
                this.$toastr.e("error", error.response.data.message);
                this.$router.push({name: 'home'})
            });
        },
        capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1)
        }
    },
    mounted: async function () {
        if (this.$route.params.id) {
            let loader = this.$loading.show({
                loader: 'spinner',
                container: this.fullPage ? null : this.$refs.formContainer,
                canCancel: true,
                onCancel: this.onCancel,
                color: '#d17842',
                backgroundColor: '#000000',
                height: 80,
                width: 80
            });
            try {
              const [$coffeeResponse, $allMenuResponse] = await Promise.all([
                request.get('/menu/' + this.$route.params.id),
                request.get('/menu')
              ]);
              this.data = $coffeeResponse.data.data;
              this.maintenance = $coffeeResponse.data.is_maintenance;
              this.availableEspressoCoffees = $allMenuResponse.data.data.menus.find(coffee => {
                return coffee.name.includes('Espresso') && coffee.is_active && coffee.can_order;
              });
                // Check if user has any espresso coffee with available coupons
              this.showCouponWarning = !this.availableEspressoCoffees?.availableCoupon > 0;
            } catch (error) {
                console.log('Error fetching menu:', error);
            } finally {
              loader.hide();
            }
        } else {
            this.$router.push({name: 'home'})
        }
    },
}
</script>

<style scoped>
.espresso-shot-selection {
    margin-top: 15px;
    background-color: #1e222a;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(209, 120, 66, 0.2);
}

.espresso-shot-selection .title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #fff;
}

.shot-options {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.shot-note {
    display: flex;
    align-items: flex-start;
    margin-top: 10px;
    padding: 8px 12px;
    background-color: rgba(209, 120, 66, 0.2);
    border-radius: 6px;
    font-size: 0.9em;
    color: #d17842;
}

.shot-note i {
    margin-right: 8px;
    font-size: 16px;
    margin-top: 2px;
}

.warning-message .alert {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-radius: 8px;
    background-color: #1e222a;
    border: 1px solid #d17842;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    margin-top: 10px;
}

.warning-message .alert i {
    margin-right: 12px;
    font-size: 20px;
    color: #d17842;
}

.warning-content {
    display: flex;
    flex-direction: column;
}

.warning-content strong {
    margin-bottom: 4px;
    font-size: 1.05em;
    color: #d17842;
}

.warning-content p {
    margin: 0;
    font-size: 0.95em;
}

/* Shot counter styling */
.shot-counter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.shot-label {
    font-weight: 500;
    color: #fff;
}

.counter-controls {
    display: flex;
    align-items: center;
    background-color: #1e222a;
    border-radius: 8px;
    padding: 5px;
}

.counter-btn {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    background-color: #0c0f14;
    border: none;
    color: #d17842;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.counter-btn:hover:not(:disabled) {
    background-color: #d17842;
    color: #fff;
}

.counter-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.counter-value {
    font-weight: 600;
    color: #fff;
    width: 30px;
    text-align: center;
    font-size: 16px;
}
</style>

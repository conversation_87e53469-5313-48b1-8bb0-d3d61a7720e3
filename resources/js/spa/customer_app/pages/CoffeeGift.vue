<template>
  <div>
    <div class="page__header">
      <div class="left__content">
        <router-link to="/" class="back__button"
          ><i class="wpc-icon wpc-angle-left"></i> Back</router-link
        >
      </div>
      <div class="page__title">
        <h5>Coffee Transfer</h5>
      </div>
      <div class="right__content"></div>
    </div>
    <div class="coffee__gift__wrapper">
      <div class="coffee__gift">
        <h3 class="section-title">Transfer Coffee</h3>
        <div class="gift__from">
          <div class="gift__item__card">
            <p class="title">Teammate</p>
            <div class="item__input">
              <v-select
                class="style-chooser"
                v-model="selectedEmployee"
                :options="employees"
                label="name"
                placeholder="Choose a Teammate"
                @input="filteredEmployee(selectedEmployee)"
              >
                <template #option="{ name, avatar, designation }">
                  <div class="custom-option">
                    <div class="option-image" :style="`background-image: url('${avatar}')`"></div>
                    <div class="option-details">
                      <h5 class="option-name">{{ name }}</h5>
                    </div>
                  </div>
                </template>
                <template #no-options>
                  <div class="no-results">No teammate found</div>
                </template>
              </v-select>

            </div>
          </div>
        </div>
        <div class="gift__from">
          <div class="gift__item__card">
            <p class="title">Coffee</p>
            <div class="item__input WpcFilterSelectorWrapper">
              <select v-model="gitMenu" class="WpcFilterSelector" name="conversion_to" placeholder="Choose a coffee"
                      @click="filteredToMenus(gitMenu)">
                <option selected value="">Choose a coffee</option>
                <option :value="menu" v-for="menu in menus">{{ menu.name }}</option>
              </select>
              <div class="icon">
                <svg width="14" height="10" xmlns="http://www.w3.org/2000/svg" role="presentation"><path d="M9.211364 7.59931l4.48338-4.867229c.407008-.441854.407008-1.158247 0-1.60046l-.73712-.80023c-.407008-.441854-1.066904-.441854-1.474243 0L7 5.198617 2.51662.33139c-.407008-.441853-1.066904-.441853-1.474243 0l-.737121.80023c-.407008.441854-.407008 1.158248 0 1.600461l4.48338 4.867228L7 10l2.211364-2.40069z"></path></svg>
              </div>
            </div>
          </div>
          <div class="selected__gift__item" v-for="menu in menus">
            <div class="gift__item" v-if="menu.id == gitMenu.id">
              <div
                class="item__thumb bgi__property"
                :style="{ backgroundImage: `url(${menu.thumbnail})` }"
              ></div>
              <div class="item__body">
                <h3 class="item__name">{{ menu.name }}</h3>
                <div class="item__body__bottom">
                  <div class="item__available__count">
                    <div class="icon">
                      <i class="wpc-icon wpc-coffee-cup"></i>
                    </div>
                    <p>{{ fromCalculatedCoffee }}</p>
                  </div>
                  <div class="WpcIncDecButtonGroup">
                    <button class="WpcDecButton" @click="sub()">
                      <i class="wpc-icon wpc-minus"></i>
                    </button>
                    <input
                      class="WpcIncDecInput"
                      disabled
                      :value="fromQuantity"
                      type="text"
                    />
                    <button
                      class="WpcIncButton"
                      @click="add()"
                      :disabled="selectedEmployee == ''"
                    >
                      <i class="wpc-icon wpc-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button v-if="buttonSpinner" type="submit" class="WpcButton WpcFilled">
          <span
            class="spinner-border spinner-border-sm text-white"
            role="status"
            aria-hidden="true"
          ></span>
          Submit
        </button>
        <button
          v-else
          href="#"
          type="submit"
          class="WpcButton WpcFilled"
          :disabled="gitMenu.availableCoupon == null || fromQuantity == 0"
          @click="sendGift"
        >
          Submit
        </button>
      </div>

      <!-- Section Divider -->
      <div class="section-divider">
        <div class="divider-line"></div>
        <h3 class="divider-text">Transfer History</h3>
        <div class="divider-line"></div>
      </div>

      <!-- Coffee Gift History Section -->
      <div class="gift-history-section">
        <!-- Filters Section -->
        <div class="analytics-filters">
            <div class="form-group">
              <label for="teammate-filter">Filter by Teammate</label>
              <div class="custom-select-container">
                <v-select
                  v-model="selectedTeammates"
                  :options="teammateOptions"
                  :reduce="teammate => teammate.value"
                  label="label"
                  multiple
                  :clearable="true"
                  :close-on-select="false"
                  :deselectFromDropdown="true"
                  placeholder="Select teammates"
                >
                  <template #selected-option="{ label }">
                    <div class="selected-tag">
                      <span>{{ label }}</span>
                    </div>
                  </template>
                  <template #option="{ label }">
                    <div class="select-option">
                      {{ label }}
                    </div>
                  </template>
                </v-select>
              </div>
            </div>

            <div class="form-group">
              <label for="coffee-filter">Filter by Coffee</label>
              <div class="custom-select-container">
                <v-select
                  v-model="selectedCoffees"
                  :options="coffeeOptions"
                  :reduce="coffee => coffee.value"
                  label="label"
                  multiple
                  :clearable="true"
                  :close-on-select="false"
                  :deselectFromDropdown="true"
                  placeholder="Select coffees"
                >
                  <template #selected-option="{ label }">
                    <div class="selected-tag">
                      <span>{{ label }}</span>
                    </div>
                  </template>
                  <template #option="{ label }">
                    <div class="select-option">
                      {{ label }}
                    </div>
                  </template>
                </v-select>
              </div>
            </div>

            <div class="form-group">
              <label for="date-range">Date Range</label>
              <div class="date-dropdown">
                <v-select
                  v-model="selectedDateRange"
                  :options="dateRangeOptions"
                  :reduce="option => option.value"
                  label="label"
                  :clearable="false"
                  @input="handleDateRangeChange"
                  placeholder="Select date range"
                >
                  <template #selected-option="{ label }">
                    <div class="selected-date-option">
                      <span>{{ label }}</span>
                    </div>
                  </template>
                  <template #option="{ label }">
                    <div class="date-option">
                      {{ label }}
                    </div>
                  </template>
                </v-select>
              </div>
            </div>

            <!-- Custom date range inputs, shown only when custom is selected -->
            <div v-if="selectedDateRange === 'custom'" class="date-range-inputs">
              <div class="form-group date-form-group">
                <div class="date-label-container">
                  <label for="date-from">From Date</label>
                </div>
                <div class="date-picker-container">
                  <date-range-picker
                    ref="fromDatePicker"
                    :opens="'right'"
                    :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                    :minDate="minDate"
                    :maxDate="maxDate"
                    :autoApply="true"
                    :singleDatePicker="true"
                    :showDropdowns="true"
                    :ranges="false"
                    :showWeekNumbers="false"
                    v-model="fromDateRange"
                    @update="updateFromDate"
                  >
                    <template v-slot:input="picker">
                      <div class="date-display">
                        {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.startDate)) }}
                      </div>
                    </template>
                  </date-range-picker>
                </div>
              </div>
              <div class="form-group date-form-group">
                <div class="date-label-container">
                  <label for="date-to">To Date</label>
                </div>
                <div class="date-picker-container">
                  <date-range-picker
                    ref="toDatePicker"
                    :opens="'left'"
                    :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                    :minDate="fromDateRange.startDate ? new Date(fromDateRange.startDate) : minDate"
                    :maxDate="maxDate"
                    :autoApply="true"
                    :singleDatePicker="true"
                    :showDropdowns="true"
                    :ranges="false"
                    :showWeekNumbers="false"
                    v-model="toDateRange"
                    @update="updateToDate"
                  >
                    <template v-slot:input="picker">
                      <div class="date-display">
                        {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.endDate)) }}
                      </div>
                    </template>
                  </date-range-picker>
                </div>
              </div>
            </div>

            <!-- Apply Filter Button -->
            <div class="form-group analytics-filters-bottom">
              <button class="btn btn-primary" @click="fetchGiftHistory" :disabled="loading">
                <span v-if="loading" class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>
                <span v-else>Apply Filter</span>
              </button>
            </div>
          </div>

        <!-- Gift History Results Section -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>History Results</h5>
              </div>
              <div class="card-body">
                <div v-if="loading" class="loading-overlay">
                  <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                  </div>
                </div>

                <div v-if="error" class="error-message">
                  {{ error }}
                </div>
                <div class="table-responsive">
                  <table v-if="!loading && !error && giftHistory.length > 0" class="table table-dark">
                    <thead>
                    <tr>
                      <th>Date</th>
                      <th>Teammate</th>
                      <th>Coffee</th>
                      <th>Quantity</th>
                      <th>Status</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="gift in giftHistory" :key="gift.id">
                      <td>{{ gift.date_formatted }}</td>
                      <td>{{ gift.receiver.name }}</td>
                      <td>{{ gift.items.name }}</td>
                      <td>{{ gift.items.qty }}</td>
                      <td>
                    <span :class="'status-badge ' + gift.status.toLowerCase()">
                      {{ gift.status }}
                    </span>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                  <div v-if="!loading && !error && giftHistory.length === 0" class="no-data-message">
                    No gift history found for the selected filters.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <p>&copy; All Right Reserved</p>
    </div>
  </div>
</template>

<script>
import { request } from "../../helpers/request";
import VueSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';

export default {
  name: "CoffeeGift",
  components: {
    "v-select": VueSelect,
    DateRangePicker
  },

  data() {
    return {
      // Coffee Gift Form Data
      menus: [],
      employees: [],
      selectedEmployee: "",
      gitMenu: "",
      fromQuantity: 0,
      buttonSpinner: false,

      // Gift History Data
      giftHistory: [],
      loading: false,
      error: null,
      selectedTeammates: [],
      selectedCoffees: [],
      selectedDateRange: 'today',
      dateFrom: this.getDefaultStartDate(),
      dateTo: this.getDefaultEndDate(),
      fromDateRange: {
        startDate: this.getDefaultStartDate(),
        endDate: this.getDefaultStartDate(),
      },
      toDateRange: {
        startDate: this.getDefaultEndDate(),
        endDate: this.getDefaultEndDate()
      },
      minDate: new Date(new Date().setFullYear(new Date().getFullYear() - 3)),
      maxDate: new Date(),
      coffeeList: [],
    };
  },

  mounted() {
    this.coffeeGiftPageData();
    this.fetchGiftHistory();
  },

  computed: {
    fromCalculatedCoffee() {
      let calculatedCoffee = this.gitMenu.availableCoupon;
      if (Number.isInteger(calculatedCoffee)) {
        return calculatedCoffee;
      } else {
        return parseFloat(calculatedCoffee).toFixed(1);
      }
    },

    // Options for the teammate filter
    teammateOptions() {
      if (!this.employees || this.employees.length === 0) {
        return [];
      }

      return this.employees.map(teammate => ({
        label: teammate.name,
        value: teammate.id
      }));
    },

    // Options for the coffee filter
    coffeeOptions() {
      if (!this.menus || this.menus.length === 0) {
        return [];
      }

      return this.menus.map(coffee => ({
        label: coffee.name,
        value: coffee.id
      }));
    },

    // Date range options
    dateRangeOptions() {
      return [
        { label: 'Today', value: 'today' },
        { label: 'Yesterday', value: 'yesterday' },
        { label: 'Last 7 Days', value: 'last7days' },
        { label: 'Last 30 Days', value: 'last30days' },
        { label: 'This Month', value: 'thisMonth' },
        { label: 'Last Month', value: 'lastMonth' },
        { label: 'Custom Range', value: 'custom' }
      ];
    },
  },

  methods: {
    // Coffee Gift Form Methods
    coffeeGiftPageData() {
      request
        .get("/coffee/transfer")
        .then(({ data: { data } }) => {
          this.menus = data?.allMenu;
          this.employees = data?.employees;
        })
        .catch((error) => {
          console.log(error);
        });
    },

    add() {
      if (this.fromQuantity >= 0 && this.gitMenu.availableCoupon > 0) {
        if (
          this.gitMenu.availableCoupon < 1 &&
          this.gitMenu.availableCoupon > 0
        ) {
          this.fromQuantity += this.gitMenu.availableCoupon;
          this.gitMenu.availableCoupon -= this.gitMenu.availableCoupon;
        } else {
          this.gitMenu.availableCoupon -= 1; //this one decreases the value of total coffee coming from database and the computed methods invokes
          this.fromQuantity += 1;
        }
      }
    },

    sub() {
      if (this.fromQuantity >= 1) {
        this.fromQuantity -= 1;
        this.gitMenu.availableCoupon += 1; //this one increases the value of total coffee coming from database and the computed methods invokes
      }
      if (this.fromQuantity > 0 && this.fromQuantity < 1) {
        this.gitMenu.availableCoupon += this.fromQuantity;
        this.fromQuantity -= this.fromQuantity;
      }
    },

    sendGift() {
      this.buttonSpinner = true;
      request
        .post("/coffee/transfer", {
          receiver_id: this.selectedEmployee.id,
          coffee_id: this.gitMenu.id,
          coffee_quantity: this.fromQuantity,
        })
        .then(({ data: { data } }) => {
          console.log(data);
          this.reset();
          this.buttonSpinner = false;
          this.$toastr.s("success", "Gift sent successfully");
          // Refresh gift history after sending a gift
          this.fetchGiftHistory();
        })
        .catch((error) => {
          console.log(error);
          this.buttonSpinner = false;
          this.$toastr.e("error", "Failed to send gift");
        });
    },

    filteredToMenus(menu) {
      this.fromQuantity = 0;
    },

    filteredEmployee(employee) {},

    reset() {
      this.gitMenu = "";
      this.fromQuantity = 0;
      this.coffeeGiftPageData();
    },

    resetConversionToValues() {
      const quantity = this.fromQuantity;
      this.gitMenu.availableCoupon += quantity;
      this.fromQuantity = 0;
    },

    // Gift History Methods
    getDefaultStartDate() {
      // Return today's date for the default start date
      return new Date().toISOString().split('T')[0];
    },

    getDefaultEndDate() {
      return new Date().toISOString().split('T')[0];
    },

    formatDateDisplay(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toISOString().split('T')[0];
    },

    updateFromDate(range) {
      this.fromDateRange = range;
      this.dateFrom = this.formatDateDisplay(range.startDate);
    },

    updateToDate(range) {
      this.toDateRange = range;
      this.dateTo = this.formatDateDisplay(range.startDate);
    },

    handleDateRangeChange() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];

      switch(this.selectedDateRange) {
        case 'today':
          this.dateFrom = todayStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: today, endDate: today };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'yesterday':
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = yesterday.toISOString().split('T')[0];
          this.dateFrom = yesterdayStr;
          this.dateTo = yesterdayStr;
          this.fromDateRange = { startDate: yesterday, endDate: yesterday };
          this.toDateRange = { startDate: yesterday, endDate: yesterday };
          break;

        case 'last7days':
          const last7days = new Date();
          last7days.setDate(last7days.getDate() - 6);
          const last7daysStr = last7days.toISOString().split('T')[0];
          this.dateFrom = last7daysStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last7days, endDate: last7days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'last30days':
          const last30days = new Date();
          last30days.setDate(last30days.getDate() - 29);
          const last30daysStr = last30days.toISOString().split('T')[0];
          this.dateFrom = last30daysStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last30days, endDate: last30days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'thisMonth':
          const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          const firstDayStr = firstDayOfMonth.toISOString().split('T')[0];
          this.dateFrom = firstDayStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: firstDayOfMonth, endDate: firstDayOfMonth };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'lastMonth':
          const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
          const firstDayLastMonthStr = firstDayOfLastMonth.toISOString().split('T')[0];
          const lastDayLastMonthStr = lastDayOfLastMonth.toISOString().split('T')[0];
          this.dateFrom = firstDayLastMonthStr;
          this.dateTo = lastDayLastMonthStr;
          this.fromDateRange = { startDate: firstDayOfLastMonth, endDate: firstDayOfLastMonth };
          this.toDateRange = { startDate: lastDayOfLastMonth, endDate: lastDayOfLastMonth };
          break;

        case 'custom':
          // Don't change the dates for custom, let the user select them
          break;
      }

      // Don't fetch automatically - wait for Apply Filter button
    },

    fetchGiftHistory() {
      this.loading = true;
      this.error = null;

      const params = {
        receiver_id: this.selectedTeammates.length > 0 ? this.selectedTeammates.join(',') : '',
        coffee_id: this.selectedCoffees.length > 0 ? this.selectedCoffees.join(',') : '',
        start_date: this.dateFrom,
        end_date: this.dateTo
      };

      request.get('/coffee/transfer/history', { params })
        .then(response => {
          this.giftHistory = response.data.data;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error fetching gift history:', error);
          this.error = 'Failed to load gift history. Please try again.';
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped>
.coffee__gift__wrapper {
  padding: 0 15px 30px;
}
.style-chooser .vs__search::placeholder,
.style-chooser .vs__dropdown-toggle,
.style-chooser .vs__dropdown-menu {
  background: #dfe5fb;
  border: none;
  color: #394066;
  text-transform: lowercase;
  font-variant: small-caps;
}

.style-chooser .vs__clear,
.style-chooser .vs__open-indicator {
  fill: #394066;
}
.coffee__gift {
  max-width: 100%;
  padding: 15px;
  margin: 0 auto;
  background-color: #242c39;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.coffee__gift .gift__from {
  margin-bottom: 20px;
}
.gift__from .gift__item__card {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
}

.gift__from .gift__item__card .title {
  flex: 0 0 100px;
  color: #fff;
  font-size: 16px;
}
.gift__from .gift__item__card .item__input {
  flex: 1;
}

.selected__gift__item .gift__item {
  display: flex;
  border-radius: 20px;
  overflow: hidden;
  background-color: #1e222a;
  padding: 15px;
}
.selected__gift__item .gift__item .item__thumb {
  flex: 0 0 64px;
  width: 64px;
  padding-top: 0;
  height: 64px;
  border-radius: 12px;
}
.selected__gift__item .gift__item .item__body {
  padding: 0 0 0 15px;
  flex: 1 1 auto;
}
.selected__gift__item .gift__item .item__body .item__name {
  font-size: 16px;
  text-align: left;
}
.selected__gift__item .gift__item .item__body .item__body__bottom {
  display: flex;
  width: 100%;
  margin-top: 10px;
  justify-content: space-between;
}
.selected__gift__item
  .gift__item
  .item__body
  .item__body__bottom
  .item__available__count {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background: #1e222a;
  border: 1px solid rgba(131, 133, 142, 0.2);
  align-items: center;
  height: 35px;
  min-width: 45px;
  padding: 0 5px;
  border-radius: 12px;
}
.selected__gift__item
  .gift__item
  .item__body
  .item__body__bottom
  .item__available__count
  .icon {
  font-size: 14px;
  margin-right: 5px;
}
.selected__gift__item
  .gift__item
  .item__body
  .item__body__bottom
  .item__available__count
  .icon
  i {
  color: #d17842;
}

/* inside select template */
.style-chooser .custom-option {
  display: flex;
  align-items: center;
  padding: 4px 8px; /* Optional: Add some padding for better visual spacing */
}

.style-chooser .option-image {
  width: 30px;   /* Set the size of the avatar */
  height: 30px;  /* Match the width for a square avatar */
  border-radius: 50%; /* Make the avatar circular */
  margin-right: 12px; /* Space between the image and the text */
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: center center;
  background-size: cover;
  background-color: #d17842;
}

.style-chooser .option-details {
  display: flex;
  flex-direction: column; /* Stack the name and designation vertically */
}

.style-chooser .option-name {
  margin: 0; /* Remove default margin */
  font-size: 14px; /* Make the font size smaller */
  font-weight: normal; /* Optional: Adjust the font weight */
  word-wrap: break-word; /* Ensure long words are wrapped */
  white-space: normal; /* Allow text to wrap to the next line */
  overflow: hidden; /* Hide any overflow text */
  text-overflow: ellipsis; /* Add ellipsis (...) if text overflows */
}

.style-chooser .option-designation {
  font-size: 12px; /* Smaller font for the designation */
  color: gray; /* Optional: Use a different color to differentiate */
}

.WpcFilterSelectorWrapper {
  margin-right: 0;
  position: relative;
}
.WpcFilterSelectorWrapper:before {
  content: '';
}
.WpcFilterSelectorWrapper .WpcFilterSelector {
  width: 100%;
  margin-right: 0;
}
.WpcFilterSelectorWrapper .icon {
  position: absolute;
  top: 10px;
  right: 10px;
  pointer-events: none;
}
.WpcFilterSelectorWrapper .icon svg {
  fill: #bcbcbc;
}

/* Gift History Styles */
.gift-history-section {
  margin-top: 20px;
  padding: 0;
}

/* Section Divider */
.section-divider {
  display: flex;
  align-items: center;
  margin: 40px 0 20px;
  text-align: center;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: #d17842;
}

.divider-text {
  margin: 0 15px;
  color: #d17842;
  font-size: 24px;
  font-weight: 600;
}

/* Filter Container */

/* Filter Button */
.analytics-filters .analytics-filters-bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
}

/* Results Section */
.gift-history-results {
  padding: 20px;
  background-color: #1e222a;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.results-title {
  margin-top: 0;
  margin-bottom: 20px;
  color: #d17842;
  font-size: 18px;
  font-weight: 500;
}

.section-title {
  font-size: 24px;
  color: #fff;
  margin-bottom: 20px;
  text-align: center;
}

.analytics-filters {
  background-color: #242c39;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  color: #fff;
}

.analytics-filters .form-group {
  margin-bottom: 1rem;
}

.analytics-filters label {
  color: #d17842;
  font-weight: 500;
  margin-bottom: 8px;
}

.custom-select-container {
  width: 100%;
}

.date-range-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
}

.date-label-container {
  margin-bottom: 8px;
}

.date-picker-container {
  width: 100%;
}
.gift-history-table-container {
  position: relative;
  margin-top: 20px;
  overflow-x: auto;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.error-message {
  color: #ff6b6b;
  text-align: center;
  padding: 20px;
  font-weight: 500;
}

.no-data-message {
  color: #fff;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.gift-history-table {
  width: 100%;
  border-collapse: collapse;
  color: #fff;
}

.gift-history-table th,
.gift-history-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #333;
}

.gift-history-table th {
  background-color: #242c39;
  font-weight: 500;
  color: #d17842;
}

.gift-history-table tr:hover {
  background-color: #242c39;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #ffc107;
  color: #000;
}

.status-badge.accepted {
  background-color: #28a745;
  color: #fff;
}

.status-badge.rejected {
  background-color: #dc3545;
  color: #fff;
}

.selected-tag {
  background-color: #d17842;
  color: #fff;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.selected-date-option,
.date-option,
.select-option {
  padding: 5px 0;
}

@media (max-width: 768px) {
  .gift-history-table th,
  .gift-history-table td {
    padding: 8px 10px;
    font-size: 14px;
  }
}
</style>

<style>
.vs__dropdown-toggle {
  background: #1e222a !important;
  border-radius: 12px;
}
.vs__dropdown-menu {
  color: #1e222a !important;
}
.vs__search {
  font-size: 14px;
  padding: 0 15px !important;
}
.vs__search:focus {
  font-size: 14px !important;
}
.vs__search::-webkit-input-placeholder {
  color: #fff;
}
.vs__search::-moz-placeholder {
  color: #fff;
}
.vs__search:-ms-input-placeholder {
  color: #fff;
}
.vs__search:-moz-placeholder {
  color: #fff;
}
.vs--searchable .vs__dropdown-toggle {
  min-height: 40px;
}
.vs__selected {
  color: #fff;
  padding: 2px 14px 0;
  margin: 0;
  font-size: 14px;
  line-height: 30px;
}
.vs__actions .vs__clear svg {
  fill: #bcbcbc;
}
.vs__actions .vs__open-indicator {
  fill: #bcbcbc;
}
.vs__selected-options {
  padding: 0 15px;
}
.vs__selected-options .vs__selected {
  padding: 0;
}
.vs__selected-options .vs__search {
  padding: 0 !important;
}

/* Vue Select Multiselect Styling */
.v-select .vs__selected-options {
  padding: 10px !important;
}
.v-select .vs__no-options {
  color: #fff;
}
.custom-select-container .v-select .vs__selected {
  background-color: #d17842;
  color: #fff;
  padding: 5px 10px !important;
  margin: 0 5px 5px 0;
  border-radius: 15px;
  font-weight: 500;
}
.v-select .vs__deselect {
  fill: #fff;
  margin-left: 8px;
}
.vs__dropdown-option--highlight {
  background-color: #d17842;
}

/* Date display styling to match form control */
.date-range-inputs .form-group {
  flex: 1;
  margin-bottom: 0;
}
.date-picker-container .reportrange-text {
  background-color: #242c39 !important;
  color: #fff;
  border-radius: 8px;
  padding: 10px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.date-picker-container .reportrange-text::after {
  content: '\1F4C5';
  margin-left: auto;
  font-size: 10px;
  color: #d17842;
}

.date-form-group {
  flex: 1;
  min-width: 200px;
}

.analytics-filters .btn-primary {
  background-color: #d17842;
  border-color: #d17842;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
}

.analytics-filters .btn-primary:focus, .analytics-filters .btn-primary:not(.disabled):active {
  background-color: #d17842;
  border-color: #d17842;
}
.gift-history-section .card {
  background-color: #242c39;
  border: none;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}
.gift-history-section .card-body {
  padding: 20px;
  color: #fff;
}
.custom-card-header {
  border-bottom: 1px solid #333;
  padding: 15px 20px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.custom-card-header h5 {
  color: #d17842;
  font-weight: 600;
  margin: 0;
  font-size: 18px;
}

.gift-history-section .table {
  color: #fff;
}

.gift-history-section .table th {
  border-top: none;
  border-bottom: 2px solid #333;
  color: #d17842;
  text-align: center;
}

.gift-history-section .table td {
  border-top: 1px solid #333;
  vertical-align: middle;
  text-align: center;
}

</style>

<template>
    <div>
        <div class="page__header">
            <div class="left__content">
                <router-link to="/login" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
            </div>
            <div class="page__title">
                <h5>Forget Password</h5>
            </div>
            <div class="right__content">
            </div>
        </div>
        <div class="account__controller">
            <div class="account__access__content">
                <div class="logo">
                    <img src="assets/img/site-logo.svg" alt="">
                </div>
                <div class="account__access__form">
                    <div class="forget__pass__heading" style="color: #f3f3f3;">
                        <h5 style="margin-bottom: 30px">Reset password</h5>
                    </div>
                    <form action="" method="post">
                        <div class="form__group">
                            <input type="text" class="form-control fancy__form" placeholder="Email" v-model="data.email">
                        </div>
                        <button :disabled="loading" class="WpcButton WpcFilled" @click.prevent="sendPassResetLink()">
                            <span v-if="loading" class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>&nbsp;
                            Email reset link
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { request } from "../../helpers/request";

export default {
    name: "ForgetPassword",
    data() {
        return {
            data : {
                email: '',
            },
            loading: false,
        }
    },
    methods : {
        sendPassResetLink() {
            this.loading = true;

            request.post('/reset-password', this.data).then((data) => {
                this.data.email = '';
                this.$toastr.s("", data.data.message);
            }).catch( error=> {
                this.$toastr.e("error", error.response.data.message);
            }).finally(() => {
                this.loading = false;
            });
        }
    }
}
</script>

<style scoped>

</style>

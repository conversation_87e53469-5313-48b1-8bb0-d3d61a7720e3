<template>
    <div id="notify">
        <div class="page__header">
            <div class="left__content">
                <router-link to="/forget-password" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back
                </router-link>
            </div>
            <div class="page__title">
                <h5>Reset Password</h5>
            </div>
            <div class="right__content"></div>
        </div>
        <div v-if="!pageLoading">
            <div v-if="data.validity" class="edit__profile__form">
                <form
                    method="post"
                    @submit.prevent="updatePassword()"
                >
                    <div class="form__group">
                        <label for="new-pass">New Password</label>
                        <input :type="showPass ? 'text' : 'password'" id="new-pass" class="form-control" v-model="updateData.new_password">
                        <p v-if="errors.new_password && errors.new_password.length > 0" class="validation-message">
                            {{ errors.new_password[0] }}</p>
                    </div>
                    <div class="form__group">
                        <label for="confirm-pass">Confirm Password</label>
                        <input :type="showPass ? 'text' : 'password'" id="confirm-pass" class="form-control"
                               v-model="updateData.confirm_password">
                        <p v-if="errors.confirm_password && errors.confirm_password.length > 0"
                           class="validation-message">{{ errors.confirm_password[0] }}</p>
                    </div>
                    <button class="WpcButton WpcFilled" :disabled="loading">
                        <span v-if="loading" class="spinner-border spinner-border-sm text-white" role="status"
                              aria-hidden="true"></span>&nbsp;
                        Save Changes
                    </button>
<!--                    <div class="show-hide-checkbox">-->
<!--                        <label>show password</label>-->
<!--                        <input type="checkbox" :checked="showPass" @change="toggleShowPass()" />-->
<!--                    </div>-->
                </form>
            </div>
            <div v-else>
                <h3 style="text-align: center">Invalid token</h3>
            </div>
        </div>

        <div class="footer">
            <p>&copy; All Right Reserved</p>
        </div>
    </div>
</template>

<script>
import { request } from "../../helpers/request";

export default {
    name: "ResetPassword",
    data() {
        return {
            data: {
                token: '',
                validity: false
            },
            updateData: {
                new_password: '',
                confirm_password: '',
            },
            loading: false,
            pageLoading: true,
            errors: [],
            showPass : false
        }
    },
    methods: {
        validateResetToken(token) {
            let loader = this.$loading.show({
                loader: 'spinner',
                container: document.getElementById("notify"),
                canCancel: true,
                onCancel: this.onCancel,
                color: '#d17842',
                backgroundColor: '#000000',
                height: 80,
                width: 80
            });

            request.get('/validate/' + token).then(({data: {data}}) => {
                this.data.token = data;
                this.data.validity = true;
            }).catch(error => {
                console.log(error.response);
            })
                .finally(() => {
                    this.pageLoading = false;
                    loader.hide();
                });
        },

        updatePassword() {
            this.loading = true;
            let formData = new FormData();
            formData.append("token", this.data.token);
            formData.append("new_password", this.updateData.new_password);
            formData.append("confirm_password", this.updateData.confirm_password);

            request.post('/update-password', formData).then((data) => {
                console.log(data)
                this.$toastr.s("success", data.data.message);
            })
            .then(() => {
                this.$router.push({name: 'login'})
            })
            .catch(error => {
                if (error.response.status === 422) {
                    this.errors = error.response.data.errors;
                } else {
                    this.$toastr.e("error", error.response.data.message);
                }
            })
            .finally(() => {
                this.loading = false;
            });
        },

        toggleShowPass(){
            this.showPass = !this.showPass;
        }
    },
    mounted() {
        this.validateResetToken(this.$route.query.token);
    }
}
</script>

<style scoped>
.validation-message {
    color: red;
}
.show-hide-checkbox {
    margin: 10px;
    font-size: 17px;
}
</style>

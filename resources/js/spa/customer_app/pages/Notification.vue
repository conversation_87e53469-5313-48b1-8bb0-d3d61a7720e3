<template>
    <div class="notification" id="notify">
        <div class="page__header">
            <div class="left__content">
                <router-link to="/" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
            </div>
            <div class="page__title">
                <h5>Notification</h5>
            </div>
            <div class="right__content">
                <!--      <a href="#" class="clear__button">Clear all</a>-->
            </div>
        </div>
        <NotificationStatus />
        <TodayNotifications/>
        <PreviousNotifications/>
    </div>
</template>

<script>
import TodayNotifications from "../components/TodayNotifications";
import PreviousNotifications from "../components/PreviousNotifications";
import NotificationStatus from "../components/NotificationStatus.vue";

export default {
    name: "NotificationComponent",

    components : {
        NotificationStatus,
        TodayNotifications,
        PreviousNotifications
    }
}
</script>

<style scoped>
</style>

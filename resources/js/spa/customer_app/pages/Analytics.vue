<template>
  <div>
    <!-- Full-page loading overlay -->
    <div class="full-page-loader" v-if="loading">
      <div class="loader-content">
        <div class="spinner"></div>
        <p>Loading data...</p>
      </div>
    </div>

    <div class="wpc-container analytics-page">
      <div class="page__header">
        <div class="left__content">
          <router-link to="/" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
        </div>
        <div class="page__title">
          <h5>My Coffee Orders Analytics</h5>
        </div>
        <div class="right__content"></div>
      </div>

      <div class="analytics-filters">
        <div class="form-group">
          <label for="coffee-filter">Filter by Coffee</label>
          <div class="custom-select-container">
            <v-select
                v-model="selectedCoffees"
                :options="coffeeOptions"
                :reduce="coffee => coffee.value"
                label="label"
                multiple
                :clearable="true"
                :close-on-select="false"
                :deselectFromDropdown="true"
                placeholder="Select coffees"
            >
              <template #selected-option="{ label }">
                <div class="selected-tag">
                  <span>{{ label }}</span>
                </div>
              </template>
              <template #option="{ label }">
                <div class="select-option">
                  {{ label }}
                </div>
              </template>
            </v-select>
          </div>
        </div>
        <div class="form-group">
          <label for="date-range">Date Range</label>
          <div class="date-dropdown">
            <v-select
              v-model="selectedDateRange"
              :options="dateRangeOptions"
              :reduce="option => option.value"
              label="label"
              :clearable="false"
              @input="handleDateRangeChange"
              placeholder="Select date range"
            >
              <template #selected-option="{ label }">
                <div class="selected-date-option">
                  <span>{{ label }}</span>
                </div>
              </template>
              <template #option="{ label }">
                <div class="date-option">
                  {{ label }}
                </div>
              </template>
            </v-select>
          </div>
        </div>

        <!-- Custom date range inputs, shown only when custom is selected -->
        <div v-if="selectedDateRange === 'custom'" class="date-range-inputs">
          <div class="form-group date-form-group">
            <div class="date-label-container">
              <label for="date-from">From Date</label>
            </div>
            <div class="date-picker-container">
              <date-range-picker
                ref="fromDatePicker"
                :opens="'right'"
                :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                :minDate="minDate"
                :maxDate="maxDate"
                :autoApply="true"
                :singleDatePicker="true"
                :showDropdowns="true"
                :ranges="false"
                :showWeekNumbers="false"
                v-model="fromDateRange"
                @update="updateFromDate"
              >
                <template v-slot:input="picker">
                  <div class="date-display">
                    {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.startDate)) }}
                  </div>
                </template>
              </date-range-picker>
            </div>
          </div>
          <div class="form-group date-form-group">
            <div class="date-label-container">
              <label for="date-to">To Date</label>
            </div>
            <div class="date-picker-container">
              <date-range-picker
                ref="toDatePicker"
                :opens="'left'"
                :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                :minDate="fromDateRange.startDate ? new Date(fromDateRange.startDate) : minDate"
                :maxDate="maxDate"
                :autoApply="true"
                :singleDatePicker="true"
                :showDropdowns="true"
                :ranges="false"
                :showWeekNumbers="false"
                v-model="toDateRange"
                @update="updateToDate"
              >
                <template v-slot:input="picker">
                  <div class="date-display">
                    {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.endDate)) }}
                  </div>
                </template>
              </date-range-picker>
            </div>
          </div>
        </div>
        <div class="form-group analytics-filters-bottom">
          <button class="btn btn-primary" @click="fetchAnalytics">Apply Filters</button>
        </div>
      </div>

      <div class="analytics-content">
        <!-- Order Status Summary -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Order Status Summary</h5>
              </div>
              <div class="card-body">
                <div class="status-summary">
                  <div class="status-item-wrapper">
                    <div class="status-item">
                      <div class="status-details">
                        <h3>{{ orderStatusCounts.total || 0 }}</h3>
                        <p>Total Orders</p>
                      </div>
                    </div>
                  </div>
                  <div class="status-item-wrapper">
                    <div class="status-item">
                      <div class="status-details">
                        <h3>{{ orderStatusCounts.pending || 0 }}</h3>
                        <p>Pending</p>
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="status-details">
                        <h3>{{ orderStatusCounts.processing || 0 }}</h3>
                        <p>Processing</p>
                      </div>
                    </div>
                  </div>
                  <div class="status-item-wrapper">
                    <div class="status-item">
                      <div class="status-details">
                        <h3>{{ orderStatusCounts.completed || 0 }}</h3>
                        <p>Completed</p>
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="status-details">
                        <h3>{{ orderStatusCounts.rejected || 0 }}</h3>
                        <p>Rejected</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Coffee Orders Summary -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Coffee Orders Summary</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-dark">
                    <thead>
                    <tr>
                      <th style="text-align: center">Coffee</th>
                      <th>Requested</th>
                      <th>Served</th>
                      <th>Rejected</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="coffee in coffeeStats" :key="coffee.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="coffee-thumb mr-2" :style="{ backgroundImage: `url('${escapeCssUrl(coffee.thumbnail)}')` }"></div>
                          <span>{{ coffee.name }}</span>
                        </div>
                      </td>
                      <td>{{ coffee.total_requested }}</td>
                      <td>{{ coffee.request_completed }}</td>
                      <td>{{ coffee.request_rejected || 0 }}</td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Time-based Coffee Consumption Analytics -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Coffee Consumption by Time</h5>
              </div>
              <div class="card-body">
                <div v-if="error" class="alert alert-danger">
                  {{ error }}
                </div>
                <div v-else>
                  <div class="chart-container" style="height: 450px;">
                    <apexchart
                      ref="timeChart"
                      type="area"
                      height="400"
                      :options="timeChartOptions"
                      :series="timeChartData.series"
                    ></apexchart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="copyright-footer">
        <p>© All Right Reserved</p>
      </div>
    </div>
  </div>
</template>

<script>
import { request } from '../../helpers/request';
import VueApexCharts from 'vue-apexcharts';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import {escapeCssUrl} from "../../helpers/utils";

export default {
  name: "CustomerAnalytics",
  components: {
    apexchart: VueApexCharts,
    vSelect,
    DateRangePicker
  },
  data() {
    return {
      loading: false,
      error: null,
      selectedCoffees: [],
      selectedDateRange: 'today',
      dateFrom: this.getDefaultStartDate(),
      dateTo: this.getDefaultEndDate(),
      fromDateRange: {
        startDate: this.getDefaultStartDate(),
        endDate: this.getDefaultStartDate(),
      },
      toDateRange: {
        startDate: this.getDefaultEndDate(),
        endDate: this.getDefaultEndDate()
      },
      minDate: new Date(new Date().setFullYear(new Date().getFullYear() - 3)),
      maxDate: new Date(),
      coffeeList: [],
      coffeeStats: [],
      orderStatusCounts: {
        pending: 0,
        processing: 0,
        completed: 0,
        rejected: 0,
        total: 0
      },
      timeChartData: {
        series: [],
        categories: []
      },
      timeChartOptions: {
        chart: {
          type: 'area',
          height: 400,
          toolbar: {
            show: true,
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          },
          zoom: {
            enabled: false
          },
          background: '#242c39',
          foreColor: '#fff'
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: 2
        },
        colors: ['#d17842'],
        fill: {
          type: 'gradient',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.2,
            stops: [0, 90, 100]
          }
        },
        markers: {
          size: 4,
          colors: ['#d17842'],
          strokeColors: '#fff',
          strokeWidth: 2,
          hover: {
            size: 6
          }
        },
        grid: {
          borderColor: '#333',
          strokeDashArray: 3,
          xaxis: {
            lines: {
              show: true
            }
          },
          yaxis: {
            lines: {
              show: true
            }
          }
        },
        xaxis: {
          categories: [],
          labels: {
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif',
              fontWeight: 400
            }
          },
          axisBorder: {
            show: true,
            color: '#333'
          },
          axisTicks: {
            show: true,
            color: '#333'
          }
        },
        yaxis: {
          labels: {
            formatter: function(val) {
              return Math.round(val);
            },
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif',
              fontWeight: 400
            }
          }
        },
        tooltip: {
          theme: 'dark',
          x: {
            format: 'dd MMM yyyy'
          }
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          floating: true,
          offsetY: -25,
          offsetX: -5,
          labels: {
            colors: '#fff'
          }
        }
      }
    };
  },
  mounted() {
    this.fetchInitialData();
  },
  methods: {
    escapeCssUrl,
    getDefaultStartDate() {
      // Return today's date for the default start date
      return new Date().toISOString().split('T')[0];
    },
    getDefaultEndDate() {
      return new Date().toISOString().split('T')[0];
    },
    formatDateDisplay(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toISOString().split('T')[0];
    },
    updateFromDate(range) {
      this.fromDateRange = range;
      this.dateFrom = this.formatDateDisplay(range.startDate);
    },
    updateToDate(range) {
      this.toDateRange = range;
      this.dateTo = this.formatDateDisplay(range.startDate);
    },

    handleDateRangeChange() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];

      switch(this.selectedDateRange) {
        case 'today':
          this.dateFrom = todayStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: today, endDate: today };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'yesterday':
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = yesterday.toISOString().split('T')[0];
          this.dateFrom = yesterdayStr;
          this.dateTo = yesterdayStr;
          this.fromDateRange = { startDate: yesterday, endDate: yesterday };
          this.toDateRange = { startDate: yesterday, endDate: yesterday };
          break;

        case 'last7days':
          const last7Days = new Date();
          last7Days.setDate(last7Days.getDate() - 6);
          this.dateFrom = last7Days.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last7Days, endDate: last7Days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'last30days':
          const last30Days = new Date();
          last30Days.setDate(last30Days.getDate() - 29);
          this.dateFrom = last30Days.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last30Days, endDate: last30Days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'thisMonth':
          const firstDayThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          this.dateFrom = firstDayThisMonth.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: firstDayThisMonth, endDate: firstDayThisMonth };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'lastMonth':
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          const firstDayLastMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
          const lastDayLastMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
          this.dateFrom = firstDayLastMonth.toISOString().split('T')[0];
          this.dateTo = lastDayLastMonth.toISOString().split('T')[0];
          this.fromDateRange = { startDate: firstDayLastMonth, endDate: firstDayLastMonth };
          this.toDateRange = { startDate: lastDayLastMonth, endDate: lastDayLastMonth };
          break;

        case 'custom':
          // Do nothing, let the user select custom dates
          break;
      }
    },

    async fetchInitialData() {
      try {
        // Show loading overlay
        this.loading = true;
        this.error = null;

        const params = {
          coffee_id: this.selectedCoffees.length > 0 ? this.selectedCoffees.join(',') : 'all',
          start_date: this.dateFrom,
          end_date: this.dateTo
        };

        // Execute both API calls in parallel
        const [coffeeResponse, analyticsResponse] = await Promise.all([
          request.get('/menu'),
          request.get('/order/analytics', { params })
        ]);

        // Process coffee list data
        this.coffeeList = coffeeResponse.data.data.menus;

        // Process analytics data
        const data = analyticsResponse.data.data;
        this.coffeeStats = data.coffeeStats;
        this.orderStatusCounts = data.orderStatusCounts;
        this.timeChartData = data.timeChartData;

        // Update chart options with categories
        if (data.timeChartData && data.timeChartData.categories) {
          this.timeChartOptions.xaxis.categories = data.timeChartData.categories;
          // Force chart update to ensure categories are displayed correctly
          if (this.$refs.timeChart && this.$refs.timeChart.chart) {
            this.$refs.timeChart.chart.updateOptions({
              xaxis: {
                categories: data.timeChartData.categories
              }
            });
          }
        }

        // Hide loading overlay after a slight delay for better UX
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = 'Failed to load data. Please try again.';
        this.loading = false;
      }
    },

    fetchAnalytics() {
      // Show loading overlay
      this.loading = true;
      this.error = null;

      const params = {
        coffee_id: this.selectedCoffees.length > 0 ? this.selectedCoffees.join(',') : 'all',
        start_date: this.dateFrom,
        end_date: this.dateTo
      };

      request.get('/order/analytics', { params })
        .then(response => {
          const data = response.data.data;
          this.coffeeStats = data.coffeeStats;
          this.orderStatusCounts = data.orderStatusCounts;
          this.timeChartData = data.timeChartData;

          // Update chart options with categories
          if (data.timeChartData && data.timeChartData.categories) {
            this.timeChartOptions.xaxis.categories = data.timeChartData.categories;
            // Force chart update to ensure categories are displayed correctly
            if (this.$refs.timeChart && this.$refs.timeChart.chart) {
              this.$refs.timeChart.chart.updateOptions({
                xaxis: {
                  categories: data.timeChartData.categories
                }
              });
            }
          }

          // Hide loading overlay after a slight delay for better UX
          setTimeout(() => {
            this.loading = false;
          }, 500);
        })
        .catch(error => {
          console.error('Error fetching analytics:', error);
          this.error = 'Failed to load analytics data. Please try again.';
          this.loading = false;
        });
    }
  },
  computed: {
    coffeeOptions() {
      if (!this.coffeeList || this.coffeeList.length === 0) {
        return [];
      }

      return this.coffeeList.map(coffee => ({
        label: coffee.name,
        value: coffee.id
      }));
    },
    dateRangeOptions() {
      return [
        { label: 'Today', value: 'today' },
        { label: 'Yesterday', value: 'yesterday' },
        { label: 'Last 7 Days', value: 'last7days' },
        { label: 'Last 30 Days', value: 'last30days' },
        { label: 'This Month', value: 'thisMonth' },
        { label: 'Last Month', value: 'lastMonth' },
        { label: 'Custom Range', value: 'custom' }
      ];
    }
  }
};
</script>

<style scoped>
.analytics-page {
  padding: 0 15px 30px;
}

.analytics-filters {
  background-color: #242c39;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  color: #fff;
}

.analytics-filters label {
  color: #d17842;
  font-weight: 500;
  margin-bottom: 8px;
}

.analytics-filters .form-control {
  background-color: #1e222a;
  border: 1px solid #242c39;
  color: #fff;
  border-radius: 8px;
}


.analytics-filters .analytics-filters-bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
}

.analytics-filters .btn-primary {
  background-color: #d17842;
  border-color: #d17842;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
}

.analytics-filters .btn-primary:focus, .analytics-filters .btn-primary:not(.disabled):active {
  background-color: #d17842;
  border-color: #d17842;
}

.analytics-filters .btn-secondary {
  background-color: #242c39;
  border-color: #333;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  color: #d17842;
  transition: all 0.2s;
}

.analytics-filters .btn-secondary:hover {
  background-color: #1a2130;
  color: #fff;
}

.date-range-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
}

.date-picker-container .reportrange-text {
  background-color: #242c39 !important;
  color: #fff;
  border-radius: 8px;
  padding: 10px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.date-picker-container .reportrange-text::after {
  content: '\1F4C5';
  margin-left: auto;
  font-size: 10px;
  color: #d17842;
}

.date-form-group {
  flex: 1;
  min-width: 200px;
}

.date-label-container {
  margin-bottom: 8px;
}

.date-picker-container {
  position: relative;
}

.selected-date-option, .date-option {
  padding: 5px 0;
}

.filter-actions {
  display: flex;
  align-items: flex-end;
}


.custom-select-container {
  width: 100%;
}

.custom-date-input {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #242c39;
  border-radius: 8px;
  background-color: #1e222a;
  cursor: pointer;
  color: #fff;
}

.custom-date-input i {
  margin-right: 8px;
  color: #d17842;
}

.status-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: space-between;
}
.status-item-wrapper {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: space-between;
}

.status-item {
  flex: 1;
  min-width: 150px;
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #1e222a;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.status-details h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
}

.status-details p {
  margin: 5px 0 0;
  color: #d17842;
}

.coffee-thumb {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  margin-right: 10px;
}

.card {
  background-color: #242c39;
  border: none;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.custom-card-header {
  border-bottom: 1px solid #333;
  padding: 15px 20px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.custom-card-header h5 {
  color: #d17842;
  font-weight: 600;
  margin: 0;
  font-size: 18px;
}

.card-body {
  padding: 20px;
  color: #fff;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.table {
  color: #fff;
}

.table th {
  border-top: none;
  border-bottom: 2px solid #333;
  color: #d17842;
  text-align: center;
}

.table td {
  border-top: 1px solid #333;
  vertical-align: middle;
  text-align: center;
}

/* Make coffee name column wider */
.table-responsive th:first-child,
.table-responsive td:first-child {
  min-width: 150px;
  text-align: left;
}

.full-page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader-content {
  text-align: center;
  color: #fff;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-top: 5px solid #d17842;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ApexCharts Download Button Styling */
:deep(.apexcharts-menu-icon) {
  fill: #d17842 !important;
}

:deep(.apexcharts-menu) {
  background-color: #242c39 !important;
  border: 1px solid #333 !important;
}

:deep(.apexcharts-menu-item) {
  color: #fff !important;
}

:deep(.apexcharts-menu-item:hover) {
  background-color: #d17842 !important;
}

.copyright-footer {
  text-align: center;
  padding: 15px 0;
  color: #6c757d;
}
</style>

<style>
/* Vue-select custom styling */
.v-select .vs__selected-options {
  padding: 10px !important;
}

.v-select .vs__no-options {
  color: #fff;
}

.custom-select-container .v-select .vs__selected {
  background-color: #d17842;
  color: #fff;
  padding: 5px 10px !important;
  margin: 0 5px 5px 0;
  border-radius: 15px;
  font-weight: 500;
}

.v-select .vs__deselect {
  fill: #fff;
  margin-left: 8px;
}

.vs__dropdown-option--highlight {
  background-color: #d17842;
}

/* Date dropdown styling */
.date-dropdown .v-select {
  background-color: #0c0f14;
  border-radius: 8px;
  font-family: 'DM Sans', sans-serif;
  position: relative;
  z-index: 100;
}

.vs__dropdown-toggle {
  border: 1px solid #242c39;
  border-radius: 8px;
  background-color: #0c0f14;
  padding-right: 10px;
}

/* Date Range Picker Global Styles */
.daterangepicker {
  background-color: white !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  color: #333 !important;
  font-family: 'DM Sans', sans-serif !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important;
  padding: 15px !important;
  width: auto !important;
  min-width: 300px !important;
}

/* Date display styling to match form control */
.date-picker-container .reportrange-text {
  background-color: #242c39 !important;
  color: #fff;
  border-radius: 8px;
  padding: 10px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.date-picker-container .reportrange-text::after {
  content: '\1F4C5';
  margin-left: auto;
  font-size: 10px;
  color: #d17842;
}

.apexcharts-menu-item {
  color: #000000;
}

.apexcharts-theme-light .apexcharts-menu-item:hover {
  background: #d17842 !important;
  color: #fff;
}

.table-dark {
  background-color: #242c39;
  color: #fff;
}

.table-dark th {
  border-color: #333;
}
</style>

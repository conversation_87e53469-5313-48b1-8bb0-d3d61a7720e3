<template>
    <div id="menu-page">
        <div class="page__header">
            <div class="left__content">
                <router-link to="/" class="back__button">
                    <i class="wpc-icon wpc-angle-left"></i> Back
                </router-link>
            </div>
            <div class="page__title"><h5>Coffee Menu</h5></div>
            <div class="right__content"></div>
        </div>

        <Maintenance v-if="isMaintenanceMode" />
        <div class="item__wrap flex__wrap">
            <router-link :to="{name:'order',params:{id:menu.id,coupon:menu.availableCoupon}}"
                         v-for="(menu, index) in data"
                         :key="index"
                         :class="['item',{'is__disabled': isMaintenanceMode || !menu.is_active}]"
            >
                <div class="item__thumb bgi__property"
                     :style="{backgroundImage: 'url(' + escapeCssUrl(menu.thumbnail) + ')'}">
                    <div class="item__available__count">
                        <div class="icon"><i class="wpc-icon wpc-coffee-cup"></i></div>
                        <p>{{ menu.availableCoupon }}</p>
                    </div>
                  <div class="item__required__token__count">
                    <img src="/assets/img/coffee-coupon.svg"  alt="coffee-coupon-icon"/>
                    <p>{{ menu.required_coupon }}</p>
                  </div>
                </div>
                <div class="item__body">
                    <h3 class="item__name">{{ menu.name }}</h3>
                    <p class="item__description">{{ menu.description }}</p>
                    <h5 v-show="menu.availableCoupon"><span>{{ menu.availableCoupon }}</span> Available Coupons</h5>
                    <h5 class="item__not__available" v-show="menu.availableCoupon == 0">No Coupon Available</h5>
                </div>
            </router-link>
        </div>
    </div>
</template>

<script>
import {request} from "../../helpers/request";
import Maintenance from "../components/Maintenance";
import { escapeCssUrl } from "../../helpers/utils";

export default {
    name: "CoffeeMenuComponent",

    data() {
        return {
            data: [],
            isMaintenanceMode : false,
        }
    },

    components : {
        Maintenance,
    },
  methods : {
    escapeCssUrl
  },

    mounted: function () {
        let loader = this.$loading.show({
            loader: 'spinner',
            container: document.getElementById("menu-page"),
            canCancel: true,
            onCancel: this.onCancel,
            color: '#d17842',
            backgroundColor: '#000000',
            height: 80,
            width: 80
        });
        request.get('/menu').then(({data}) => {
            this.data = data.data.menus
            this.isMaintenanceMode = data.is_maintenance
        }).catch((error) => {
            console.log(error);
        }).finally(() => {
            loader.hide();
        });
    },
}
</script>

<style scoped>
  .item.is__disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  .item .item__thumb .item__required__token__count {
    background-color: rgba(30, 34, 42, 0.8);
    color: #fff;
    font-weight: 600;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    padding: 6px 12px;
    border-bottom-right-radius: 20px;
  }
  .item .item__thumb .item__required__token__count img {
    color: #d17842;
    margin-right: 5px;
  }
</style>

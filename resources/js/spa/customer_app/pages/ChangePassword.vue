<template>
    <div>
        <div class="page__header">
            <div class="left__content">
                <router-link to="/" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
            </div>
            <div class="page__title">
                <h5>Change Password</h5>
            </div>
            <div class="right__content"></div>
        </div>
        <div class="edit__profile__form">
            <form
                method="post"
                @submit.prevent="updateProfile()"
            >
                <div class="form__group">
                    <label for="old-pass">Old password</label>
                    <input :type="showPass ? 'text' : 'password'" id="old-pass" class="form-control" v-model="password.old_password">
                    <p v-if="errors.old_password && errors.old_password.length > 0" class="validation-message">{{errors.old_password[0]}}</p>
                </div>
                <div class="form__group">
                    <label for="new-pass">New Password</label>
                    <input :type="showPass ? 'text' : 'password'" id="new-pass" class="form-control" v-model="password.new_password">
                    <p v-if="errors.new_password && errors.new_password.length > 0" class="validation-message">{{errors.new_password[0]}}</p>
                </div>
                <div class="form__group">
                    <label for="confirm-pass">Confirm Password</label>
                    <input :type="showPass ? 'text' : 'password'" id="confirm-pass" class="form-control" v-model="password.confirm_password">
                    <p v-if="errors.confirm_password && errors.confirm_password.length > 0" class="validation-message">{{errors.confirm_password[0]}}</p>
                </div>
<!--                <div class="show-hide-checkbox">-->
<!--                    <label>show password</label>-->
<!--                    <input type="checkbox" :checked="showPass" @change="toggleShowPass()" />-->
<!--                </div>-->
                <button class="WpcButton WpcFilled" :disabled="loading">
                    <span v-if="loading" class="spinner-border spinner-border-sm text-white" role="status"
                          aria-hidden="true"></span>&nbsp;
                    Save Changes
                </button>
            </form>
        </div>

        <div class="footer">
            <p>&copy; All Right Reserved</p>
        </div>
    </div>
</template>

<script>
import { request } from "../../helpers/request";

export default {
    name: "ChangePassword",
    data() {
        return {
            errors: [],
            password: {
                old_password: "",
                new_password: "",
                confirm_password: "",
            },
            loading: false,
            showPass: false,
        }
    },
    methods: {
        updateProfile() {
            this.errors = [];
            this.loading = true;
            let formData = new FormData();

            for (let key in this.password) {
                formData.append(key, this.password[key]);
            }

            request.post('/change-password', formData).then((response) => {
                this.$toastr.s("success", response.data.message);
            })
                .then(() => {
                    this.password = {
                        old_password: "",
                        new_password: "",
                        confirm_password: "",
                    };
                    this.$router.push({name: 'home'});
                })
                .catch((error) => {
                    if (error.response.status === 422) {
                        this.errors = error.response.data.errors;
                    } else {
                        this.$toastr.e("error", error.response.data.message);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        toggleShowPass(){
            this.showPass = !this.showPass;
        }
    },
}
</script>

<style scoped>
.validation-message {
    color: red;
}
.show-hide-checkbox {
    margin: 10px;
    font-size: 17px;
}
</style>

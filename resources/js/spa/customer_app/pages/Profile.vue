<template>
  <div>
    <div class="page__header">
      <div class="left__content">
        <router-link to="/" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
      </div>
      <div class="page__title">
        <h5>Edit Profile</h5>
      </div>
      <div class="right__content"></div>
    </div>
    <div class="edit__profile__form">
      <form method="post" @submit.prevent="updateProfile(user.id)">
        <div class="user__thumb__change">
          <div class="user__thumb bgi__property" :style="{ backgroundImage : `url(${escapeCssUrl(userData.avatar_url)})`}">

          </div>
          <label class="WpcEditButton">
            <input type="file" name="avatar" accept="image/jpeg, image/png, image/gif" @change="onImageChange" class="d-none">
            <span class="Icon">
								  <i class="wpc-icon wpc-edit"></i>
								</span>
            <span class="Text">Change Photo</span>
          </label>
        </div>
        <div class="form__group">
          <label for="inputName">Name</label>
          <input type="text" id="inputName" v-model="userData.name" class="form-control">
        </div>
        <div class="form__group">
          <label for="inputDeg">Designation</label>
          <input type="text" id="inputDeg" v-model="userData.designation" class="form-control">
        </div>
        <div class="form__group">
          <label for="inputMail">Email</label>
          <input type="email" id="inputMail" v-model="userData.email" class="form-control">
        </div>
<!--        <div class="form__group">-->
<!--          <label for="inputPass">Password</label>-->
<!--          <input type="password" id="inputPass" v-model="userData.password" class="form-control">-->
<!--        </div>-->
        <button class="WpcButton WpcFilled">Save Change</button>
      </form>
    </div>

    <div class="footer">
      <p>&copy; All Right Reserved</p>
    </div>
  </div>
</template>

<script>
import {request} from "../../helpers/request";
import {mapActions, mapState} from "vuex";
import {LOGIN, UPDATE_USER} from "../../store/actions";
import { escapeCssUrl } from "../../helpers/utils";

export default {
  name: "ProfileComponent",
  data() {
    return {
      userData: {
        name: '',
        designation: '',
        email: '',
        // password: '',
        avatar: null,
        avatar_url: null,
      },
    }
  },

  computed: {
    ...mapState(['user']),
  },


  mounted() {
    this.userData.name = this.user.name;
    this.userData.designation = this.user.designation;
    this.userData.email = this.user.email;
    // this.userData.password = this.user.password;
    this.userData.avatar_url = this.user.avatar
  },
  methods: {
    escapeCssUrl,
    ...mapActions([UPDATE_USER]),
    onImageChange(e) {
      const file = e.target.files[0];
      if (file) {
        const validImageTypes = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif']; // Add more types if needed
        if (validImageTypes.includes(file.type)) {
          this.userData.avatar = e.target.files[0];
          this.userData.avatar_url = URL.createObjectURL(this.userData.avatar);
        } else {
          this.$toastr.w("Please select a JPG, JPEG, PNG, or GIF file.", "Invalid image file type");
        }
      }

    },
    updateProfile(id) {

      let formData = new FormData();

      for (let key in this.userData) {
        formData.append(key, this.userData[key]);
      }

      request.post(`/user/${id}`, formData).then((response) => {
        this.updateData();
        this.$toastr.s("success", response.data.message);
      }).catch((error) => {

      })
    },
    updateData() {
      // this.user.name = this.userData.name;
      // this.user.designation = this.userData.designation;
      // this.user.email = this.userData.email;
      // this.user.password = this.userData.password;
      // this.user.avatar = this.userData.avatar_url;
      const updatedData = {
        name: this.userData.name,
        email: this.userData.email,
        designation: this.userData.designation,
        // password: this.userData.password,
        avatar: this.userData.avatar_url,
      }

      this.UPDATE_USER(updatedData);
    }
  },
}
</script>

<style scoped>

</style>

<template>
    <div>

        <div class="page__header">
            <div class="left__content">
                <router-link to="/" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
            </div>
            <div class="page__title">
                <h5>Coffee conversion</h5>
            </div>
            <div class="right__content">

            </div>
        </div>
        <div class="pb-3">
            <div class="coffee__convertion">
                <div class="convertion convertion__from">
                    <div class="convertion__header">
                        <p>From</p>
                        <div class="WpcFilterSelectorWrapper">
                            <select v-model="conversion_from" class="WpcFilterSelector"
                                    @change="filteredToMenus(conversion_from)">
                                <option selected value="">Choose a coffee</option>
                                <option :value="menu" v-for="menu in data">{{ menu.name }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="selected__item" v-for="menu in data">
                        <div class="item" v-if="menu.id == conversion_from.id">
                            <div class="item__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${menu.thumbnail})`}">

                            </div>
                            <div class="item__body">
                                <h3 class="item__name">{{ menu.name }}</h3>
                                <div class="item__body__bottom">
                                    <div class="item__available__count">
                                        <div class="icon">
                                            <i class="wpc-icon wpc-coffee-cup"></i>
                                        </div>
                                        <p>{{ fromCalculatedCoffee }}</p>
                                    </div>
                                    <div class="WpcIncDecButtonGroup">
                                        <button class="WpcDecButton" @click="sub()">
                                            <i class="wpc-icon wpc-minus"></i>
                                        </button>
                                        <input class="WpcIncDecInput" :value="fromQuantity" type="text">
                                        <button class="WpcIncButton" @click="add()" :disabled="conversion_to ==''">
                                            <i class="wpc-icon wpc-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="switch__sec">
                    <span><img src="assets/img/down-arrow.svg" alt=""></span>
                </div>
                <div class="convertion convertion__to">
                    <div class="convertion__header">
                        <p>To</p>
                        <div class="WpcFilterSelectorWrapper">
                            <select v-model="conversion_to" class="WpcFilterSelector" name="conversion_to"
                                    @click="resetConversionToValues">
                                <option selected value="">Choose a coffee</option>
                                <option :value="menu" v-for="menu in FilteredMenus">{{ menu.name }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="selected__item mb-4" v-for="menu in data">
                        <div class="item" v-if="menu.id == conversion_to.id">
                            <div class="item__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${menu.thumbnail})`}">

                            </div>
                            <div class="item__body">
                                <h3 class="item__name">{{ menu.name }}</h3>
                                <div class="item__body__bottom">
                                    <div class="item__available__count">
                                        <div class="icon">
                                            <i class="wpc-icon wpc-coffee-cup"></i>
                                        </div>
                                        <p>{{ toCalculatedCoffee }}</p>
                                    </div>
                                    <div class="WpcIncDecButtonGroup">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="subConversionSectionShower" class="text-center">
                        <span class="wpc-icon wpc-plus"></span>
                    </div>
                    <div v-if="subConversionSectionShower" class="extra-conversion-section">
                        <div class="convertion convertion__to">
                            <div class="selected__item" v-for="menu in data" v-if="conversion_from && conversion_to">
                                <div class="item" v-if="menu.id == conversion_to.id">
                                    <div class="item__thumb bgi__property"
                                         :style="{ backgroundImage : `url(${sub_conversion_to.thumbnail})`}">

                                    </div>
                                    <div class="item__body">
                                        <div class="item__body__bottom">
                                            <div class="item__available__count">
                                                <div class="icon">
                                                    <i class="wpc-icon wpc-coffee-cup"></i>
                                                </div>
                                                <p>{{ calculateSubConversionCoffee }}</p>
                                            </div>
                                            <div class="WpcFilterSelectorWrapper item__name">
                                                <select v-model="sub_conversion_to" class="WpcFilterSelector">
                                                    <option :value="menu" v-for="(menu) in subConversionMenus">
                                                        {{ menu.name }}
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="WpcIncDecButtonGroup">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button v-if="buttonSpinner" type="submit" class="WpcButton WpcFilled">&nbsp;
                    <span class="spinner-border spinner-border-sm text-white" role="status" aria-hidden="true"></span>
                    Submit
                </button>
                <button v-else href="#" type="submit" class="WpcButton WpcFilled"
                        :disabled="conversion_from.availableCoupon == null || toCalculatedCoffee == 0
                || conversion_from.id == conversion_to.id || fromQuantity == 0 || conversion_to == ''"
                        @click="saveConversion">Submit
                </button>
            </div>
        </div>
        <div class="footer">
            <p>&copy; All Right Reserved</p>
        </div>

    </div>
</template>

<script>
import {request} from "../../helpers/request";

export default {
    name: "CoffeeConversion",

    data() {
        return {
            data: [],
            FilteredMenus: {},
            conversion_from: '',
            conversion_to: '',
            fromQuantity: 0,
            subConversionMenus: null,
            sub_conversion_to: null,
            buttonSpinner : false
        }
    },

    mounted() {
        this.fromMenuDetails();
    },

    computed: {
        fromCalculatedCoffee() {
            let calculatedCoffee = this.conversion_from.availableCoupon;
            if (Number.isInteger(calculatedCoffee)) {
                return calculatedCoffee;
            } else {
                return parseFloat(calculatedCoffee).toFixed(1);
            }
        },
        toCalculatedCoffee() {
            let fromCalculatedCoupons = this.fromQuantity * this.conversion_from.required_coupon; //calculated the coupons coming from 'from section'
            return Math.floor(fromCalculatedCoupons / this.conversion_to.required_coupon);  //converted them to 'to' coffee
        },
        calculateRemainingCoupon() {
            return Number((this.fromQuantity * this.conversion_from.required_coupon) - (this.toCalculatedCoffee * this.conversion_to.required_coupon));
        },
        calculateSubConversionCoffee() {
            return this.calculateRemainingCoupon / this.sub_conversion_to?.required_coupon
        },
        subConversionSectionShower() {
            return !!(this.calculateRemainingCoupon && this.sub_conversion_to !== null);

        }
    },

    methods: {
        fromMenuDetails() {
            request.get('/coffee/conversion').then(({data: {data}}) => {
                this.data = data;
            }).catch((error) => {
                console.log(error);
            })
        },

        add() {
            // console.log(Math.floor(this.conversion_from.availableCoupon))
            if (this.fromQuantity >= 0 && this.conversion_from.availableCoupon > 0) {
                if (this.conversion_from.availableCoupon < 1 && this.conversion_from.availableCoupon > 0) {
                    this.fromQuantity += this.conversion_from.availableCoupon;
                    this.conversion_from.availableCoupon -= this.conversion_from.availableCoupon;
                } else {
                    this.conversion_from.availableCoupon -= 1; //this one decreases the value of total coffee coming from database and the computed methods invokes
                    this.fromQuantity += 1;
                }
                this.remainingMenuDetails();
            }
        },

        sub() {
            if (this.fromQuantity >= 1) {
                this.fromQuantity -= 1;
                this.conversion_from.availableCoupon += 1; //this one increases the value of total coffee coming from database and the computed methods invokes
                this.remainingMenuDetails();
            }
            if (this.fromQuantity > 0 && this.fromQuantity < 1) {
                this.conversion_from.availableCoupon += this.fromQuantity;
                this.fromQuantity -= this.fromQuantity;
                this.remainingMenuDetails();
            }
        },

        saveConversion() {
            this.buttonSpinner = true;
            request.post('/coffee/conversion/save', {
                from_coffee_id: this.conversion_from.id,
                from_quantity: this.fromQuantity,
                to_coffee_id: this.conversion_to.id,
                to_coffee_quantity: this.toCalculatedCoffee,
                to_coffee_required_coupon: this.conversion_to.required_coupon,
                extra_coffee: this.sub_conversion_to ? this.sub_conversion_to : null,
                extra_coffee_quantity: this.calculateSubConversionCoffee ? this.calculateSubConversionCoffee : null,
            })
                .then(({data: {data}}) => {
                    this.data = data;
                    this.reset();
                    this.buttonSpinner = false
                    this.$toastr.s('success', 'Coffee has been converted successfully');
                }).catch((error) => {
                console.log(error);
            });
        },

        remainingMenuDetails() {
            let filteredByCoupon = this.data.filter(remainingMenu => remainingMenu.required_coupon === this.calculateRemainingCoupon)
            let subConversionAble = filteredByCoupon.filter(menu => menu.id !== this.conversion_from.id); //then checked if the menu is equal to the 'from' section
            if (subConversionAble.length >= 1) {
                this.subConversionMenus = subConversionAble;
                this.sub_conversion_to = this.subConversionMenus[0];
            } else {
                this.sub_conversion_to = null;
            }
        },

        filteredToMenus(menu) {
            this.conversion_to = '';
            this.fromQuantity = 0;
            this.FilteredMenus = this.data.filter(toFilteredMenu => toFilteredMenu.id !== menu.id);
        },
        reset() {
            this.FilteredMenus = {};
            this.conversion_from = '';
            this.conversion_to = '';
            this.subConversionMenus = null;
            this.sub_conversion_to = null;
            this.fromQuantity = 0;
            this.fromMenuDetails();
        },
        resetConversionToValues() {
            const quantity = this.fromQuantity;
            this.conversion_from.availableCoupon += quantity;
            this.fromQuantity = 0;
        }
    }

}
</script>

<style scoped>

</style>

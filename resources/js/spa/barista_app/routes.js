import Barista from "./pages/Home";
import BaristaNotification from "./pages/Notification";
import BaristaCoffeeMenu from "./pages/CoffeeMenu";
import BaristaCustomRequest from './pages/CustomRequest';
import BaristaOrderHistory from "./pages/OrderHistory";
import BaristaOrderAnalytics from "./pages/Analytics.vue";
import BaristaProfile from "./pages/Profile";

const routes = [
    {
        path:'/',
        component:Barista,
        name:'barista',
        meta: {
            title: 'Barista App',
            barista:true
        }
    },
    {
        path:'notification',
        component:BaristaNotification,
        meta: {
            title: 'Barista Notification',
            barista:true
        }
    },
    {
        path:'coffee',
        component:BaristaCoffeeMenu,
        meta: {
            title: 'Barista Coffee',
            barista:true
        }
    },
    {
        path:'custom-request',
        component:BaristaCustomRequest,
        meta: {
            title: 'Barista Custom Request',
            barista:true
        }
    },
    {
        path:'order-history',
        component:BaristaOrderHistory,
        meta: {
            title: 'Barista Order History',
            barista:true
        }
    },
    {
        path:'order-analytics',
        component:BaristaOrderAnalytics,
        meta: {
            title: 'Order Analytics',
            barista:true
        }
    },
    {
        path:'profile',
        component:BaristaProfile,
        meta: {
            title: 'Barista Profile',
            barista:true
        }
    },
]

export default routes;

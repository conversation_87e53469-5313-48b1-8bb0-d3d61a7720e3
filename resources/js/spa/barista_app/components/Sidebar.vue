<template>
<div>
  <div class="wpc-navigation__drawer__wrap" v-bind:class="{ show: sidebar}">
    <div class="wpc-navigation__drawer">
      <a href="#" @click.prevent="sidebarClose(false)" class="navigation__drawer__closer">
        <i class="wpc-icon wpc-times"></i>
      </a>
      <div class="navigation__drawer__header">
        <div class="navigation__drawer__user">
          <div class="navigation__drawer__user__thumb bgi__property" :style="{backgroundImage: 'url(' + escapeCssUrl(getUser.avatar) + ')',}">
          </div>
          <div class="navigation__drawer__user__body">
            <h4 class="user__name">{{getUser.name}}</h4>
            <p class="designation">{{getUser.designation}}</p>
          </div>
        </div>
        <div class="navigation__drawer__user__edit">
          <router-link to="/barista/profile"><i class="wpc-icon wpc-edit"></i></router-link>
        </div>
      </div>
      <ul class="navigation__menu">
        <li class="navigation__list">
          <router-link to="/barista/notification" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-notification"></i>
									</span>
            <span class="text">Notification</span>
          </router-link>
        </li>

        <li class="navigation__list">
          <router-link to="/barista/coffee" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-menu"></i>
									</span>
            <span class="text">Coffee Menu</span>
          </router-link>
        </li>
        <li class="navigation__list">
          <router-link to="/barista/custom-request" class="navigation__item">
								<span class="icon">
									<i class="wpc-icon wpc-order"></i>
								</span>
            <span class="text">Custom Request</span>
          </router-link>
        </li>
        <li class="navigation__list">
          <router-link to="/barista/order-history" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-order"></i>
									</span>
            <span class="text">Order History</span>
          </router-link>
        </li>
        <li class="navigation__list">
          <router-link to="/barista/order-analytics" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-menu"></i>
									</span>
            <span class="text">Order Analytics</span>
          </router-link>
        </li>
        <li class="navigation__list">
          <router-link to="/" class="navigation__item">
									<span class="icon">
										<i class="wpc-icon wpc-order"></i>
									</span>
            <span class="text">Switch to Customer</span>
          </router-link>
        </li>
      </ul>
      <ul class="navigation__menu mt-auto">
        <li class="navigation__list">
          <a href="#" class="navigation__item" @click.prevent="logout()">
									<span class="icon">
										<i class="wpc-icon wpc-logout"></i>
									</span>
            <span class="text">Log Out</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
</template>

<script>
import {mapActions, mapState} from "vuex";
import {LOGIN, TOGGLE_SIDEBAR_BARISTA} from "../../store/actions";
import {request} from "../../helpers/request";
import { escapeCssUrl } from "../../helpers/utils";

export default {
  name: "Sidebar",
  computed:{
    ...mapState(['baristaSidebarVisible']),
    ...mapState(['user']),

    sidebar(){
      return this.baristaSidebarVisible;
    },
    getUser(){
      return this.user;
    }
  },

  methods:{
    escapeCssUrl,
    ...mapActions([TOGGLE_SIDEBAR_BARISTA]),
    ...mapActions([LOGIN]),
    sidebarClose(data){
      return this.TOGGLE_SIDEBAR_BARISTA(data);
    },

    logout(){
      request.post('/logout').then(response=>{
        window.localStorage.removeItem('wpCafeAuthToken');
        this.LOGIN({});
        this.$router.push({name: 'login'});
      }).catch(error=>{
        console.log(error);
      });


    }
  },
}
</script>

<style>
.wpc-navigation__drawer {
  overflow-y: auto;
}
</style>


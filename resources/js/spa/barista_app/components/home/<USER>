<template xmlns="http://www.w3.org/1999/html">
    <div class="order__queue">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="pending-tab" data-toggle="tab" href="#pending" role="tab"
                   aria-controls="pending" aria-selected="true">Pending Requests({{ pendingRequests }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="processing-tab" data-toggle="tab" href="#processing" role="tab"
                   aria-controls="processing" aria-selected="false">Processing Requests({{ processingRequests }})</a>
            </li>
        </ul>
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                <div class="tap__to__order__page">
                    <a type="button" @click="refreshPage()"><i class="wpc-icon wpc-exchange"></i></a>&nbsp;&nbsp; &nbsp;
                    <router-link to="/barista/coffee"><i class="wpc-icon wpc-menu"></i></router-link>
                </div>
                <div class="ordered__menu">

                    <div class="ordered__item" v-for="(order,index) in pageState.orders"
                         v-if="order.status == 'pending'" :key="index + order.id">
                        <div class="menu__details">
                            <div class="ordered__item__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${escapeCssUrl(order.menu.thumbnail)})`}">

                            </div>
                            <div class="orderer__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${escapeCssUrl(order.user.avatar)})`}">

                            </div>
                            <div class="orderer__info">
                                <h5>{{ order.user.name }}</h5>
                                <span>{{ order.user.designation }}</span>
                            </div>
                        </div>
                        <div class="order__info">
                            <div class="order__info__details">
                                <h5>{{ order.menu.name }}</h5>
                                <span class="order__id">Requested at {{ moment(order.created_at) }}</span>
                                <span class="order__id"><b>Serial No #{{ order.serial_number }}</b> &nbsp; ({{order.time_changed_format}})</span>
                                <span v-if="order.for_guest === 1"><b>(for guest)</b></span>
                                <span v-if="order.is_espresso_double_shot === 1" class="double-shot-badge">Espresso- Double Shots</span>
                            </div>
                            <div class="order__info__button">
                                <a href="#" v-if="defaultButtonCancel" class="WpcButton mr-1"
                                   @click="cancelPendingOrder(order.id,index)">Cancel</a>
                                <a href="#" v-else-if="clickedButton === index" type="button" disabled
                                   class="WpcButton mr-1">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                    Cancel
                                </a>
                                <a href="#" v-else class="WpcButton mr-1" @click="cancelPendingOrder(order.id,index)">Cancel</a>

                                <a href="#" v-if="defaultButton" class="WpcButton WpcFilled"
                                   @click="processOrder(order.id,index)">Mark
                                    as Processing</a>
                                <a href="#" v-else-if="clickedButton === index" type="button" disabled
                                   class="WpcButton WpcFilled">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                    Mark as Processing
                                </a>
                                <a href="#" v-else class="WpcButton WpcFilled" @click="processOrder(order.id,index)">Mark
                                    as
                                    Processing</a>
                            </div>
                        </div>
                        <div class="order__notes" v-if="order.note">
                            <p>{{ order.note }}</p>
                        </div>
                        <div class="order__info__responsive">
                            <a href="#" v-if="defaultButtonCancel" class="WpcButton cancel mr-1"
                               @click="cancelPendingOrder(order.id,index)">Cancel</a>
                            <a href="#" v-else-if="clickedButton === index" type="button" disabled
                               class="WpcButton cancel mr-1">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                Cancel
                            </a>
                            <a href="#" v-else class="WpcButton cancel mr-1" @click="cancelPendingOrder(order.id,index)">Cancel</a>

                            <a href="#" v-if="defaultButton" class="WpcButton WpcFilled"
                               @click="processOrder(order.id,index)">Mark
                                as Processing</a>
                            <a href="#" v-else-if="clickedButton === index" type="button" disabled
                               class="WpcButton WpcFilled">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                Mark as Processing
                            </a>
                            <a href="#" v-else class="WpcButton WpcFilled" @click="processOrder(order.id,index)">Mark
                                as
                                Processing</a>
                        </div>
                    </div>

                </div>
            </div>
            <div class="tab-pane fade" id="processing" role="tabpanel" aria-labelledby="processing-tab">
              <a type="button" href="#"
                 v-show="processingRequests"
                 @click="showCompleteMessage"
                 class="WpcButton WpcFilled">Complete All
              </a>
                <div class="tap__to__order__page">
                    <router-link to="/barista/coffee"><i class="wpc-icon wpc-menu"></i></router-link>
                </div>
                <div class="ordered__menu">
                    <div class="ordered__item" :key="index + order.id" v-for="(order,index) in orders" v-if="order.status == 'processing'">
                        <div class="menu__details">
                            <div class="ordered__item__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${escapeCssUrl(order.menu.thumbnail)})`}">

                            </div>
                            <div class="orderer__thumb bgi__property"
                                 :style="{ backgroundImage : `url(${escapeCssUrl(order.user.avatar)})`}">

                            </div>
                            <div class="orderer__info">
                                <h5>{{ order.user.name }}</h5>
                                <span>{{ order.user.designation }}</span>
                            </div>
                        </div>
                        <div class="order__info">
                            <div class="order__info__details">
                                <h5>{{ order.menu.name }}</h5>
                                <span class="order__id">Requested at {{ moment(order.created_at) }}</span>
                                <span class="order__id"><b>Serial No - {{ order.serial_number }}</b> &nbsp; ({{order.time_changed_format}})</span>
                                <span v-if="order.for_guest === 1"><b>(for guest)</b></span>
                                <span v-if="order.is_espresso_double_shot === 1" class="double-shot-badge">Espresso- Double Shots</span>
                            </div>
                            <div class="order__info__button">
                                <a href="#" v-if="defaultButtonCancel" class="WpcButton mr-1"
                                   @click="cancelProcessingOrder(order.id,index)">Cancel</a>
                                <a href="#" v-else-if="clickedButton === index" type="button" disabled
                                   class="WpcButton mr-1">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>Cancel</a>
                                <a href="#" v-else class="WpcButton mr-1"
                                   @click="cancelProcessingOrder(order.id,index)">Cancel</a>

                                <a v-if="defaultButton" href="#" class="WpcButton WpcFilled"
                                   @click="confirmOrder(order.id,index)">Mark
                                    as Delivered</a>
                                <a v-else-if="clickedButton === index" type="button" disabled href="#"
                                   class="WpcButton WpcFilled">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                    Mark as Delivered</a>
                                <a v-else href="#" class="WpcButton WpcFilled" @click="confirmOrder(order.id,index)">Mark
                                    as
                                    Delivered</a>
                            </div>
                        </div>
                        <div class="order__notes" v-if="order.note">
                            <p>{{ order.note }}</p>
                        </div>
                        <div class="order__info__responsive">
                            <a href="#" v-if="defaultButtonCancel" class="WpcButton cancel mr-1"
                               @click="cancelProcessingOrder(order.id,index)">Cancel</a>
                            <a href="#" v-else-if="clickedButton === index" type="button" disabled
                               class="WpcButton cancel mr-1">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>Cancel</a>
                            <a href="#" v-else class="WpcButton cancel mr-1"
                               @click="cancelProcessingOrder(order.id,index)">Cancel</a>

                            <a v-if="defaultButton" href="#" class="WpcButton WpcFilled"
                               @click="confirmOrder(order.id,index)">Mark
                                as Delivered</a>
                            <a v-else-if="clickedButton === index" type="button" disabled href="#"
                               class="WpcButton WpcFilled">
                                    <span class="spinner-border spinner-border-sm text-white" role="status"
                                          aria-hidden="true"></span>
                                Mark as Delivered</a>
                            <a v-else href="#" class="WpcButton WpcFilled" @click="confirmOrder(order.id,index)">Mark
                                as
                                Delivered</a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {request} from "../../../helpers/request";
import {mapActions, mapState} from "vuex";
import {LOGIN, UPDATE_PAGE_STATE} from "../../../store/actions";
import moment from "moment";
import { escapeCssUrl } from "../../../helpers/utils";

export default {
    data() {
        return {
            loadProcessOrder: false,
            defaultButton: true,
            defaultButtonCancel: true,
            clickedButton: undefined,
            canceledFromPending: false,
            markedAsProcessing: false,
            markedAsDelivered: false,
        }

    },
    methods: {
      escapeCssUrl,
        ...mapActions([UPDATE_PAGE_STATE]),

      showCompleteMessage(){
        this.$swal.fire({
          title: 'Mark All Processing Orders as completed?',
          text: "All processing orders will be completed!",
          paddingBottom: '150px',
          paddingTop: '50px',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes.',
          cancelButtonText: 'No',
        }).then((result) => {
          if (result.isConfirmed) {
            this.completeAllProcessingOrders()
          }
        })
      },

      completeAllProcessingOrders() {
        request.get(`barista/complete-orders`).then(({data}) => {
          this.pageState.orders.map(order => {
            order.status = 'completed';
          })
          this.$toastr.s("success", data.message);
        }).catch((error) => {
          console.log(error);
        })
      },

        moment: function (date) {
          return moment(date).format("D MMM h:mm a");
        },

        refreshPage(){
            let loader = this.$loading.show({
                loader: 'spinner',
                container: this.fullPage ? null : this.$refs.formContainer,
                canCancel: true,
                onCancel: this.onCancel,
                color: '#d17842',
                backgroundColor: '#000000',
                height: 80,
                width: 80
            });
            request.get('/barista/orders').then(({data: {data}}) => {
                  this.UPDATE_PAGE_STATE(data)
            }).catch((error) => {
                console.log(error);
            }).finally(() => {
                loader.hide();
            });
        },

        cancelPendingOrder(id, index) {
            // this.pendingRequests -= 1;
            this.clickedButton = index;
            this.defaultButtonCancel = false;
            let status = 'cancelled';
            let url = `/barista/order/update/${id}/status/${status}`
            request.get(url).then(({data: {data}}) => {
                this.pageState.orders.map(order => {
                    if (order.id === data.id) {
                        order.status = data.status;
                    }
                })
                this.defaultButtonCancel = true
                this.clickedButton = undefined;
                this.$toastr.s("Success", response.data.message)
            }).catch((error) => {
                console.log(error);
            })
        },
        cancelProcessingOrder(id, index) {
            this.clickedButton = index;
            this.defaultButtonCancel = false;
            let status = 'cancelled';
            let url = `/barista/order/update/${id}/status/${status}`
            request.get(url).then(({data: {data}}) => {
                this.defaultButtonCancel = true
                this.clickedButton = undefined;
                this.pageState.orders.map(order => {
                    if (order.id === data.id) {
                        order.status = data.status;
                    }
                })
                this.$toastr.s("Success", data.message)
            }).catch((error) => {
                console.log(error);
            })
        },
        processOrder(id, index) {
            this.markedAsProcessing = true;
            this.clickedButton = index;
            this.defaultButton = false;
            let status = 'processing';
            let url = `/barista/order/update/${id}/status/${status}`
            request.get(url).then(({data: {data}}) => {
                this.pageState.orders.map(order => {
                    if (order.id === data.id) {
                        order.status = data.status;
                    }
                })
                this.defaultButton = true;
                this.clickedButton = undefined;
                this.$toastr.s("Success", data.message)
            }).catch((error) => {
                console.log(error);
            })
        },
        confirmOrder(id, index) {
            this.clickedButton = index;
            this.defaultButton = false;
            let status = 'completed';
            let url = `/barista/order/update/${id}/status/${status}`
            request.get(url).then(({data: {data}}) => {
                this.defaultButton = true;
                this.clickedButton = undefined;
                this.pageState.orders.map(order => {
                    if (order.id === data.id) {
                        order.status = data.status;
                    }
                })
                // this.$toastr.s("Success", response.data.message)
                this.completedRequestCounter();
            }).catch((error) => {
                console.log(error);
            })
        },
        completedRequestCounter() {
            request.get('/barista/orders').then(({data: {data}}) => {
                this.pageState.completedOrdersCount = data.completedOrdersCount;
            });
        },
    },

    computed: {
        ...mapState(['pageState']),
        orders() {
            return this.pageState.orders;
        },
        pendingRequests() {
            if (this.pageState.orders) {
                return this.pageState.orders.filter(order => order.status === 'pending').length;
            }
        },
        processingRequests() {
            if (this.pageState.orders) {
                return this.pageState.orders.filter(order => order.status === 'processing').length;
            }
        },
    }
}
</script>

<style scoped>

.ordered__menu .ordered__item .order__info .order__info__details .double-shot-badge {
  margin-top: 5px;
  display: inline-block;
  background-color: #d17842;
  color: white;
  font-size: 0.75rem;
  padding: 3px 6px;
  border-radius: 8px;
  vertical-align: middle;
  font-weight: 600;
}

.order__info__responsive {
    display: none;
}
.order__info__responsive .WpcButton {
    min-height: auto;
    padding: 8px 15px;
    display: inline-block;
    width: auto;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 400;
    background-color: #d17842;
    color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.order__info__responsive .WpcButton.cancel {
    background: #dc3545;
    border-color: #dc3545;
}
.ordered__menu .ordered__item .order__notes p {
    word-break: break-word;
    padding-left: 11px;
}
.ordered__menu .ordered__item .order__info .order__info__details span {
    display: block;
}
.ordered__menu .ordered__item .order__notes p::before {
    font-size: 26px;
    font-family: "PT Sans", sans-serif;
}
@media all and (max-width: 450px) {
    .ordered__menu .ordered__item .order__info .order__info__button {
        display: none;
    }
    .ordered__menu .ordered__item .order__info .order__info__details {
        max-width: 100%;
    }
    .order__info__responsive {
        display: block;
    }
}
</style>

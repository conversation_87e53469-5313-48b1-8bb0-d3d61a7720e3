<template>
  <div class="pb-4">
    <div class="item__wrap flex__wrap">
      <router-link :to="{name:'order',params:{id:menu.id,coupon:menu.availableCoupon}}" v-for="(menu, index) in data"
                   :key="index" data-id="6" class="item">
        <div class="item__thumb bgi__property"
             :style="{backgroundImage: 'url(' + escapeCssUrl(menu.thumbnail) + ')'}">
          <div class="item__available__count">
            <div class="icon"><i class="wpc-icon wpc-coffee-cup"></i>
            </div>
            <p>{{ menu.availableCoupon }}</p>
          </div>
        </div>
        <div class="item__body">
          <h3 class="item__name">{{ menu.name }}</h3>
        </div>
      </router-link>
    </div>
  </div>
</template>
<script>

import {request} from "../../../helpers/request";
import {eventBus} from "../../../app";
import { escapeCssUrl } from "../../../helpers/utils";

export default {
  data() {
    return {
      data: [],
      availableMenus: [],
      selectedItem: false,
    }
  },
  methods: {
    escapeCssUrl,
    getMenus() {
      this.$store.state.userSidebarVisible = false;

      let loader = this.$loading.show({
        loader: 'dots',
        container: this.fullPage ? null : this.$refs.formContainer,
        canCancel: true,
        onCancel: this.onCancel,
      });
      request.get('/menu').then(({data: {data}}) => {
        this.data = data
        loader.hide();
      }).catch((error) => {
        console.log(error);
      })
    },
  },
  mounted: function () {
    this.getMenus();
    eventBus.$on('orderCanceled_getMenus', (data) => {
      if (data) {
        this.getMenus();
      }
    });
  },
}
</script>

<template>
  <div class="coupon__statistic">
    <div class="statistic__card">
      <div class="card__top">
        <div class="icon">
          <i class="wpc-icon wpc-coffee-seeds"></i>
        </div>
        <h4>{{ pendingRequests }}</h4>
      </div>
      <p>Pending Requests</p>
    </div>
    <div class="statistic__card">
      <div class="card__top">
        <div class="icon">
          <i class="wpc-icon wpc-coffee-cup"></i>
        </div>
        <h4>{{ completedRequests }}</h4>
      </div>
      <p>Completed Requests (Today)</p>
    </div>
  </div>
</template>
<script>
import {mapState} from "vuex";

export default {
  computed: {
    ...mapState(['pageState']),
    pendingRequests() {
      if (this.pageState.orders) {
        return this.pageState.orders.filter(order => order.status === 'pending').length;
      }
    },
    completedRequests() {
      if (this.pageState.orders) {
        return this.pageState.completedOrdersCount;
      }
    }
  }
}
</script>

<template>
    <div class="header" style="background-image: url('/barista-h-bg.png'); background-size: cover">
        <div class="header__nav">
            <div class="nav__icon">
                <a href="#" @click.prevent="sidebarOpen(true)"><i class="wpc-icon wpc-bar"></i></a>
            </div>
            <div class="notification__icon">
                <router-link to="/barista/notification"><i class="wpc-icon wpc-notification"></i></router-link>
            </div>
        </div>
        <div class="header__greeting">
            <div class="row">
                <div class="col-12">
                    <p>{{ isMorning() }}</p>
                    <h2>{{ user.name }}</h2>
                </div>
            </div>
            <div class="barista__status">
                <div class="" >
                    <form method="post" id="barista_available_form">
                        <label>
                            <input type="checkbox" name="is_available" :checked="is_available"
                                   @change="baristaStatusChanged(user.id)">
                            <span>Available Now</span>
                        </label>
                    </form>
                </div>
                <div class="">
                    <form method="post" id="barista_available_form">
                        <label>
                            <input type="checkbox"
                                   name="is_available"
                                   :checked="is_maintenance"
                                   @change="maintenanceStatusChanged()">
                            <span>Maintenance</span>
                        </label>
                    </form>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
import {mapActions, mapState} from "vuex";
import {TOGGLE_SIDEBAR_BARISTA} from "../../store/actions";
import {request} from "../../helpers/request";

export default {
    name: "Header",
    data() {
        return {
            is_available: null,
            is_maintenance: false,
        }
    },
    computed: {
        ...mapState(['user', 'pageState']),

        isAnyOrderPending() {
            let processingOrdersCount =  this.pageState.orders.filter(order => order.status === 'processing').length;
            let pendingOrdersCount =  this.pageState.orders.filter(order => order.status === 'pending').length;

            return !(processingOrdersCount <= 0 && pendingOrdersCount <= 0);

        }
    },
    mounted() {
        this.getUser()
    },
    methods: {
        ...mapActions([TOGGLE_SIDEBAR_BARISTA]),
        sidebarOpen(data) {
            return this.TOGGLE_SIDEBAR_BARISTA(data)
        },

        baristaStatusChanged(id) {
            request.get(`barista/status/${id}/update`).then(({data: {data}}) => {
                this.is_available = data.is_available;
                this.$toastr.s("success", 'Barista Status Updated');
            }).catch((error) => {
                console.log(error);
            })
        },

        maintenanceStatusChanged() {
            this.updateMaintenanceStatus()
            if (!this.is_maintenance && this.isAnyOrderPending) {
                this.$swal.fire({
                    title: 'Cancel all pending orders?',
                    text: "All pending and processing orders will be canceled!",
                    paddingBottom: '150px',
                    paddingTop: '50px',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes.',
                    cancelButtonText: 'No',
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.cancelAllPendingOrders()
                    }
                })
            }

        },

        cancelAllPendingOrders() {
            request.get(`barista/cancel-orders`).then(({data}) => {
                this.pageState.orders.map(order => {
                    order.status = 'cancelled';
                })
                this.$toastr.s("success", data.message);
            }).catch((error) => {
                console.log(error);
            })
        },

        updateMaintenanceStatus() {
            request.get(`barista/maintenance/update`).then(({data}) => {
                this.is_maintenance = data.data.is_maintenance;
                this.$toastr.s("success", data.message);
            }).catch((error) => {
                console.log(error);
            })
        },

        getUser() {
            request.get(`/user`).then(({data : data}) => {
                this.is_available = data.data.is_available;
                this.is_maintenance = data.is_barista_maintenance;
            }).catch((error) => {
                console.log(error);
            })
        },

        isMorning() {
            let date = new Date();
            let time = date.getHours();

            if (time < 12) {
                return 'Good Morning'
            }
            if (time > 12 && time < 17) {
                return 'Good Afternoon'
            } else {
                return 'Good Evening'
            }

        }

    }
}
</script>

<style scoped>
.header .header__greeting #barista_available_form {
    position: static;
}
.barista__status {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;

}
</style>

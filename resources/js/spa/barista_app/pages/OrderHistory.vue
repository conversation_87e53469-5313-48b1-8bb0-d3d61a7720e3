<template>
  <div>
    <div class="page__header">
      <div class="left__content">
        <router-link to="/barista" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
      </div>
      <div class="page__title">
        <h5>Order History</h5>
      </div>
      <div class="right__content">
        <!--      <a href="#" class="clear__button">Clear all</a>-->
      </div>
    </div>

    <div class="section__header">
      <h3 class="section__header__title">Today</h3>
    </div>

    <ul class="wpc-order__history">
      <li class="item__ordered" v-for="(today_order,index) in order_history_today" :key="index">
        <div class="order__top">
          <p class="order__id">#request-{{ today_order.id }}</p>
          <span class="order__status">{{ today_order.status }}</span>
        </div>
        <div class="order__details">
          <div class="item__thumb bgi__property" :style="{backgroundImage: 'url(' + today_order.menu.thumbnail + ')',}">

          </div>
          <div class="item__body">
            <h5>You have an order for {{ today_order.menu.name }}</h5>
            <span class="order__date">{{ orderDate(today_order.created_at) }}</span>
            <span class="order__date">Status: {{ today_order.status }}</span>
          </div>
        </div>
      </li>
    </ul>

    <div class="section__header mt30">
      <h3 class="section__header__title">Previous Orders</h3>
    </div>
    <ul class="wpc-order__history pb-4">
      <li class="item__ordered" v-for="(prev_order,index) in order_history_previous" :key="index">
        <div class="order__top">
          <p class="order__id">#request-{{ prev_order.id }}</p>
          <span class="order__status">{{ prev_order.status }}</span>
        </div>
        <div class="order__details">
          <div class="item__thumb bgi__property" :style="{backgroundImage: 'url(' + prev_order.menu.thumbnail + ')',}">

          </div>
          <div class="item__body">
            <h5>You have an order for {{ prev_order.menu.name }}</h5>
            <span class="order__date">{{ orderDate(prev_order.created_at) }}</span>
          </div>
        </div>
      </li>
    </ul>
    <div class="row">
      <div class="col-lg-6 offset-4">
        <nav class="WpcPaginationWrapper text-center">
          <ul class="pagination">
            <pagination align="center" :data="order_history" @pagination-change-page="list"></pagination>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
import {request} from "../../helpers/request";
import moment from "moment";
import pagination from 'laravel-vue-pagination'

export default {
  name: "OrderHistoryComponent",
  components: {
    pagination
  },
  data() {
    return {
      order_history: {},
      order_history_today: [],
      order_history_previous: [],
    }
  },

  mounted: function () {
    this.list()
  },

  methods: {

    orderDate(date) {
      return moment(date).format("MMM D, YYYY");
    },

    async list(page = 1) {
      let loader = this.$loading.show({
        loader: 'spinner',
        container: this.fullPage ? null : this.$refs.formContainer,
        canCancel: true,
        onCancel: this.onCancel,
        color: '#d17842',
        backgroundColor: '#000000',
        height: 80,
        width: 80
      });
      await request.get(`/barista/order-history?page=${page}`).then(({data: {data}}) => {
        this.order_history = data.order_history_previous
        this.order_history_today = data.order_history_today;
        this.order_history_previous = data.order_history_previous.data;
      }).catch(({response}) => {
        console.error(response)
      }).finally(() => {
        loader.hide();
      });
    }
  }


}
</script>

<style scoped>

</style>

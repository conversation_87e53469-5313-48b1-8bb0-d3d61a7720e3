<template>
  <div class="custom-request">
    <div class="page__header">
      <div class="left__content">
        <router-link to="/barista" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
      </div>
      <div class="page__title">
        <h5>Custom Request</h5>
      </div>
      <div class="right__content"></div>
    </div>

    <div class="search__bar">
      <div class="form__group">
        <span class="search__icon"><i class="wpc-icon wpc-search"></i></span>
        <input type="text" placeholder="Search" name="search" value="" id="search_emp" class="form__control" v-model="searchedData" @keyup="userData()">
      </div>
    </div>
    <form action="" method="post">
      <div class="employer__suggestion">
        <div class="employer__suggestion__title">
          <p>Employee</p>
        </div>
        <div class="ajax_div_employee" id="ajax_div_employee">

          <div class="employer__suggestion__item" v-for="user in users">
            <div class="employer__info">
              <div class="employer__thumb bgi__property" :style="{ backgroundImage : `url(${user.avatar})`}">
              </div>
              <div class="employer__name">
                <h5>{{ user.name }}</h5>
                <span>{{ user.designation }}</span>
              </div>
            </div>
            <label class="check__employer">
              <input name="selected_emp" v-model="selected_employee" :value="user.id" type="checkbox">
              <span></span>
            </label>
          </div>

        </div>
      </div>
      <div class="add__order">
        <p class="add__order__title">Type</p>
        <div class="add__order__form">
          <div class="wpc__select">
            <span><i class="wpc-icon wpc-arrow-down"></i></span>
            <select name="menu_id" v-model="selected_menu">
              <option value="0" selected>Select Menu</option>
              <option v-for="menu in menus" v-bind:value="menu.id">{{ menu.name }}</option>
            </select>
          </div>
          <div class="item__quantity__wrap">
            <span class="item__decrease" @click="sub"><i class="wpc-icon wpc-minus"></i></span>
            <input id="quantity" class="item__vlaue" name="menu_quantity" type="number" v-model="quantity" readonly
                   min="1"/>
            <span class="item__increase" @click="add"><i class="wpc-icon wpc-plus"></i></span>
          </div>
        </div>
      </div>

      <div class="mt-auto pl15 pr15 pb-4">
        <button type="button" class="WpcButton WpcFilled" @click.prevent="placeOrder()">Place Order</button>
      </div>
    </form>
  </div>
</template>

<script>
import {request} from "../../helpers/request";
export default {
  name: "CustomRequest",

  data() {
    return {
      users: [],
      menus: [],
      quantity: 1,
      selected_employee:[],
      selected_menu:0,
      searchedData :'',
    }
  },

  mounted() {
    this.userData();
  },

  methods: {
    userData() {
      request.get(`/barista/get-customer-list?search=${this.searchedData}`).then(({data:{data}}) => {
        this.users = data.employee;
        this.menus = data.allMenu;
      }).catch(error => {
        console.log(error);
      }).finally(() => {
        loader.hide();
      });
    },

    add() {
      this.quantity += 1
    },

    sub() {
      if (this.quantity > 1) {
        this.quantity -= 1
      }
    },

    placeOrder(){
      for (let i = 0; i < this.selected_employee.length; i++) {
        request.post('/barista/order-place',{employees:this.selected_employee[i],menu_id:this.selected_menu}).then(({data:{data}}) => {
          this.$toastr.s("success",'Order created successfully');
        }).catch(error => {
          this.$toastr.e("error",error.response.data.message);
        });
      }
    }

  },
}
</script>

<style scoped>

</style>

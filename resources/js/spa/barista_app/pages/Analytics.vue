<template>
  <div>
    <!-- Full-page loading overlay -->
    <div class="full-page-loader" v-if="loading">
      <div class="loader-content">
        <div class="spinner"></div>
        <p>Loading data...</p>
      </div>
    </div>

    <div class="wpc-container analytics-page">
      <div class="page__header">
        <div class="left__content">
          <router-link to="/barista" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
        </div>
        <div class="page__title">
          <h5> Coffee Orders Analytics</h5>
        </div>
        <div class="right__content"></div>
      </div>

      <div class="analytics-filters">
          <div class="form-group">
            <label for="coffee-filter">Filter by Coffee</label>
            <div class="custom-select-container">
              <v-select
                  v-model="selectedCoffees"
                  :options="coffeeOptions"
                  :reduce="coffee => coffee.value"
                  label="label"
                  multiple
                  :clearable="true"
                  :close-on-select="false"
                  :deselectFromDropdown="true"
                  placeholder="Select coffees"
              >
                <template #selected-option="{ label }">
                  <div class="selected-tag">
                    <span>{{ label }}</span>
                  </div>
                </template>
                <template #option="{ label }">
                  <div class="select-option">
                    {{ label }}
                  </div>
                </template>
              </v-select>
            </div>
          </div>
          <div class="form-group">
            <label for="date-range">Date Range</label>
            <div class="date-dropdown">
              <v-select
                v-model="selectedDateRange"
                :options="dateRangeOptions"
                :reduce="option => option.value"
                label="label"
                :clearable="false"
                @input="handleDateRangeChange"
                placeholder="Select date range"
              >
                <template #selected-option="{ label }">
                  <div class="selected-date-option">
                    <span>{{ label }}</span>
                  </div>
                </template>
                <template #option="{ label }">
                  <div class="date-option">
                    {{ label }}
                  </div>
                </template>
              </v-select>
            </div>
          </div>

          <!-- Custom date range inputs, shown only when custom is selected -->
          <div v-if="selectedDateRange === 'custom'" class="date-range-inputs">
            <div class="form-group date-form-group">
              <div class="date-label-container">
                <label for="date-from">From Date</label>
              </div>
              <div class="date-picker-container">
                <date-range-picker
                  ref="fromDatePicker"
                  :opens="'right'"
                  :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                  :minDate="minDate"
                  :maxDate="maxDate"
                  :autoApply="true"
                  :singleDatePicker="true"
                  :showDropdowns="true"
                  :ranges="false"
                  :showWeekNumbers="false"
                  v-model="fromDateRange"
                  @update="updateFromDate"
                >
                  <template v-slot:input="picker">
                    <div class="date-display">
                      {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.startDate)) }}
                    </div>
                  </template>
                </date-range-picker>
              </div>
            </div>
            <div class="form-group date-form-group">
              <div class="date-label-container">
                <label for="date-to">To Date</label>
              </div>
              <div class="date-picker-container">
                <date-range-picker
                  ref="toDatePicker"
                  :opens="'left'"
                  :locale-data="{ firstDay: 0, format: 'dd/mm/yyyy' }"
                  :minDate="fromDateRange.startDate ? new Date(fromDateRange.startDate) : minDate"
                  :maxDate="maxDate"
                  :autoApply="true"
                  :singleDatePicker="true"
                  :showDropdowns="true"
                  :ranges="false"
                  :showWeekNumbers="false"
                  v-model="toDateRange"
                  @update="updateToDate"
                >
                  <template v-slot:input="picker">
                    <div class="date-display">
                      {{ new Intl.DateTimeFormat('en-GB').format(new Date(picker.endDate)) }}
                    </div>
                  </template>
                </date-range-picker>
              </div>
            </div>
          </div>
            <div class="analytics-filters-bottom">
              <button class="btn btn-primary" @click="fetchAnalytics">Apply Filters</button>
          </div>
      </div>


      <div class="analytics-content mt-4">
        <!-- Export functionality removed -->

        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Orders Summary</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                    <tr>
                      <th>Coffee</th>
                      <th>Requested</th>
                      <th>Served</th>
                      <th>Rejected</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="coffee in coffeeStats" :key="coffee.id">
                      <td>
                        <div class="d-flex align-items-center">
<!--                          <div class="coffee-thumb mr-2" :style="{ backgroundImage: `url(${coffee.thumbnail})` }"></div>-->
                          <span>{{ coffee.name }}</span>
                        </div>
                      </td>
                      <td>{{ coffee.total_requested }}</td>
                      <td>{{ coffee.request_completed }}</td>
                      <td>{{ coffee.request_rejected || 0 }}</td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- New Consumption Statistics Chart -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Coffee Orders Statistics</h5>
              </div>
              <div class="card-body">
                <div v-if="error" class="alert alert-danger">
                  {{ error }}
                </div>
                <div v-else>
                  <div class="chart-container" style="height: 400px;">
                    <apexchart
                      ref="consumptionChart"
                      type="bar"
                      height="350"
                      :options="consumptionChartOptions"
                      :series="consumptionData.series"
                    ></apexchart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Time-based Coffee Consumption Analytics -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header custom-card-header">
                <h5>Coffee Consumption by Time</h5>
              </div>
              <div class="card-body">
                <div v-if="error" class="alert alert-danger">
                  {{ error }}
                </div>
                <div v-else>
                  <div class="chart-container" style="height: 450px;">
                    <apexchart
                      ref="timeChart"
                      type="area"
                      height="400"
                      :options="timeChartOptions"
                      :series="timeChartData.series"
                    ></apexchart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="copyright-footer">
        <p>© All Right Reserved</p>
      </div>
    </div>
  </div>
</template>

<script>
import { request } from '../../helpers/request';
import VueApexCharts from 'vue-apexcharts';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';

export default {
  name: "BaristaAnalytics",
  components: {
    apexchart: VueApexCharts,
    vSelect,
    DateRangePicker
  },
  data() {
    return {
      selectedCoffees: [],
      selectedDateRange: 'today',
      dateFrom: this.getDefaultStartDate(),
      dateTo: this.getDefaultEndDate(),
      fromDateRange: {
        startDate: this.getDefaultStartDate(),
        endDate: this.getDefaultStartDate(),
      },
      toDateRange: {
        startDate: this.getDefaultEndDate(),
        endDate: this.getDefaultEndDate()
      },
      minDate: new Date(new Date().setFullYear(new Date().getFullYear() - 3)),
      maxDate: new Date(),
      coffeeList: [],
      coffeeStats: [],

      consumptionData: {
        series: [],
        categories: []
      },
      timeChartData: {
        series: [],
        categories: []
      },
      consumptionChartOptions: {
        chart: {
          id: 'consumption-chart',
          type: 'bar',
          toolbar: {
            show: true,
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            },
            export: {
              csv: {
                filename: 'coffee_consumption_stats',
                columnDelimiter: ',',
                headerCategory: 'Coffee',
                headerValue: 'Value',
              },
              svg: {
                filename: 'coffee_consumption_stats',
              },
              png: {
                filename: 'coffee_consumption_stats',
              }
            }
          },
          stacked: false,
          background: '#242c39',
          foreColor: '#fff',
          zoom: {
            enabled: false
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded',
            borderRadius: 4
          },
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          type: 'category',
          categories: [],
          labels: {
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif'
            },
            tickPlacement: 'on',
            rotate: -45,
            rotateAlways: true,
            formatter: function(value) {
              // Ensure we display the actual value, not just the index
              return value;
            }
          },
          axisBorder: {
            show: true,
            color: '#333'
          },
          axisTicks: {
            show: true,
            color: '#333'
          }
        },
        yaxis: {
          title: {
            text: 'Number of Orders',
            style: {
              color: '#d17842',
              fontSize: '14px',
              fontFamily: 'DM Sans, sans-serif',
              fontWeight: 500
            }
          },
          labels: {
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif'
            },
            // Only show whole numbers (integers) on Y-axis
            formatter: function(val) {
              return Math.floor(val);
            }
          },
          // Ensure min value is 0 and steps are integers
          // tickAmount: 5,
          min: 0,
          forceNiceScale: true
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          theme: 'dark',
          y: {
            formatter: function(val) {
              return val + " orders";
            }
          }
        },
        legend: {
          position: 'bottom',
          horizontalAlign: 'center',
          offsetY: 10,
          labels: {
            colors: '#fff'
          },
          markers: {
            width: 12,
            height: 12,
            strokeWidth: 0,
            radius: 12,
            offsetX: 0,
            offsetY: 0
          },
          itemMargin: {
            horizontal: 10,
            vertical: 8
          }
        },
        grid: {
          borderColor: '#333',
          strokeDashArray: 3
        },
        colors: ['#d17842', '#33b2df', '#546E7A', '#d4526e', '#13d8aa']
      },
      timeChartOptions: {
        chart: {
          id: 'time-chart',
          type: 'area',
          toolbar: {
            show: true,
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            },
            export: {
              csv: {
                filename: 'coffee_consumption_by_time',
                columnDelimiter: ',',
                headerCategory: 'Date',
                headerValue: 'Value',
              },
              svg: {
                filename: 'coffee_consumption_by_time',
              },
              png: {
                filename: 'coffee_consumption_by_time',
              }
            }
          },
          background: '#242c39',
          foreColor: '#fff',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: 3,
          colors: ['#d17842', '#33b2df', '#546E7A', '#d4526e', '#13d8aa']
        },
        fill: {
          type: 'gradient',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.2,
            stops: [0, 90, 100]
          }
        },
        xaxis: {
          type: 'category',
          categories: [],
          labels: {
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif'
            },
            tickPlacement: 'on',
            rotate: -45,
            rotateAlways: true,
            hideOverlappingLabels: false,
            formatter: function(value) {
              // Ensure we display the actual value, not just the index
              return value;
            }
          },
          axisBorder: {
            show: true,
            color: '#333'
          },
          axisTicks: {
            show: true,
            color: '#333'
          },
          title: {
            text: '',
            style: {
              color: '#d17842',
              fontSize: '14px',
              fontFamily: 'DM Sans, sans-serif',
              fontWeight: 500
            }
          }
        },
        yaxis: {
          title: {
            text: 'Number of Orders',
            style: {
              color: '#d17842',
              fontSize: '14px',
              fontFamily: 'DM Sans, sans-serif',
              fontWeight: 500
            }
          },
          // Only show whole numbers (integers) on Y-axis
          labels: {
            formatter: function(val) {
              return Math.floor(val);
            },
            style: {
              colors: '#fff',
              fontSize: '12px',
              fontFamily: 'DM Sans, sans-serif'
            }
          },
          // Ensure min value is 0 and steps are integers
          // tickAmount: 5,
          min: 0,
          forceNiceScale: true
        },
        tooltip: {
          theme: 'dark',
          x: {
            format: 'HH:mm'
          },
          y: {
            formatter: function(val) {
              return val + " orders";
            }
          }
        },
        legend: {
          show: true,
          position: 'bottom',
          horizontalAlign: 'center',
          offsetY: 10,
          labels: {
            colors: '#fff'
          },
          markers: {
            width: 12,
            height: 12,
            strokeWidth: 0,
            radius: 12,
            offsetX: 0,
            offsetY: 0
          },
          itemMargin: {
            horizontal: 10,
            vertical: 8
          }
        },
        grid: {
          borderColor: '#333',
          strokeDashArray: 3
        },
        colors: ['#d17842', '#33b2df', '#546E7A', '#d4526e', '#13d8aa']
      },
      loading: true,
      error: null
    };
  },
  mounted() {
    // Set initial date range based on the default selection
    this.handleDateRangeChange();
    this.fetchInitialData();
  },
  methods: {
    getDefaultStartDate() {
      // Return today's date for the default start date
      return new Date().toISOString().split('T')[0];
    },
    getDefaultEndDate() {
      return new Date().toISOString().split('T')[0];
    },
    formatDateDisplay(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toISOString().split('T')[0];
    },
    updateFromDate(range) {
      this.fromDateRange = range;
      this.dateFrom = this.formatDateDisplay(range.startDate);
    },
    updateToDate(range) {
      this.toDateRange = range;
      this.dateTo = this.formatDateDisplay(range.startDate);
    },

    handleDateRangeChange() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];

      switch(this.selectedDateRange) {
        case 'today':
          this.dateFrom = todayStr;
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: today, endDate: today };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'yesterday':
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = yesterday.toISOString().split('T')[0];
          this.dateFrom = yesterdayStr;
          this.dateTo = yesterdayStr;
          this.fromDateRange = { startDate: yesterday, endDate: yesterday };
          this.toDateRange = { startDate: yesterday, endDate: yesterday };
          break;

        case 'last7days':
          const last7days = new Date();
          last7days.setDate(last7days.getDate() - 6);
          this.dateFrom = last7days.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last7days, endDate: last7days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'last30days':
          const last30days = new Date();
          last30days.setDate(last30days.getDate() - 29);
          this.dateFrom = last30days.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: last30days, endDate: last30days };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'thisMonth':
          const thisMonth = new Date();
          const firstDayThisMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
          this.dateFrom = firstDayThisMonth.toISOString().split('T')[0];
          this.dateTo = todayStr;
          this.fromDateRange = { startDate: firstDayThisMonth, endDate: firstDayThisMonth };
          this.toDateRange = { startDate: today, endDate: today };
          break;

        case 'lastMonth':
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          const firstDayLastMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
          const lastDayLastMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
          this.dateFrom = firstDayLastMonth.toISOString().split('T')[0];
          this.dateTo = lastDayLastMonth.toISOString().split('T')[0];
          this.fromDateRange = { startDate: firstDayLastMonth, endDate: firstDayLastMonth };
          this.toDateRange = { startDate: lastDayLastMonth, endDate: lastDayLastMonth };
          break;

        case 'custom':
          // Do nothing, let the user select custom dates
          break;
      }

      // If not custom, fetch analytics immediately
      // if (this.selectedDateRange !== 'custom') {
      //   this.fetchAnalytics();
      // }
    },

    // Using vue-select's built-in clear functionality
    async fetchInitialData() {
      try {
        // Show loading overlay
        this.loading = true;
        this.error = null;

        const params = {
          coffee_id: this.selectedCoffees.length > 0 ? this.selectedCoffees.join(',') : 'all',
          start_date: this.dateFrom,
          end_date: this.dateTo
        };

        // Execute both API calls in parallel
        const [coffeeResponse, analyticsResponse] = await Promise.all([
          request.get('/barista/menu'),
          request.get('/barista/order/analytics', { params })
        ]);

        // Process coffee list data
        this.coffeeList = coffeeResponse.data.data;

        // Process analytics data
        const data = analyticsResponse.data.data;
        this.coffeeStats = data.coffeeStats;

        // Set consumption chart data
        if (data.consumptionStats) {
          this.consumptionData.series = data.consumptionStats.series;

          // Ensure categories are properly set
          if (data.consumptionStats.categories && data.consumptionStats.categories.length > 0) {
            this.consumptionChartOptions.xaxis.categories = data.consumptionStats.categories;
            // Force chart update to ensure categories are displayed correctly
            if (this.$refs.consumptionChart && this.$refs.consumptionChart.chart) {
              this.$refs.consumptionChart.chart.updateOptions({
                xaxis: {
                  categories: data.consumptionStats.categories
                }
              });
            }
          }
        }

        // Set time-based chart data
        if (data.series && data.series.length > 0) {
          this.timeChartData.series = data.series;

          // Ensure categories are properly set
          if (data.categories && data.categories.length > 0) {
            this.timeChartOptions.xaxis.categories = data.categories;

            // Force chart update to ensure categories are displayed correctly
            if (this.$refs.timeChart && this.$refs.timeChart.chart) {
              this.$refs.timeChart.chart.updateOptions({
                xaxis: {
                  categories: data.categories
                }
              });
            }
          }

          // For time-based chart, ensure we always have a visible legend
          this.timeChartData.series = data.series.map(series => {
            // Always try to set the name property for the legend using coffee names
            const coffeeId = series.coffeeId || (series.id ? series.id.toString() : null);
            if (coffeeId) {
              const coffee = this.coffeeList.find(c => c.id.toString() === coffeeId);
              if (coffee) {
                series.name = coffee.name; // Use the actual coffee name
              } else if (!series.name) {
                series.name = 'Coffee ' + coffeeId; // Fallback if no name and coffee not found
              }
            } else if (!series.name) {
              series.name = 'Selected Coffee'; // Default fallback
            }
            return series;
          });

          // Force the legend to be visible
          this.timeChartOptions.legend.show = true;
        }

        // Hide loading overlay after a slight delay for better UX
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = 'Failed to load data. Please try again.';
        this.loading = false;
      }
    },

    // Keep individual methods for when filters are applied
    fetchAnalytics() {
      // Show loading overlay
      this.loading = true;
      this.error = null;

      const params = {
        coffee_id: this.selectedCoffees.length > 0 ? this.selectedCoffees.join(',') : 'all',
        start_date: this.dateFrom,
        end_date: this.dateTo
      };

      request.get('/barista/order/analytics', { params })
        .then(response => {
          const data = response.data.data;
          this.coffeeStats = data.coffeeStats;

          // Set consumption chart data
          if (data.consumptionStats) {
            this.consumptionData.series = data.consumptionStats.series;

            // Ensure categories are properly set
            if (data.consumptionStats.categories && data.consumptionStats.categories.length > 0) {
              this.consumptionChartOptions.xaxis.categories = data.consumptionStats.categories;
              console.log('Consumption chart categories:', this.consumptionChartOptions.xaxis.categories);

              // Force chart update to ensure categories are displayed correctly
              if (this.$refs.consumptionChart && this.$refs.consumptionChart.chart) {
                this.$refs.consumptionChart.chart.updateOptions({
                  xaxis: {
                    categories: data.consumptionStats.categories
                  }
                });
              }
            }
          }

          // Set time-based chart data
          if (data.series && data.series.length > 0) {
            this.timeChartData.series = data.series;

            // Ensure categories are properly set
            if (data.categories && data.categories.length > 0) {
              this.timeChartOptions.xaxis.categories = data.categories;
              this.timeChartOptions.xaxis.type = 'category';
              console.log('Time chart categories:', this.timeChartOptions.xaxis.categories);

              // Force chart update to ensure categories are displayed correctly
              if (this.$refs.timeChart && this.$refs.timeChart.chart) {
                this.$refs.timeChart.chart.updateOptions({
                  xaxis: {
                    categories: data.categories,
                    type: 'category'
                  }
                });
              }
            }

            // For time-based chart, ensure we always have a visible legend
            this.timeChartData.series = data.series.map(series => {
              // Always try to set the name property for the legend using coffee names
              const coffeeId = series.coffeeId || (series.id ? series.id.toString() : null);
              if (coffeeId) {
                const coffee = this.coffeeList.find(c => c.id.toString() === coffeeId);
                if (coffee) {
                  series.name = coffee.name; // Use the actual coffee name
                } else if (!series.name) {
                  series.name = 'Coffee ' + coffeeId; // Fallback if no name and coffee not found
                }
              } else if (!series.name) {
                series.name = 'Selected Coffee'; // Default fallback
              }
              return series;
            });

            // Force the legend to be visible
            this.timeChartOptions.legend.show = true;
          }

          // Hide loading overlay after a slight delay for better UX
          setTimeout(() => {
            this.loading = false;
          }, 500);
        })
        .catch(error => {
          console.error('Error fetching analytics:', error);
          this.error = 'Failed to load analytics data. Please try again.';
          this.loading = false;
        });
    },
  },
  computed: {
    coffeeOptions() {
      if (!this.coffeeList || this.coffeeList.length === 0) {
        return [];
      }

      return this.coffeeList.map(coffee => ({
        label: coffee.name,
        value: coffee.id
      }));
    },
    dateRangeOptions() {
      return [
        { label: 'Today', value: 'today' },
        { label: 'Yesterday', value: 'yesterday' },
        { label: 'Last 7 Days', value: 'last7days' },
        { label: 'Last 30 Days', value: 'last30days' },
        { label: 'This Month', value: 'thisMonth' },
        { label: 'Last Month', value: 'lastMonth' },
        { label: 'Custom Range', value: 'custom' }
      ];
    },
    availableDates() {
      // Generate dates for the last year
      const dates = [];
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      // Loop through each day from one year ago to today
      for (let d = new Date(oneYearAgo); d <= today; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        const dateObj = new Date(d);
        const formattedDate = dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });

        dates.push({
          label: formattedDate,
          value: dateStr
        });
      }

      // Sort dates in descending order (newest first)
      return dates.reverse();
    }
  }
};
</script>

<style scoped>
.analytics-page {
  padding: 0 15px 30px;
}

.analytics-filters {
  background-color: #242c39;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  color: #fff;
}

.analytics-filters label {
  color: #d17842;
  font-weight: 500;
  margin-bottom: 8px;
}

.analytics-filters .form-control {
  background-color: #1e222a;
  border: 1px solid #242c39;
  color: #fff;
  border-radius: 8px;
}


.analytics-filters .analytics-filters-bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
}

.analytics-filters .btn-primary {
  background-color: #d17842;
  border-color: #d17842;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
}

.analytics-filters .btn-secondary {
  background-color: #242c39;
  border-color: #333;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  color: #d17842;
  transition: all 0.2s;
}

.analytics-filters .btn-secondary:hover {
  background-color: #1a2130;
  color: #fff;
}

/* Export functionality CSS removed */

/* ApexCharts Download Button Styling */
.apexcharts-menu-icon {
  fill: #d17842 !important;
}

.apexcharts-menu {
  background-color: #242c39 !important;
  border: 1px solid #333 !important;
}

.apexcharts-menu-item {
  color: #fff !important;
}

.apexcharts-menu-item:hover {
  background-color: #d17842 !important;
}

.card {
  background-color: #242c39;
  border: none;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.custom-card-header {
  border-bottom: 1px solid #333;
  padding: 15px 20px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.custom-card-header h5 {
  color: #d17842;
  font-weight: 600;
  margin: 0;
  font-size: 18px;
}

.card-body {
  padding: 20px;
  color: #fff;
}

.coffee-thumb {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  margin-right: 10px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.table {
  color: #fff;
}

.table th {
  border-top: none;
  border-bottom: 2px solid #333;
  color: #d17842;
  text-align: center;
}

.table td {
  border-top: 1px solid #333;
  vertical-align: middle;
  text-align: center;
}

.copyright-footer {
  text-align: center;
  padding: 15px 0;
}

.copyright-footer p {
  color: #999;
  font-size: 14px;
  margin: 0;
}

/* Full-page loading overlay */
.full-page-loader {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 550px;
  height: 100%;
  background-color: rgba(12, 15, 20, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader-content {
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #333;
  border-top: 5px solid #d17842;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

.loader-content p {
  color: #fff;
  font-size: 16px;
  margin: 0;
}


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

</style>

<style>
.v-select .vs__selected-options {
  padding: 10px !important;
}
.v-select .vs__no-options {
  color: #fff;
}
.custom-select-container .v-select .vs__selected {
  background-color: #d17842;
  color: #fff;
  padding: 5px 10px !important;
  margin: 0 5px 5px 0;
  border-radius: 15px;
  font-weight: 500;
}
.v-select .vs__deselect {
  fill: #fff;
  margin-left: 8px;
}
.vs__dropdown-option--highlight {
  background-color:  #d17842;
}

/* Date dropdown styling */
.date-dropdown .v-select {
  background-color: #0c0f14;
  border-radius: 8px;
  font-family: 'DM Sans', sans-serif;
  position: relative;
  z-index: 100;
}

.vs__dropdown-toggle {
  border: 1px solid #242c39;
  border-radius: 8px;
  background-color: #0c0f14;
  padding-right: 10px;
}

.date-dropdown .vs__selected {
  color: #fff;
  font-weight: 500;
}

.vs__dropdown-menu {
  background-color: #0c0f14;
  border: 1px solid #242c39;
  border-radius: 8px;
  margin-top: 8px;
  padding: 5px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.vs__dropdown-option {
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 2px;
}

.vs__actions .vs__open-indicator, .vs__actions .vs__close-indicator {
  fill: #d17842;
}

.selected-date-option {
  display: flex;
  align-items: center;
  color: #fff;
}

.date-range-inputs {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  padding: 15px;
  background-color: #1e222a !important;
  border-radius: 8px;
}

.date-range-inputs .form-group {
  flex: 1;
  margin-bottom: 0;
}

.date-form-group {
  display: flex;
  flex-direction: column;
}

.date-label-container {
  margin-bottom: 8px;
}

.date-picker-container, .vue-daterange-picker {
  width: 100%;
}

/* Date Range Picker Global Styles */
.daterangepicker {
  background-color: white !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  color: #333 !important;
  font-family: 'DM Sans', sans-serif !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important;
  padding: 15px !important;
  width: auto !important;
  min-width: 300px !important;
}

/* Date display styling to match form control */

.date-picker-container .reportrange-text {
  background-color: #242c39 !important;
  color: #fff;
  border-radius: 8px;
  padding: 10px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.date-picker-container .reportrange-text::after {
  content: '\1F4C5';
  margin-left: auto;
  font-size: 10px;
  color: #d17842;
}
.apexcharts-menu-item {
  color: #000000;
}
.apexcharts-theme-light .apexcharts-menu-item:hover {
  background: #d17842 !important;
  color: #fff;
}

.coffee-thumb {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-color: #242c39;
}

/* Table responsiveness styles */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
  margin-bottom: 1rem;
  border-radius: 8px;
}

.table-responsive::-webkit-scrollbar {
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #242c39;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #d17842;
  border-radius: 3px;
}

/* Ensure table takes full width but cells have minimum width */
.table-responsive table {
  min-width: 100%;
  width: 100%;
}

.table-responsive th,
.table-responsive td {
  white-space: nowrap;
  min-width: 80px;
}

/* Make coffee name column wider */
.table-responsive th:first-child,
.table-responsive td:first-child {
  min-width: 150px;
}

.table-dark {
  background-color: #242c39;
  color: #fff;
}

.table-dark th {
  border-color: #333;
}
</style>

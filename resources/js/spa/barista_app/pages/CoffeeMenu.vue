<template>
  <div>
    <div class="page__header">
      <div class="left__content">
        <router-link to="/barista" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
      </div>
      <div class="page__title">
        <h5>Coffee Menu</h5>
      </div>
      <div class="right__content">
      </div>
    </div>
    <div>
      <div class="item__wrap flex__wrap">

        <div class="item" v-for="menu in data">
          <div class="item__thumb bgi__property" :style="{ backgroundImage : `url(${escapeCssUrl(menu.thumbnail)})`}">
          </div>
          <div class="item__body">
            <h3 class="item__name">{{ menu.name }}</h3>
            <label>
              <input type="checkbox" :value="menu.id" :checked="menu.is_active==1" name="is_active"
                     @change="updateMenu(menu.id,$event)">
              <span v-if="menu.is_active==1">Available Now</span>
              <span v-else>Not Available</span>
            </label>
          </div>
        </div>
      </div>
    </div>
    <Footer/>
  </div>
</template>

<script>
import Footer from "../components/Footer";
import {request} from "../../helpers/request";
import { escapeCssUrl } from "../../helpers/utils";

export default {
  name: "CoffeeMenuComponent",
  components: {Footer},

  data() {
    return {
      data: [],
      is_active: null,
    }
  },
  mounted: function () {
    this.menus();
  },

  methods: {
    escapeCssUrl,
    menus() {
      let loader = this.$loading.show({
        loader: 'spinner',
        container: this.fullPage ? null : this.$refs.formContainer,
        canCancel: true,
        onCancel: this.onCancel,
        color: '#d17842',
        backgroundColor: '#000000',
        height: 80,
        width: 80
      });
      request.get('/barista/menu').then(({data: {data}}) => {
        this.data = data
      }).catch((error) => {
        console.log(error);
      }).finally(() => {
        loader.hide();
      });
    },

    updateMenu(id, event) {
      if (event.target.checked) {
        this.is_active = 1;
      } else {
        this.is_active = 0;
      }
      request.put(`barista/menu`, {id: id, is_active: this.is_active}).then(({data: {data}}) => {
        this.$toastr.s('success', 'Menu Updated successfully');
        this.menus();
      }).catch((error) => {
        console.log(error);
      })
    }
  }
}
</script>

<style scoped>

</style>

<template>
  <div>
    <Header/>
    <Sidebar/>
    <HomeContent/>
    <Footer/>
  </div>
</template>

<script>
import Header from "../components/Header";
import Sidebar from "../components/Sidebar";
import Footer from "../components/Footer";
import HomeContent from "../components/home/<USER>";
import {request} from "../../helpers/request";
import {mapActions} from "vuex";
import {UPDATE_PAGE_STATE} from "../../store/actions";

export default {
  name: "HomeComponent",
  components: {
    HomeContent,
    Header,
    Sidebar,
    Footer
  },
  methods: {
    ...mapActions([UPDATE_PAGE_STATE]),
  },
  beforeMount() {
    let loader = this.$loading.show({
      loader: 'spinner',
      container: this.fullPage ? null : this.$refs.formContainer,
      canCancel: true,
      onCancel: this.onCancel,
      color: '#d17842',
      backgroundColor: '#000000',
      height: 80,
      width: 80
    });
    request.get('/barista/orders').then(({data: {data}}) => {
      this.UPDATE_PAGE_STATE(data);
    }).catch((error) => {
      console.log(error);
    }).finally(() => {
      loader.hide();
    });
  }
}
</script>

<style scoped>

</style>

<template>
  <div class="notification">
    <div class="page__header">
      <div class="left__content">
        <router-link to="/barista" class="back__button"><i class="wpc-icon wpc-angle-left"></i> Back</router-link>
      </div>
      <div class="page__title">
        <h5>Notification</h5>
      </div>
      <div class="right__content">
        <!--      <a href="#" class="clear__button">Clear all</a>-->
      </div>
    </div>

    <div class="wpc-notificatons">
      <div class="accordion" id="accordionExample">

        <div class="notification__item" v-for="(today,index) in allNotifications" :key="index">
          <div class="card">
            <div class="card-header" :id="'collapseNotifications'+index">
              <h2 class="mb-0">
                <button class="btn btn-link btn-block text-left collapsed" type="button" data-toggle=""
                        :data-target="'collapseNotifications'+index" aria-expanded="true"
                        :aria-controls="'collapseNotifications'+index">
                  Request of {{ today.message }} by {{ today.notification_owner.name }}
                </button>
              </h2>
            </div>

            <div :id="'collapseNotifications'+index" class="collapse" aria-labelledby="headingOne"
                 data-parent="#accordionExample">
            </div>
          </div>
          <div class="notification__date__time">
            <p class="notification__time">{{ notificationTime(today.created_at) }}</p>
            <p class="notification__time">{{ notificationDateTime(today.created_at) }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="w-50 mx-auto">
        <nav class="WpcPaginationWrapper text-center">
          <ul class="pagination">
            <pagination :limit="3" :data="notifications" @pagination-change-page="list"></pagination>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
import {request} from "../../helpers/request";
import moment from "moment";
import pagination from 'laravel-vue-pagination'

export default {
  name: "Notification",
  components: {
    pagination
  },
  data() {
    return {
      notifications: {},
      allNotifications: [],
    }
  },
  mounted: function () {
    this.list();
  },

  methods: {
    notificationTime(date) {
      return moment(date).fromNow();
    },
    notificationDateTime(date) {
      return moment(date).format("YYYY-MM-DD hh:mm:ss A");
    },

    async list(page = 1) {
      let loader = this.$loading.show({
        loader: 'spinner',
        container: this.fullPage ? null : this.$refs.formContainer,
        canCancel: true,
        onCancel: this.onCancel,
        color: '#d17842',
        backgroundColor: '#000000',
        height: 80,
        width: 80
      });
      await request.get(`/barista/notifications?page=${page}`).then(({data: {data}}) => {
        this.notifications = data.notifications
        this.allNotifications = data.notifications.data;
      }).catch(({response}) => {
        console.error(response)
      }).finally(() => {
        loader.hide();
      });
    }
  }

}

</script>

<style scoped>
.wpc-notificatons .notification__item .notification__date__time {
  display: flex;
  justify-content: space-between;
}
</style>

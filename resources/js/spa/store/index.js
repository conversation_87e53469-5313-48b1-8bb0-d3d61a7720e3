import Vue from 'vue';
import Vuex from 'vuex';
import actions from "./actions";
import mutations from "./mutations";
import createPersistedState from "vuex-persistedstate";

Vue.use(Vuex);

const store = new Vuex.Store({
    state: {
        userSidebarVisible: '',
        baristaSidebarVisible: '',
        user: {},
        pageState: {},
        customerPageState: {},
        customerCoffeeCounterState: {},
    },
    plugins: [createPersistedState()],
    mutations,
    actions
})

export default store;


export const TOGGLE_SIDEBAR = "TOGGLE_SIDEBAR";
export const TOGGLE_SIDEBAR_BARISTA = "TOGGLE_SIDEBAR_BARISTA";
export const LOGIN = "LOGIN";
export const ORDER_DETAILS = "ORDER_DETAILS";
export const UPDATE_PAGE_STATE = "UPDATE_PAGE_STATE";

export const UPDATE_USER = "UPDATE_USER";
export const UPDATE_CUSTOMER_PAGE_STATE = "UPDATE_CUSTOMER_PAGE_STATE";
export const UPDATE_CUSTOMER_COFFEE_COUNTER_STATE = "UPDATE_CUSTOMER_COFFEE_COUNTER_STATE";
export const LOGOUT = "LOGOUT";


import {SET_CUSTOMER_COFFEE_COUNTER_STATE, SET_ITEMS, SET_ITEMS_BARISTA, SET_USER_DATA} from './mutations';
import { SET_USER } from './mutations';
import { SET_PAGE_STATE } from './mutations';
import { SET_CUSTOMER_HOMEPAGE_STATE } from './mutations'
import { setToken } from '../helpers/auth'

export default {
    [TOGGLE_SIDEBAR]({ commit },data) {
        commit(SET_ITEMS, data);
    },
    [TOGGLE_SIDEBAR_BARISTA]({ commit },data) {
        commit(SET_ITEMS_BARISTA, data);
    },
    [LOGIN]({ commit }, data) {
        setToken(data.token);
        commit(SET_USER, data);
    },
    [LOGOUT]({ commit }, data = null) {
        setToken(null);
        commit(SET_USER, {});
    },
    [UPDATE_USER]({ commit }, data) {
        commit(SET_USER_DATA, data);
    },
    [UPDATE_PAGE_STATE]({ commit }, data) {
        commit(SET_PAGE_STATE, data);
    },
    [UPDATE_CUSTOMER_PAGE_STATE]({ commit}, data){
        commit(SET_CUSTOMER_HOMEPAGE_STATE , data);
    },

    [UPDATE_CUSTOMER_COFFEE_COUNTER_STATE]({ commit}, data){
        commit(SET_CUSTOMER_COFFEE_COUNTER_STATE , data);
    }
}

import { mapActions } from "vuex";

require('../bootstrap');
import Vue from 'vue'
window.Vue = require('vue').default;
import store from "./store";
import router  from './router';
import VueToastr from "vue-toastr";
import VueLoading from 'vue-loading-overlay';
import VueSweetalert2 from 'vue-sweetalert2';
import 'vue-loading-overlay/dist/vue-loading.css';
import { LOGOUT } from "./store/actions";
Vue.use(VueToastr);
Vue.use(VueLoading);
Vue.use(VueSweetalert2);
export const eventBus = new Vue();



Vue.component('home-component', require('./customer_app/components/Main').default);



const app = new Vue({
    el: '#customerSpaApp',
    router,
    store,
    methods: {
        ...mapActions([LOGOUT])
    }
});

export default app;

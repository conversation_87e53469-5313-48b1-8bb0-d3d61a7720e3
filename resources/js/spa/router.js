import Vue from 'vue'
import VueRouter from 'vue-router';
Vue.use(VueRouter);
import store from "./store";
import customerRoutes from "./customer_app/routes";
import baristaRoutes from "./barista_app/routes";
import Main from "./customer_app/components/Main";

const routes = [
    ...customerRoutes,
    {
        path:'/barista',
        component: Main,
        children : baristaRoutes,
        meta:{barista:true}
    },
];

const router = new VueRouter({
    mode: 'history',
    routes,
    scrollBehavior(){
        return { x: 0, y: 0 }
    },
});

router.beforeEach((to, from, next) => {
    let isAuthRequired = to.meta.requiresAuth || false;
    let isCustomer = to.meta.customer || false;
    let isBarista = to.meta.barista || false;
    let isAdmin = to.meta.admin || false;
    let isGuest = to.meta.isGuest || false;

    let isLoggedIn = store.state.user.token || null;
    let permission = store.state.user.role || null;

    if(isAuthRequired && !isLoggedIn) {
        next({
            path: '/login',
            query: {redirect: to.fullPath}
        });
    }

    if (isGuest && isLoggedIn) {
        next({
            path: '/',
        });
    }

    if (isBarista && !isLoggedIn) {
        next({
            path: '/login',
            query: {redirect: to.fullPath}
        });
    }

    if (isBarista && permission == 'employee') {
        next({
            path: '/',
        });
    }


    next();
})

export default router;

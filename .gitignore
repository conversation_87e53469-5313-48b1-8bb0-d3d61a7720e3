# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
# production
/build

package-lock.json

.vscode
.vscode/*
/public/storage
/storage/*.key
/vendor
Homestead.yaml
Homestead.json
.env
public/mix-manifest.json

# ====== OS X ===========================================
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# ====== WINDOWS ===========================================
# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# ====== JETBRAINS IDEs ===========================================
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm
## Directory-based project format
.idea/
# if you remove the above rule, at least ignore user-specific stuff:
# .idea/workspace.xml
# .idea/tasks.xml
# and these sensitive or high-churn files:
# .idea/dataSources.ids
# .idea/dataSources.xml
# .idea/sqlDataSources.xml
# .idea/dynamic.xml

## File-based project format
*.ipr
*.iws
*.iml

## Additional for IntelliJ
out/

# generated by mpeltonen/sbt-idea plugin
.idea_modules/

# generated by JIRA plugin
atlassian-ide-plugin.xml

# generated by Crashlytics plugin (for Android Studio and Intellij)
com_crashlytics_export_strings.xml

# ====== VAGRANT ========================================
.vagrant/

public/BaristaApp/assets/css/*.css
public/CustomerApp/assets/css/*.css
public/js/barista-dashboard.js
public/js/customer/*.js
public/js/barista/*.js
public/js/*.js.LICENSE.txt
public/js/*.js.map
public/js/customer/*.*.map
storage/debugbar
yarn-error.log
yarn.lock
public/spa

# ====== DOCKER ========================================
.docker
/docker-compose.yml
/Dockerfile

const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
//
// mix.js('resources/js/app.js', 'public/js').vue()
//     .sass('resources/sass/app.scss', 'public/css');

/*
mix.sass('resources/src/baristaapp/assets/scss/style.scss', 'public/BaristaApp/assets/css').sourceMaps();
mix.sass('resources/src/customer-app/assets/scss/style.scss', 'public/CustomerApp/assets/css').sourceMaps();
mix.js('resources/js/barista-dashboard.js', 'public/js');
mix.js('resources/js/customer-app/home.js', 'public/js/customer')
mix.js('resources/js/customer-app/conversion.js', 'public/js/customer')
    .js('resources/js/customer-app/menu.js', 'public/js/customer').vue().version();*/

mix.js('resources/js/spa/app.js', 'public/spa')
    .vue()
mix.version(['public/spa/app.js']);

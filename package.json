{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@types/vue-select": "^3.16.8", "axios": "^0.21", "bootstrap": "^4.6.0", "jquery": "^3.6", "laravel-mix": "^6.0.19", "lodash": "^4.17.19", "popper.js": "^1.16.1", "postcss": "^8.1.14", "resolve-url-loader": "^3.1.2", "sass": "^1.32.11", "sass-loader": "^11.0.1", "vue": "^2.6.12", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14"}, "dependencies": {"apexcharts": "^4.6.0", "laravel-echo": "^1.11.0", "laravel-vue-pagination": "^2.3.1", "moment": "^2.29.1", "pusher-js": "^7.0.3", "toastr": "^2.1.4", "vue": "^2.6.14", "vue-apexcharts": "^1.7.0", "vue-button-spinner": "^2.2.1", "vue-loading-overlay": "^3.4.2", "vue-router": "^3.5.2", "vue-select": "^3.20.4", "vue-sweetalert2": "^5.0.5", "vue-toastr": "^2.1.2", "vue2-animate": "^2.1.4", "vue2-daterange-picker": "^0.6.8", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}}
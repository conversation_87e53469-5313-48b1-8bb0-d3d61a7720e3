<?php

use App\Http\Controllers\ApiV1\Auth\UserController;
use App\Http\Controllers\ApiV1\Barista\BaristaMenuController;
use App\Http\Controllers\ApiV1\Barista\BaristaNotificationController;
use App\Http\Controllers\ApiV1\Barista\BaristaOrderController;
use App\Http\Controllers\ApiV1\Barista\BaristaProfileController;
use App\Http\Controllers\ApiV1\Barista\HomeController;
use App\Http\Controllers\ApiV1\Barista\MaintenanceController;
use App\Http\Controllers\ApiV1\Customer\BaristaController;
use App\Http\Controllers\ApiV1\Customer\CustomerController;
use App\Http\Controllers\ApiV1\Customer\MenuController;
use App\Http\Controllers\ApiV1\Customer\NotificationController;
use App\Http\Controllers\ApiV1\Customer\OrderController;
use App\Http\Controllers\ApiV1\Customer\ProfileController;
use App\Http\Controllers\ApiV1\Customer\CoffeeTransferController;
use Illuminate\Support\Facades\Route;

// public API's
Route::post('/register', [UserController::class, 'register']);
Route::post('/login', [UserController::class, 'login']);
Route::post('/reset-password', [UserController::class, 'resetPasswordMail']);
Route::get('/validate/{token}', [UserController::class, 'validateToken']);
Route::post('/update-password', [UserController::class, 'resetPassword']);


//protected API's

/*
 * customer APIs starts from here
 * */
Route::group(['middleware' => ['auth:sanctum']], function () {
    Route::post('/logout', [UserController::class, 'logout']);
    //get profile
    Route::get('/users', [CustomerController::class, 'index']);
    Route::get('/user', [ProfileController::class, 'index']);
    Route::post('/user/{id}', [ProfileController::class, 'store']);
    //get menu's
    Route::get('/menu', [MenuController::class, 'index']);
    //single menu
    Route::get('/menu/{id}', [MenuController::class, 'getSingleMenu']);
    // pace an order
    Route::post('/order', [OrderController::class, 'placeOrder']);
    //order history
    Route::get('/order', [OrderController::class, 'history']);
    //cancel order
    Route::get('/order/{order}/cancel', [OrderController::class, 'cancelOrder']);
    //pending orders
    Route::get('/pending-orders', [OrderController::class, 'getPendingOrders']);
    //get user coupon details
    Route::get('/coupons', [OrderController::class, 'getUserCoupons']);
    //order analytics
    Route::get('/order/analytics', [OrderController::class, 'analytics']);
    //notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::get('/today-notifications', [NotificationController::class, 'getTodayNotifications']);
    Route::get('/prev-notifications', [NotificationController::class, 'getPreviousNotifications']);

    // user notification channels
    Route::get('/user-notification-channels', [NotificationController::class, 'getUserNotificationSettings']);
    Route::post('/user-notification-channels/update', [NotificationController::class, 'updateUserNotificationSettings']);

    //coffee conversion
    Route::get('/coffee/conversion', [MenuController::class, 'showCoffeeConversion']);

    //coffee transfer
    Route::prefix('/coffee/transfer')->group(function() {
        Route::get('/pending', [CoffeeTransferController::class, 'getPendingCoffeeTransferData']);
        Route::get('/history', [CoffeeTransferController::class, 'getGiftHistory']);
        Route::get('/{transfer}/cancel', [CoffeeTransferController::class, 'cancelPendingTransfer']);
        Route::get('/{transfer}/accept', [CoffeeTransferController::class, 'acceptTransfer']);
        Route::get('/', [CoffeeTransferController::class, 'showCoffeeTransferPageData']);
        Route::post('/', [CoffeeTransferController::class, 'saveCoffeeTransferData']);
    });


    Route::post('/coffee/conversion/save', [MenuController::class, 'saveCoffeeConversion']);
    //check barista status
    Route::get('barista-status', [BaristaController::class, 'getBaristaStatus']);
    //change password
    Route::post('/change-password', [UserController::class, 'changePassword']);


    /******
     ***barista API's are here
     ******/


    Route::group(['prefix' => 'barista'], function () {
        // get all orders
        Route::get('/orders', [HomeController::class, 'index']);
        //barista notification
        Route::get('/notifications', [BaristaNotificationController::class, 'index']);
        //get all menu's
        Route::get('/menu', [BaristaMenuController::class, 'index']);
        //menu update
        Route::put('/menu', [BaristaMenuController::class, 'update']);
        //order History
        Route::get('/order-history', [BaristaOrderController::class, 'history']);
        //order status
        Route::get('/order/status/{status}', [BaristaOrderController::class, 'showOrderByStatus']);
        //order status update
        Route::get('/order/update/{orderId}/status/{status}', [BaristaOrderController::class, 'updateOrderStatus']);
        //cancel all pending orders
        Route::get('/cancel-orders', [BaristaOrderController::class, 'cancelAllPendingOrders']);
        //mark all prcessing orders as completed
        Route::get('/complete-orders', [BaristaOrderController::class, 'completeAllProcessingOrders']);
        //place order
        Route::post('/order-place', [BaristaOrderController::class, 'placeOrder']);
        //get all customer
        Route::get('/get-customer-list', [BaristaOrderController::class, 'customerList']);
        //custom order employees
        Route::post('custom-order/employees', [BaristaOrderController::class, 'showEmployeesForOrder']);
        //get barista profile
        Route::get('profile', [BaristaProfileController::class, 'index']);
        //barista profile update
        Route::post('profile/{user}/update', [BaristaProfileController::class, 'store']);
        //get pending orders
        Route::get('pending-order', [HomeController::class, 'getPendingOrders']);
        //get processing orders
        Route::get('processing-order', [HomeController::class, 'getProcessingOrders']);
        //change barista status
        Route::get('status/{user_id}/update', [BaristaProfileController::class, 'availabilityStatusUpdate']);
        // change maintenance mode
        Route::get('maintenance/update', [MaintenanceController::class, 'handleMaintenance']);

        // analytics
        Route::get('order/analytics', [BaristaOrderController::class, 'analytics'])->name('barista.order.analytics');
    });
});

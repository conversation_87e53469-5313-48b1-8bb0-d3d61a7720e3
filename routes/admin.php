<?php

use App\Http\Controllers\Admin\GiftController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\OrderControlller;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\TeamControlller;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::domain(config('app.admin_domain'))->group(function (){
    Route::get('logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index']);

    Auth::routes();
    Route::view('/login', 'admin.auth.login')->name('login');
    Route::group(['middleware' => ['auth', 'admin','permission']], function () {
        Route::get('/', [HomeController::class, 'index'])->name('home');
        Route::post('/home/<USER>/analytics', [HomeController::class, 'coffeeAnalytics'])->name('coffees.analytics');
        Route::post('/orders/analytics', [OrderControlller::class, 'orderAnalytics'])->name('orders.analytics');
        Route::post('gift', [GiftController::class, 'store'])->name('gift.store');

        /*
         *  Employees related routes are here
         *
         * */

        Route::prefix('employees')->group(function() {
            Route::get('/', [UserController::class, 'index'])->name('employees');
            Route::get('/list', [UserController::class, 'showListView'])->name('employees.list');
            Route::post('/', [UserController::class, 'store'])->name('employees.store');
            Route::get('/{user}', [UserController::class, 'show'])->name('employees.show');
            Route::post('/{user}', [UserController::class, 'update'])->name('employees.update');
            Route::get('/{user}/delete', [UserController::class, 'destroy'])->name('employees.delete');

            Route::post('employees/{user}/coffee/{coffee}/update', [UserController::class, 'couponCountUpdate'])->name('employees.coupon.update');
            Route::get('employees/{user}/coffee/{coffee}/delete', [UserController::class, 'couponCountDelete'])->name('employees.coupon.delete');
        });
        /*
         *  Coffee Menu related routes are here.
         * */
        Route::prefix('coffee')->group(function() {
            Route::get('/', [MenuController::class, 'index'])->name('coffees.index');
            Route::post('/', [MenuController::class, 'store'])->name('coffees.store');
            Route::post('coffees/{menu}', [MenuController::class, 'update'])->name('coffees.update');
            Route::get('coffees/{menu}/delete', [MenuController::class, 'destroy'])->name('coffees.delete');
        });

        /*
         *  Teams related routes are here
         * */
        Route::prefix('team')->group(function() {
            Route::get('/', [TeamControlller::class, 'index'])->name('teams.index');
            Route::post('/', [TeamControlller::class, 'store'])->name('teams.store');
            Route::post('teams/{team}', [TeamControlller::class, 'update'])->name('team.update');
            Route::get('teams/{team}/delete', [TeamControlller::class, 'destroy'])->name('team.delete');
        });
        Route::prefix('report')->group(function() {
            Route::get('/gift/history', [OrderControlller::class, 'index'])->name('orders');
        });
        Route::prefix('settings')->group(function() {
            Route::get('/', [SettingsController::class, 'index'])->name('settings');
            Route::post('/settings/recurring-gift/save', [SettingsController::class, 'storeRecurringGift'])->name('settings.coupon.recurring-gift-store');
            Route::post('/{user}/permission', [SettingsController::class, 'permissionChanges'])->name('settings.permission.store');
        });
        /*
         *  Gift related routes are here
         * */


    });
});



var firebaseConfig = {
  apiKey: "AIzaSyBQV0teWOl_YWMO_ARUQIWuKBY44EUrwB0",
  authDomain: "team-rnd-wpdev-arcom.firebaseapp.com",
  databaseURL: "https://team-rnd-wpdev-arcom.firebaseio.com",
  projectId: "team-rnd-wpdev-arcom",
  storageBucket: "team-rnd-wpdev-arcom.appspot.com",
  messagingSenderId: "916088299463",
  appId: "1:916088299463:web:a177848b6e1125ee0f83e2"
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

messaging.requestPermission().then(function () {
  return messaging.getToken();
}).then(function (fcm_token) {
  fetch('/save/fcm-token', {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({fcm_token})
  }
).
  then(function (res) {
    return res.json();
  }).then(function (data) {
    console.log(data);
  }).catch(function (err) {
    // console.log(err.toJSON());
  })
});

messaging.onMessage(function ({notification : {title, body}}) {
  toastr.warning(body, title);
});


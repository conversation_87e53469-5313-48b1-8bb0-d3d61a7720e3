<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>WPCafe</title>
		<link rel="stylesheet" href="assets/css/bootstrap/bootstrap.min.css" />
		<link rel="stylesheet" href="assets/css/wpc-icon/style.css" />
		<link rel="stylesheet" href="assets/css/style.min.css" />
	</head>
	<body>
		<div class="WpcPageBody">
			<!-- sidebar start -->
			<section class="WpcSidebar">
				<div class="WpcSidebarContent">
					<div class="WpcSidebarContentTop">
						<a href="#" class="WpcSiteLogo">
							<h2 class="WpcSiteTitle">WPCafe</h2>
						</a>
						<a href="#" class="WpcSiteLogoCollapsed">
							<h2 class="WpcSiteTitle">WPC</h2>
						</a>
					</div>
					<nav class="WpcSidebarContentMiddle">
						<ul class="WpcSidebarNav">
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink Active">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-speedometer"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Dashboard</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-user"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Employees</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-list"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Coffee Menu</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-order"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Order History</div>
								</a>
							</li>
							<li class="WpcSidebarNavItem">
								<a href="#" class="WpcSidebarNavItemLink">
									<span class="WpcSidebarNavItemLinkIcon">
										<i class="wpc-icon wpc-settings"></i>
									</span>
									<div class="WpcSidebarNavItemLinkText">Settings</div>
								</a>
							</li>
						</ul>
					</nav>
				</div>
			</section>
			<!-- sidebar end -->

			<main class="WpcContentArea">
				<!-- site header -->
				<header class="WpcContentAreaHeader">
					<div class="WpcHeaderWrapper">
						<div
							class="
								WpcContentAreaHeaderRight
								d-flex
								align-items-center
								ml-auto
							"
						>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcNotificationIcon WpcHasNotification mr-4"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<span class="Icon">
										<i class="wpc-icon wpc-notification"></i>
									</span>
								</button>
								<div
									class="
										dropdown-menu dropdown-menu-middle
										WpcNotificationDropdown
									"
								>
									<ul>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
										<li>
											<span class="WpcNotification">
												<h6 class="WpcNotificationTitle">
													You have an order for Expresso
												</h6>
												<span class="WpcNotificationTime">17 hours ago</span>
											</span>
										</li>
									</ul>
								</div>
							</div>
							<div class="dropdown WpcProfileControl">
								<button
									class="WpcProfileControlButton"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<div class="WpcProfileImage">
										<img
											src="assets/img/bruce-mars-AndE50aaHn4-unsplash 1.png"
											alt=""
										/>
									</div>
									<div class="WpcProfileDetails">
										<h4 class="WpcUserName">Jhonathan Doe</h4>
									</div>
								</button>
								<div
									class="dropdown-menu dropdown-menu-right WpcProfileDropdown"
								>
									<ul>
										<li>
											<a href="#">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-settings"></i>
												</span>
												<p class="WpcNavigationText">Settings</p>
											</a>
										</li>
										<li>
											<a href="#">
												<span class="WpcNavigationIcon">
													<i class="wpc-icon wpc-logout"></i>
												</span>
												<p class="WpcNavigationText">Logout</p>
											</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</header>
				<div class="WpcContentAreaBody">
					<section class="WpcAnalyticsSection">
						<div class="WpcSectionTitleWrap">
							<h4 class="WpcSectionTitle">Settings</h4>
						</div>
						<form>
							<div class="WpcFormGroup">
								<label class="WpcFormLabel">Add Image</label>
								<div class="d-flex align-item-center">
									<div class="WpcImageSelector">
										<label
											class="WpcImgBox"
											style="background-image: url('assets/img/user1.png')"
										>
										</label>
									</div>
									<div class="WpcButtonGroup">
                    <label class="WpcEditButton">
                      <input type="file" name="" id="" class="d-none"/>
                      <span class="Icon">
                        <i class="wpc-icon wpc-edit"></i>
                      </span>
                      <span class="Text">Change Photo</span>
                    </label>
									</div>
								</div>
							</div>
							<div class="row WpcHasColGapInForm">
								<div class="col-md-6">
									<div class="WpcFormGroup">
										<label class="WpcFormLabel">Full Name</label>
										<input type="text" class="form-control" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="WpcFormGroup">
										<label class="WpcFormLabel">Designation</label>
										<input type="text" class="form-control" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="WpcFormGroup">
										<label class="WpcFormLabel">Email</label>
										<input type="email" class="form-control" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="WpcFormGroup">
										<label class="WpcFormLabel">Password</label>
										<input type="password" class="form-control" />
									</div>
								</div>
							</div>
              <div class="WpcFormGroup align-items-baseline">
                <button class="WpcButton WpcFilled mt-4">
                  <span class="Text">Save Changes</span>
                </button>
              </div>
						</form>
					</section>
				</div>
			</main>
		</div>
		<script src="assets/js/jquery/jquery-3.4.1.min.js"></script>
		<script src="assets/js/bootstrap/bootstrap.bundle.min.js"></script>
		<script src="assets/js/custom.js"></script>
	</body>
</html>

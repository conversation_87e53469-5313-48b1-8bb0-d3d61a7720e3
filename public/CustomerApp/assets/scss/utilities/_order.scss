
.order__tracking__wrap {
    padding: 10px 15px;
}
.order__tracking {
    margin-top: 15px;
    display: flex;
    align-items: center;
    border: 1px solid #d17842;
    border-radius: 20px;
    padding: 5px;
    padding-right: 10px;
    background: #d17842;
    transition: all .3s ease;
    position: relative;
    &:hover {
        background: $primary;
        .order__tracking__body {
            p {
                color: $white;
            }
        }
    }
    & &__menu__wrapper{
        background-color: #342520;
        display: flex;
        flex-flow: column;
        align-items: center;
        border-radius: 20px;
        padding: 10px;
        margin-right: 15px;
            h5 {
                font-size: 15px;
                font-weight: 400;
                color: $white;
                margin: 8px 0 0;
            }
    }
    & &__thumb {
        flex: 0 0 80px;
        min-width: 80px;
        height: 80px;
        width: 80px;
        border-radius: 20px;
        overflow: hidden;
    }
    & &__body {
        flex: 1 0 auto;
        h3 {
            font-size: 18px;
            font-weight: 500;
            color: $white;
            margin-bottom: 2px;
        }
        .cancel__order {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 12px;
            border-radius: 10px;
        }
        p {
            font-size: 14px;
            font-weight: 400;
            color: #f3f3f3;
            transition: all .3s ease;
        }
    }
}

.wpc-order__history {
    padding: 0 15px;
    margin: 0;
    list-style: none;
    .item__ordered {
        background-color: $accent;
        border: 1px solid rgba($accent, .1);
        border-radius: 10px;
        padding: 15px;
        &:not(:last-child) {
            margin-bottom: 15px;
        }
        .order__top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            .order__status {
                font-size: 12px;
                font-weight: 400;
                color: $primary;
                background: rgba($primary, .1);
                padding: 3px 10px;
                border-radius: 15px;
                border: 1px solid rgba($primary, .5);
            }
        }
        .order__id {
            font-size: 13px;
            font-weight: 400;
            color: $primary;
        }
        .order__details {
            display: flex;
            align-items: center;
            .item__thumb {
                width: 50px;
                height: 50px;
                min-width: 50px;
                margin-right: 10px;
                border-radius: 10px;
            }
            .item__body {
                h5 {
                    font-size: 16px;
                    font-weight: 400;
                    color: $white;
                    margin-bottom: 5px;
                }
                .order__date {
                    font-size: 13px;
                    font-weight: 400;
                    color: $secondary;
                }
            }
        }
    }
}

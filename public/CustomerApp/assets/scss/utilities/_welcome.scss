

.account__controller {
    display: flex;
    flex-direction: column;
    text-align: center;
    justify-content: space-between;
    height: 100vh;
    padding: 15px;
    background: url('../../img/access-bg.jpg') no-repeat center;
    background-size: cover;
    .site__welcome__logo {
        padding-top: 70%;
    }
    .account__access__content {
        padding-top: 35%;
        h3 {
            font-size: 35px;
            font-weight: 500;
            color: $dark;
            margin-bottom: 20px;
        }
        p {
            font-size: 16px;
            font-weight: 400;
            color: $secondary;
        }
        .account__access__form {
            margin-top: 23%;
            background: rgba(196, 196, 196, .1);
            border-radius: 15px;
            padding: 35px 25px;
            .form__group {
                margin-bottom: 20px;
                .fancy__form {
                    background: transparent;
                    border: 0;
                    border-bottom: .5px solid rgba($white, .1);
                    color: #83858E;
                    height: 40px;
                    &::-webkit-input-placeholder { 
                        color: #83858E;
                    }
                    &::-moz-placeholder { 
                        color: #83858E;
                    }
                    &:-ms-input-placeholder {
                        color: #83858E;
                    }
                    &:-moz-placeholder {
                        color: #83858E;
                    }
                }
            }
            .WpcButton {
                margin-top: 10px;
            }
        }
    }
    .alternet__access {
        margin-bottom: 35px;
        .WpcButton {
            margin-bottom: 25px;
        }
        .alternet__text {
            color: $secondary;
            font-weight: 400;
            a {
                color: $dark;
                font-weight: 500;
            }
        }
    }
}

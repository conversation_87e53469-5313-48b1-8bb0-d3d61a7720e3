
.wpc-notificatons {
    padding: 0 15px;
    .notification__item {
        border: 1px solid rgba($accent, .1);
        background-color: $accent;
        border-radius: 10px;
        padding: 15px 25px;
        &:not(:last-child) {
            margin-bottom: 15px;
        }
        .notification__time {
            font-size: 13px;
            font-weight: 400;
            color: $secondary;
            line-height: 1.5;
        }
        .card {
            border: 0;
            background: transparent;
            .card-header {
                padding: 0;
                background: transparent;
                border-bottom: 0;
                
                h2 {
                    .btn {
                        padding: 0;
                        font-size: 16px;
                        font-weight: 400;
                        color: $white;
                        text-decoration: none;
                        position: relative;
                        span {
                            position: absolute;
                            top: 5px;
                            right: 0;
                            color: $grey;
                            font-size: 10px;
                            transform: rotate(0);
                            transition: all .3s ease;
                        }
                        &.collapsed {
                            span {
                                transform: rotate(-90deg);
                            }
                        }
                    }
                }
            }
            .card-body {
                padding: 0;
                padding: 10px 0;
                p {
                    font-size: 14px;
                    font-weight: 400;
                    color: $grey;
                }
            }
        }
    }
}



.header {
    background: $sidebar;
    padding: 30px 15px 85px;
    box-shadow: inset 0 0 0 1000px rgba(16, 20, 25, .65);
    .header__nav {
        display: flex;
        justify-content: space-between;
        padding: 0 15px 20px;
        .nav__icon {
            width: 50px;
            a {
                color: $white;
                font-size: 22px;
            }
        }
        .notification__icon {
            a {
                color: $primary;
                font-size: 22px;
                position: relative;
                &:before {
                    position: absolute;
                    top: 0;
                    right: 2px;
                    height: 8px;
                    width: 8px;
                    background: $success;
                    border-radius: 50%;
                    content: '';
                }
            }
        }
    }
    .header__greeting {
        padding: 15px;
        position: relative;
        .barista_available_status {
            color: $white;
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1rem;
            padding-left: 18px;
            &::before {
                position: absolute;
                top: 3px;
                left: 0;
                height: 12px;
                width: 12px;
                border-radius: 50%;
                background: $secondary;
                content: "";
            }
            &.isOnline {
                &::before {
                    background: $success;
                }
            }
        }
        p {
            font-size: 17px;
            color: $primary;
            font-weight: 700;
        }
        h2 {
            font-size: 32px;
            font-weight: 700;
            color: $white;
        }
    }
}

.section__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    margin-bottom: 15px;
    .section__header__title {
        font-size: 20px;
        padding-left: 5px;
        font-weight: 700;
        color: $white;
    }
    .section__header__icon {
        height: 40px;
        width: 40px;
        border-radius: 10px;
        background-color: #242c39;
        color: $primary;
        line-height: 40px;
        text-align: center;
        i {
            color: $primary;
        }
    }
}

.page__header {
    padding: 20px 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    .left__content, 
    .right__content {
        flex: 1;
    }
    .left__content {
        .back__button {
            font-size: 16px;
            font-weight: 400;
            color: $white;
        }
    }
    .page__title {
        flex: 0 0 auto;
        text-align: center;
        h5 {
            font-size: 16px;
            font-weight: 400;
            color: $white;
        }
    }
    .right__content {
        text-align: right;
        .clear__button {
            font-size: 14px;
            font-weight: 400;
            color: $white;
        }
    }
}

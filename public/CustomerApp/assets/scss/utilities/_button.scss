.WpcButton {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-height: 50px;
	width: 100%;
	padding: 1px;
	z-index: 1;
	border-radius: 15px;
	background: rgba($primary, 0.1);
	border: 1px solid $primary;
	font-weight: 500;
	@include transition;
	@include transition-properties(background, box-shadow);
	position: relative;
	.Icon {
		height: 24px;
		width: 24px;
		min-width: 24px;
		background: $dark;
		color: $white;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		border-radius: 50px;
		font-size: 8px;
		@include transition($duration: 0.2s);
		@include transition-properties(background, color, filter);
	}
	.Text {
		padding: 0 25px;
		font-size: 1rem;
		font-weight: 500;

		&:not(:first-child) {
			padding-left: 10px;
		}
		&:not(:last-child) {
			padding-right: 10px;
		}
	}
	&:hover {
		background: $primary;
		box-shadow: 0 20px 20px -10px rgba($primary, 0.4);
	}
	&.WpcFilled {
		background: $primary;
		color: $white;
		&:hover {
			background: darken($primary, 10%);
			box-shadow: 0 20px 20px -10px rgba($primary, 0.4);
		}
	}
	&.WpcDisabled {
		background: transparent;
		border-color: rgba($secondary, 0.2);
		cursor: not-allowed;
		.Text {
			color: $secondary;
		}
		&:hover {
			background: transparent;
			box-shadow: none;
		}
	}
}
.WpcButton[disabled], .WpcButton:disabled{
		opacity: .75;
}
.WpcEditButton {
	cursor: pointer;
	display: inline-flex;
	align-items: center;
	align-self: center;
	.Icon {
		height: 40px;
		width: 40px;
		min-width: 40px;
		border-radius: 10px;
		border: 1px solid rgba($dark, 0.1);
		display: inline-flex;
		align-items: center;
		justify-content: center;
		font-size: 1rem;
		color: $primary;
		@include transition();
		@include transition-properties(border-color, background-color, color);
		&:not(:last-child) {
			margin-right: 10px;
		}
	}
	.Text {
		font-size: 0.875rem;
		line-height: 1.125rem;
		color: $secondary;
	}
	&:hover {
		.Icon {
			border-color: $primary;
			background-color: $primary;
			color: $white;
		}
	}
	&.WpcBigSize {
		.Icon {
			height: 50px;
			width: 50px;
			min-width: 50px;
			border-radius: 12px;
			font-size: 1.25rem;
			&:not(:last-child) {
				margin-right: 12px;
			}
		}
		.Text {
			font-size: 1rem;
		}
	}
	&.WpcFilled {
		.Icon {
			border-color: $primary;
			background: $primary;
			color: $dark;
		}
		&:hover {
			.Icon {
				background: darken($primary, 10%);
				box-shadow: 0 20px 20px -10px rgba($primary, 0.4);
			}
		}
	}
	&.WpcDisabled {
		.Icon {
			background: transparent;
			border-color: rgba($secondary, 0.2);
			color: rgba($secondary, 0.2);
			cursor: not-allowed;
		}
		.Text {
			color: $secondary;
		}
		&:hover {
			.Icon {
				background: transparent;
				box-shadow: none;
			}
		}
	}
}

.WpcAddButton {
	margin-bottom: 10px;
	display: inline-flex;
	align-items: center;
	cursor: pointer;
	.Icon {
		height: 20px;
		width: 20px;
		min-width: 20px;
		border-radius: 20px;
		border: 1px solid rgba($secondary, 0.2);
		color: $secondary;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		font-size: 0.5rem;
		@include transition();
		@include transition-properties(border-color, color);
		&:not(:last-child) {
			margin-right: 10px;
		}
	}
	.Text {
		font-size: 0.875rem;
		line-height: 1.25;
		color: $secondary;
		@include transition($for: color);
	}
	&:hover {
		.Icon {
			border-color: $secondary;
			color: $dark;
		}
		.Text {
			color: $dark;
		}
	}
}

.WpcBackButton {
	display: flex;
	align-items: center;
	color: $dark;
	.Icon {
		font-size: 0.875rem;
		margin-right: 10px;
		display: inline-flex;
		align-items: center;
    color: $secondary;
    @include transition();
	}
	.Text {
		font-size: 1.125rem;
		font-weight: 400;
		display: flex;
    color: $secondary;
    @include transition();
	}
	&:hover {
    .Icon {
      color: $dark;
    }
    .Text {
      color: $dark;
    }
	}
}

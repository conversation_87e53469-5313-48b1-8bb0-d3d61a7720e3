var options = [];

$('.TableToggler .dropdown-menu .Checkbox').on('click', function (event) {
  var $target = $(event.currentTarget),
    val = $target.attr('data-value'),
    $inp = $target.find('input'),
    idx;

  if ((idx = options.indexOf(val)) > -1) {
    options.splice(idx, 1);
    setTimeout(function () {
      $inp.prop('checked', false);
    }, 0);
  } else {
    options.push(val);
    setTimeout(function () {
      $inp.prop('checked', true);
    }, 0);
  }
  return false;
});

// section gap
.WpcSectionGap {
	margin-top: 60px;
}
.WpcPaginationWrapper {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 30px;
}
// col gap
.WpcHasColGap {
	margin-bottom: -50px;
	*[class*='col-'],
	> .col {
		margin-bottom: 50px !important;
	}
	@include respond-below(md) {
		margin-bottom: -30px;
		*[class*='col-'],
		> .col {
			margin-bottom: 30px !important;
		}
	}
	@include respond-below(sm) {
		margin-bottom: -15px;
		*[class*='col-'],
		> .col {
			margin-bottom: 15px !important;
		}
	}
}
.WpcHasColGapInForm {
	margin-bottom: -20px;
	*[class*='col-'],
	> .col {
		margin-bottom: 20px !important;
	}
}

// section title
.WpcSectionTitleWrap {
	display: flex;
	align-items: center;
	margin-bottom: 50px;
	flex-wrap: wrap;
	@include respond-below(md) {
		flex-direction: column;
		align-items: baseline;
		margin-bottom: 30px;
	}
	@include respond-below(sm) {
		margin-bottom: 20px;
	}
	&--flex {
		justify-content: space-between;
		.WpcSectionTitle {
			margin-right: auto !important;
		}
		.timeline__filter {
			width: 130px;
			text-align: right;
			margin-right: 20px;
			#reportrange {
				background: #fff;
				cursor: pointer;
				padding: 12px 10px;
				border: 1px solid rgba(131, 133, 142, 0.2);
				width: 100%;
				height: auto;
				border-radius: 12px;
				span {
					font-size: 14px;
					color: #19224d;
				}
			}
		}
	}
}
.WpcSectionTitle {
	font-weight: 500;
	line-height: 1;
	font-size: 2.375rem;
	color: $dark;
	@include respond-below(lg) {
		font-size: 1.75rem;
	}
	@include respond-below(md) {
		margin-bottom: 10px;
	}
}
.WpcSectionSemiTitle {
	font-weight: 500;
	line-height: 1.3;
	font-size: 1.375rem;
	color: $primary;
}

// applicant empty message
.WpcEmptyMessage {
	padding: 30px 26.5px;
	font-size: 1.125rem;
	line-height: 1.5;
	background: #fff;
	border-radius: 10px;
	color: #ff5f74;
	font-weight: 500;
	&:not(first-child) {
		margin-top: 30px;
	}
	&:not(last-child) {
		margin-bottom: 30px;
	}
}

.WpcButtonGroup {
	display: flex;
	margin-left: -10px;
	margin-right: -10px;
	margin-bottom: -10px;
	@include respond-below(lg) {
		flex-wrap: wrap;
	}
	> * {
		margin-left: 10px;
		margin-right: 10px;
		margin-bottom: 10px;
	}
}

.WpcCounterCardWrapper {
	width: 400px;
	flex-basis: 400px;
	display: flex;
	flex-direction: column;
	@include respond-between(xxl, hl) {
		width: 350px;
		flex-basis: 350px;
	}
	@include respond-below(xxl) {
		flex: 0 0 100%;
		display: grid;
		grid-gap: 30px;
		grid-template-columns: repeat(2, 1fr);
	}
	@include respond-between(md, lg) {
		grid-gap: 15px;
	}
	@include respond-below(md) {
		grid-template-columns: repeat(1, 1fr);
		grid-gap: 10px;
	}
	.WpcCounterCard {
		display: flex;
		align-items: center;
		border: 2px solid rgba($primary, 0.3);
		border-radius: 38px;
		padding: 30px;
		@include transition();
		@include respond-below(hl) {
			padding: 20px;
		}
		@include respond-between(md, lg) {
			padding: 15px;
			border-radius: 32px;
		}
		.WpcCounterContent {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 30px;
			@include respond-below(hl) {
				margin-left: 20px;
			}
			.WpcCounterCount {
				font-size: 3.125rem;
				line-height: 1;
				color: $primary;
				letter-spacing: -0.03em;
				margin-bottom: 10px;
				display: flex;
				align-items: flex-end;
				@include respond-below(hl) {
					font-size: 2.75rem;
				}
				span {
					font-size: 1rem;
					line-height: 1;
					letter-spacing: -0.03em;
					color: $secondary;
					margin-left: 5px;
					margin-bottom: 5px;
					@include transition();
				}
			}
			.WpcCounterTitle {
				font-size: 1.5rem;
				line-height: 1.2;
				color: $dark;
				letter-spacing: -0.03em;
				@include transition();
			}
		}
		&.Active,
		&:hover {
			background: $sidebar;
			.WpcCounterContent {
				.WpcCounterCount {
					span {
						color: $white;
					}
				}
				.WpcCounterTitle {
					color: $white;
				}
			}
		}
		&:not(:last-child) {
			margin-bottom: 30px;
			@include respond-below(hl) {
				margin-bottom: 20px;
			}
			@include respond-below(xxl) {
				margin-bottom: 0;
			}
		}
	}
}

// Data Table
.WpcDataTableWrapper {
	display: block;
	width: 100%;
	margin-top: -15px;
	margin-bottom: -15px;
	overflow-x: auto;
	.WpcDataTable {
		width: 100%;
		display: table;
		border-collapse: separate !important;
		border-spacing: 0 15px;
		thead {
			tr {
				th {
					padding: 0px 10px;
					font-size: 1rem;
					font-weight: 500;
					line-height: 1;
					color: $secondary;
					&:first-child {
						padding-left: 25px;
						.WpcCheckbox {
							.Text {
								padding-left: 20px;
							}
						}
					}
					&:last-child {
						padding-right: 25px;
					}
					&.WpcTableCheckbox {
						width: 60px;
					}
					&.WpcTableActionHeader {
						width: 45px;
					}
				}
			}
		}
		tbody {
			tr {
				td {
					padding: 20px 10px;
					font-size: 1.125rem;
					font-weight: 400;
					line-height: 1;
					color: $secondary;
					border-top: 1px solid rgba($primary, 0.3);
					border-bottom: 1px solid rgba($primary, 0.3);
					@include transition($for: border-color);
					@include respond-between(xxl, hl) {
						font-size: 1rem;
					}
					&:first-child {
						padding-left: 25px;
						border-left: 1px solid rgba($primary, 0.3);
						border-top-left-radius: 20px;
						border-bottom-left-radius: 20px;
						.WpcCheckbox {
							.Text {
								padding-left: 20px;
							}
						}
					}
					&:last-child {
						padding-right: 25px;
						border-right: 1px solid rgba($primary, 0.3);
						border-top-right-radius: 20px;
						border-bottom-right-radius: 20px;
					}
					.WpcTableInfoBox {
						display: flex;
						align-items: center;
						.WpcTableInfoBoxImage {
							background-color: rgba($primary, 0.1);
							height: 100px;
							width: 100px;
							min-width: 100px;
							border-radius: 22px;
							background-repeat: no-repeat;
							background-attachment: scroll;
							background-position: center center;
							background-size: cover;
							@include respond-below(hl) {
								height: 80px;
								width: 80px;
								min-width: 80px;
								border-radius: 15px;
							}
							@include respond-below(xl) {
								height: 70px;
								width: 70px;
								min-width: 70px;
								border-radius: 15px;
							}
							&:not(:last-child) {
								margin-right: 20px;
							}
						}
						.WpcTableInfoBoxDetails {
							display: inline-flex;
							flex-direction: column;
							justify-content: center;
							.WpcTableInfoBoxTitle {
								font-size: 1.875rem;
								font-weight: 500;
								line-height: 1;
								letter-spacing: -0.03em;
								color: $dark;
								@include respond-between(lg, hl) {
									font-size: 1.375rem;
								}
								&:not(:last-child) {
									margin-bottom: 15px;
								}
							}
							.WpcTableInfoBoxInfo {
								font-size: 1.25rem;
								line-height: 1;
								font-weight: 400;
								color: $secondary;
								@include respond-between(xxl, hl) {
									font-size: 1rem;
								}
							}
							.WpcTableInfoBoxSemiInfo {
								font-size: 0.875rem;
								line-height: 1;
								font-weight: 400;
								color: $secondary;
							}
						}
						.WpcTableImageCollection {
							display: flex;
							align-content: center;
							flex-direction: row;

							.WpdTableAddImageButton,
							.WpcTableInfoBoxImage {
								display: inline-flex;
								margin-right: -5px;
								height: 40px;
								width: 40px;
								min-width: 40px;
								border: 2px solid $white;
							}
							.WpdTableAddImageButton {
								background: $success;
								border-radius: 50%;
								display: flex;
								align-items: center;
								justify-content: center;
								font-size: 16px;
								color: $white;
							}
						}
					}
					.WpcTableActionWrapper {
						position: relative;
						display: flex;
						align-items: center;
						min-height: 20px;
						.WpcToggleDropdown {
							position: absolute;
							top: 0;
							right: 0;
							> button {
								display: inline-flex;
								justify-content: center;
								color: $secondary;
								font-size: 1rem;
								opacity: 0.5;
								width: 20px;
								height: 20px;
								@include transition($duration: 0.1s);
								&:hover {
									opacity: 1;
								}
							}
						}
					}
					.WpcCoffeePreview {
						width: 80px;
						@include respond-between(xxl, hl) {
							width: 70px;
						}
						.WpcCoffeePreviewImage {
							border-top-left-radius: 12px;
							border-top-right-radius: 12px;
						}
						.WpcCoffeePreviewDetails {
							padding: 5px 2px;
							font-size: 0.875rem;
							border-bottom-left-radius: 12px;
							border-bottom-right-radius: 12px;
						}
					}
					.WpcCoffeeInfoCard {
						display: flex;
						align-items: center;
						.WpcCoffeeInfoIcon {
							height: 50px;
							width: 50px;
							min-width: 50px;
							border-radius: 10px;
							display: inline-flex;
							align-items: center;
							justify-content: center;
							font-size: 1.125rem;
							background: $primary;
							color: $white;
							&:not(:last-child) {
								margin-right: 15px;
							}
						}
						.WpcCoffeeInfoText {
							display: inline-flex;
							align-items: center;
							font-size: 1.875rem;
							font-weight: 400;
							line-height: 1;
							letter-spacing: -0.03em;
							color: $dark;
						}
					}
				}
				&:focus-within,
				&:hover {
					td {
						border-color: $primary;
						&:first-child {
							border-color: $primary;
						}
						&:last-child {
							border-color: $primary;
						}
					}
				}
			}
		}
	}
	.WpcEmptyDataMessage {
		background: $white;
		width: 100%;
		margin-bottom: 10px;
		padding: 10px 30px;
		min-height: 50px;
		display: flex;
		align-items: center;
		color: $dark;
	}
}

// post grid
.WpcPostGridWrapper {
	.WpcPostGrid {
		background: $white;
		display: flex;
		flex-direction: column;
		border-radius: 38px;
		.WpcPostGridThumbnail {
			border-top-left-radius: 38px;
			border-top-right-radius: 38px;
			overflow: hidden;
			padding-top: 87.5%;
			background-repeat: no-repeat;
			background-position: center center;
			background-attachment: scroll;
			background-size: cover;
		}
		.WpcPostGridDetails {
			padding: 30px;
			padding-bottom: 40px;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-bottom-left-radius: 38px;
			border-bottom-right-radius: 38px;
			border: 1px solid rgba($primary, 0.2);
			border-top-color: transparent;
			position: relative;
			.WpcPostGridTitle {
				font-size: 1.875rem;
				color: $dark;
				font-weight: 500;
				line-height: 1;
				text-align: center;
				@include respond-between(xxl, hl) {
					font-size: 1.5rem;
				}
			}
			.WpcPostGridContent {
				font-size: 1.25rem;
				line-height: 1;
				color: $dark;
				line-height: 1.6;
				margin-top: 15px;
				text-align: center;
				span {
					color: $secondary;
				}
			}
			.WpcButton {
				margin-top: 30px;
			}
			.WpcToggleDropdown {
				position: absolute;
				top: 20px;
				right: 15px;
				> button {
					display: inline-flex;
					justify-content: center;
					color: $secondary;
					font-size: 1rem;
					opacity: 0.5;
					width: 20px;
					height: 20px;
					@include transition($duration: 0.1s);
					&:hover {
						opacity: 1;
					}
				}
			}
		}
	}
}

// Info Box
.WpcInfoBoxWrapper {
	.WpcInfoBox {
		display: flex;
		align-items: center;
		padding: 20px;
		border: 1px solid rgba($primary, 0.2);
		border-radius: 32px;
		min-height: 140px;
		position: relative;
		@include transition($for: border);
		.WpcRibbonStyle {
			position: absolute;
			left: -10px;
			top: -10px;
			z-index: 1;
			overflow: hidden;
			width: 105px;
			height: 105px;
			text-align: right;
			span {
				height: 25px;
				font-size: 12px;
				font-weight: bold;
				color: rgb(255, 255, 255);
				text-transform: uppercase;
				text-align: center;
				line-height: 1;
				transform: rotate(-45deg);
				transform-origin: center;
				width: 130px;
				display: block;
				position: absolute;
				top: 26px;
				left: -26px;
				background: $primary;
				display: flex;
				align-items: center;
				justify-content: center;
				&:before {
					content: '';
					position: absolute;
					left: 0px;
					top: calc(100% - 1px);
					z-index: -1;
					border-width: 7px;
					border-style: solid;
					border-color: darken($primary, 5%) transparent transparent darken($primary, 5%);
				}
				&:after {
					content: '';
					position: absolute;
					right: 0px;
					top: calc(100% - 1px);
					z-index: -1;
					border-width: 7px;
					border-style: solid;
					border-color: darken($primary, 5%) darken($primary, 5%) transparent transparent;
				}
			}
		}
		.WpcInfoBoxImage {
			background-color: rgba($primary, 0.1);
			height: 100px;
			width: 100px;
			min-width: 100px;
			overflow: hidden;
			background-repeat: no-repeat;
			background-position: center center;
			background-attachment: scroll;
			background-size: cover;
			border-radius: 22px;
			margin-right: 30px;
			@include respond-between(xxl, hl) {
				height: 80px;
				width: 80px;
				min-width: 80px;
			}
			@include respond-below(sm) {
				height: 80px;
				width: 80px;
				min-width: 80px;
				margin-right: 15px;
			}
		}
		.WpcInfoBoxDetails {
			display: flex;
			flex-direction: column;
			padding-right: 10px;
			flex-grow: 1;
			.WpcInfoBoxTitle {
				font-size: 1.875rem;
				font-weight: 500;
				line-height: 1;
				letter-spacing: -0.03em;
				color: $dark;
				margin-bottom: 15px;
				margin-right: 20px;
				@include respond-between(xxl, hl) {
					font-size: 1.5rem;
				}
				@include respond-below(sm) {
					font-size: 1.5rem;
				}
			}
			.WpcPostGridContent {
				font-size: 0.875rem;
				line-height: 1.3;
				color: $secondary;
			}
		}
		.WpcAvailableCount {
			min-width: 40px;
			height: 40px;
			background: $white;
			color: $dark;
			font-size: 1rem;
			border: 1px solid rgba($dark, 0.3);
			border-radius: 10px;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			margin-right: 25px;
			margin-top: 5px;
		}
		.WpcToggleDropdown {
			position: absolute;
			top: 25px;
			right: 15px;
			> button {
				display: inline-flex;
				color: $secondary;
				font-size: 1rem;
				opacity: 0.5;
				width: 20px;
				height: 20px;
				@include transition($duration: 0.1s);
				&:hover {
					opacity: 1;
				}
			}
		}
		&.WpcAddItem {
			height: 100%;
			background: rgba($primary, 0.1);
			border: 2px dashed rgba($primary, 0.6);
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			cursor: pointer;
			@include transition();
			.WpcInfoBoxTitle {
				font-size: 1.5rem;
				color: $primary;
				font-weight: 400;
				letter-spacing: -0.03em;
			}
			&:hover {
				background: rgba($primary, 0.15);
				border: 2px dashed rgba($primary, 0.75);
			}
		}
		&:hover {
			border-color: $primary;
		}
	}
}

// coffee preview
.WpcCoffeePreviewWrapper {
	padding-top: 20px;
	display: flex;
	flex-wrap: wrap;
	margin-left: -5px;
	margin-right: -5px;
	margin-bottom: -10px;
	position: relative;
	.WpcCoffeePreview {
		margin-left: 5px;
		margin-right: 5px;
		margin-bottom: 10px;
	}
	&:before {
		content: '';
		position: absolute;
		left: 5px;
		right: 5px;
		top: 0;
		border-top: 1px solid rgba($secondary, 0.2);
	}
}
.WpcCoffeePreview {
	display: inline-flex;
	flex-direction: column;
	width: 130px;
	position: relative;
	@include respond-between(xxl, hl) {
		width: 100px;
	}
	@include respond-below(xl) {
		width: 100px;
	}
	@include respond-between(md, lg) {
		width: 90px;
	}
	.deleteItem {
		position: absolute;
		top: 10px;
		right: 10px;
		height: 20px;
		width: 20px;
		background: $primary;
		color: $white;
		font-size: 12px;
		display: inline-flex;
		justify-content: center;
		align-items: center;
		border-radius: 4px;
	}
	.WpcCoffeePreviewImage {
		background-color: rgba($primary, 0.1);
		border-top-left-radius: 18px;
		border-top-right-radius: 18px;
		overflow: hidden;
		padding-top: 87.5%;
		background-repeat: no-repeat;
		background-position: center center;
		background-attachment: scroll;
		background-size: cover;
	}
	.WpcCoffeePreviewDetails {
		padding: 12px 2px;
		font-size: 1rem;
		line-height: 1;
		background: $white;
		color: $dark;
		width: 100%;
		text-align: center;
		border: 1px solid rgba($primary, 0.2);
		border-bottom-left-radius: 18px;
		border-bottom-right-radius: 18px;
		@include respond-between(xxl, hl) {
			font-size: 0.875rem;
			padding: 8px 2px;
		}
	}
}

.WpcFont14 {
	font-size: 0.875rem;
}
.WpcFont500 {
	font-weight: 500;
}
.WpcFont700 {
	font-weight: 700;
}
.WpcCursorPointer {
	cursor: pointer;
}

.WpcSearchForm button {
	position: absolute;
	font-size: 1.125rem;
	height: 50px;
	right: 0;
	top: 0;
	color: #d9dadc;
	padding: 0 10px;
	line-height: 1;
}

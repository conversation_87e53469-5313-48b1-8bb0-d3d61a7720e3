.WpcFormGroup {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	.WpcFormLabel {
		font-size: 1rem;
		color: $secondary;
		margin-bottom: 10px;
		margin-right: 10px;
		line-height: 1;
	}
	&:not(:last-child) {
		margin-bottom: 20px;
	}
}

// form control
input.form-control {
	height: 50px;
	padding: 0 20px !important;
	font-size: 1rem;
	color: $dark;
	background: $white;
	border-color: rgba($secondary, 0.2);
	border-radius: 12px;
	border-width: 1px;
	&:focus {
		box-shadow: 0 0 5px rgba($dark, 0.15);
	}
	&::placeholder {
		color: lighten($secondary, 20%);
	}
	&:not(:last-child) {
		margin-bottom: 5px;
	}
}

select.form-control {
	height: 50px;
	padding: 0 20px !important;
	font-size: 1rem;
	color: $dark;
	background: $white;
	border-color: rgba($secondary, 0.2);
	border-radius: 12px;
	border-width: 1px;
  appearance: none;
  -webkit-appearance: none;
	&:focus {
		box-shadow: 0 0 5px rgba($dark, 0.15);
	}
	&::placeholder {
		color: lighten($secondary, 20%);
	}
	&:not(:last-child) {
		margin-bottom: 5px;
	}
}

// form control
textarea.form-control {
	padding: 15px 20px !important;
	min-height: 100px;
	font-size: 1rem;
	color: $dark;
	background: $white;
	border-color: rgba($secondary, 0.2);
	border-radius: 12px;
	border-width: 1px;
	&:focus {
		box-shadow: 0 0 5px rgba($dark, 0.15);
	}
	&::placeholder {
		color: lighten($secondary, 20%);
	}
	&:not(:last-child) {
		margin-bottom: 5px;
	}
}

// for error message
.WpcHasError {
	border: 1px solid $danger !important;
	&:focus {
		box-shadow: 0 0 5px rgba($danger, 0.15) !important;
	}
}

// search form
.WpcSearchForm {
	position: relative;
  .form-control {
    height: 50px;
    padding: 0 45px 0 20px !important;
    font-size: 1rem;
    color: $dark;
    background: $white;
    border-color: rgba($secondary, 0.2);
    border-radius: 18px;
    border-width: 1px;
    width: 200px;
    &:focus {
      box-shadow: 0 0 5px rgba($dark, 0.15);
    }
    &::placeholder {
      color: lighten($secondary, 20%);
    }
    &::-ms-clear,
    &::-ms-reveal {
      display: none;
      width: 0;
      height: 0;
    }
    /* clears the 'X' from Chrome */
    &::-webkit-search-decoration,
    &::-webkit-search-cancel-button,
    &::-webkit-search-results-button,
    &::-webkit-search-results-decoration {
      display: none;
    }
  }
	&:after {
		// font-family: 'wpc-icon';
		// content: '\e915';
		// position: absolute;
		// pointer-events: none;
		// font-size: 1.125rem;
		// top: 50%;
		// transform: translateY(-50%);
		// right: 15px;
		// color: darken($light, 5%);
		// margin-left: 20px !important;
	}
}

.WpcCheckbox {
	cursor: pointer;
	font-size: 0.875rem;
	font-weight: 500;
	@include transition;
	.Text {
		font-size: 0.875rem;
		color: $dark;
		display: inline-flex;
		align-items: flex-start;
		line-height: 20px;
		position: relative;
		padding-left: 30px;
		flex-wrap: wrap;
		min-height: 20px;
		a {
			margin-left: 5px;
			margin-right: 5px;
		}
	}
	input[type='checkbox'] {
		display: none;
		+ .Text:before {
			position: absolute;
			top: 0;
			left: 0;
			content: '';
			color: $light;
			background: transparent;
			border: 2px solid $light;
			border-radius: 4px;
			height: 20px;
			width: 20px;
			min-width: 20px;
			display: inline-flex;
			justify-content: center;
			align-items: center;
			font-family: 'Wpc-icon' !important;
			font-size: 0.625rem;
		}
		&:checked + .Text:before {
			content: '\e906';
			line-height: 1;
			color: $success;
			border-color: $success;
		}
	}
}

// for disable
input:disabled {
	cursor: no-drop;
	background-color: darken($light, 3%) !important;
}

.WpcImageSelector {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	.WpcImgBox {
		background: rgba($primary, 0.1);
		height: 100px;
		width: 100px;
		min-width: 100px;
		overflow: hidden;
		border-radius: 20px;
		position: relative;
		margin-right: 20px;
		background-repeat: no-repeat;
		background-position: center center;
		background-attachment: scroll;
		background-size: cover;
		input[type='file'] {
			display: none;
		}
		.WpcDummyImage {
			height: 100%;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 3px dashed rgba($primary, 0.6);
			border-radius: 20px;
			font-size: 1.75rem;
			color: $primary;
			cursor: pointer;
		}
	}
}

.WpcFilterSelectorWrapper {
	display: inline-flex;
	align-items: center;
	position: relative;
	label {
		font-size: 1rem;
		line-height: 1;
		color: $secondary;
		margin-right: 10px;
	}
	.WpcFilterSelector {
		height: 50px;
		padding: 0 45px 0 20px;
		background: transparent !important;
		border: 1px solid rgba($secondary, 0.2);
		border-radius: 18px;
		font-size: 1rem;
		line-height: 1;
		color: $dark;
		-webkit-appearance: none;
	}
	&:before {
		font-family: 'Wpc-icon';
		content: '\e90f';
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
		font-size: 0.6875rem;
		color: rgba($secondary, 0.2);
	}
}

.WpcIncDecButtonGroup {
	display: inline-flex;
	align-items: center;
	.WpcIncDecInput::-webkit-outer-spin-button,
  .WpcIncDecInput::-webkit-inner-spin-button {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: textfield;
  }
	.WpcIncDecInput,
	.WpcIncButton,
	.WpcDecButton {
		height: 40px;
		width: 40px;
		min-width: 40px;
		border: 1px solid rgba($secondary, 0.2);
		border-radius: 10px;
		padding: 2px;
		display: inline-flex;
		font-size: 1rem;
		align-items: center;
		justify-content: center;
		text-align: center;
		color: $dark;
		&:not(:last-child) {
			margin-right: 10px;
		}
	}
	.WpcIncButton,
	.WpcDecButton {
		font-size: 0.75rem;
		color: $secondary;
	}
	&:not(:last-child) {
		margin-right: 10px;
	}
}

.select2 {
  .select2-selection {
    min-height: 50px!important;
    padding: 0 50px 0 20px!important;
    font-size: 1rem;
    color: $dark!important;
    background: $white!important;
    border-color: rgba($secondary, 0.2)!important;
    border-radius: 12px!important;
    border-width: 1px!important;
    display: flex!important;
    align-items: center!important;
    .select2-selection__rendered{
      margin-left: -5px!important;
      margin-right: -5px!important;
      .select2-selection__choice{
        margin: 2px!important;
      }
    }
    .select2-search{
      display: inline-flex!important;
      .select2-search__field{
        margin-top: 0!important;
      }
    }
    &:focus-within {
      box-shadow: 0 0 5px rgba($dark, 0.15)!important;
    }
    &:after {
      font-family: 'Wpc-icon';
      content: '\e90f';
      position: absolute;
      top: 50%;
      right: 20px;
      transform: translateY(-50%);
      font-size: 0.6875rem;
      color: rgba($secondary, 0.4);
    }
    .select2-selection__arrow {
      display: none;
    }
  }
}
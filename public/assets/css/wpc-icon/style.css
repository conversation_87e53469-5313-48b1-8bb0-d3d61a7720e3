@font-face {
  font-family: 'wpc-icon';
  src:  url('fonts/wpc-icon.eot?lbz97q');
  src:  url('fonts/wpc-icon.eot?lbz97q#iefix') format('embedded-opentype'),
    url('fonts/wpc-icon.ttf?lbz97q') format('truetype'),
    url('fonts/wpc-icon.woff?lbz97q') format('woff'),
    url('fonts/wpc-icon.svg?lbz97q#wpc-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.wpc-icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'wpc-icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wpc-close:before {
  content: "\e917";
}
.wpc-plus:before {
  content: "\e900";
}
.wpc-minus:before {
  content: "\e901";
}
.wpc-user:before {
  content: "\e902";
}
.wpc-arrow-right:before {
  content: "\e903";
}
.wpc-arrow-left:before {
  content: "\e904";
}
.wpc-camera:before {
  content: "\e905";
}
.wpc-tick:before {
  content: "\e906";
}
.wpc-delete:before {
  content: "\e907";
}
.wpc-edit:before {
  content: "\e908";
}
.wpc-dots-vertical:before {
  content: "\e909";
}
.wpc-logout:before {
  content: "\e90a";
}
.wpc-list:before {
  content: "\e90b";
}
.wpc-coffee:before {
  content: "\e90c";
}
.wpc-coffee-seeds:before {
  content: "\e90d";
}
.wpc-coffee-cup:before {
  content: "\e90e";
}
.wpc-arrow-down:before {
  content: "\e90f";
}
.wpc-settings:before {
  content: "\e910";
}
.wpc-speedometer:before {
  content: "\e911";
}
.wpc-notification:before {
  content: "\e912";
}
.wpc-order:before {
  content: "\e913";
}
.wpc-team:before {
  content: "\e916";
}
.wpc-search:before {
  content: "\e915";
}
.wpc-eye:before {
  content: "\e914";
}

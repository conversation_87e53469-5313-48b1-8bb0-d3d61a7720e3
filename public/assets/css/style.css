@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");
* {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  box-sizing: border-box;
}

html {
  font-size: 16px;
}
@media (max-width: 991.98px) {
  html {
    font-size: 14px;
  }
}

body {
  font-family: "DM Sans", sans-serif;
  line-height: 1.3;
  font-weight: 400;
  letter-spacing: 0;
  min-height: 100vh;
  background: #fff;
  -webkit-font-smoothing: antialiased !important;
  -moz-font-smoothing: antialiased !important;
  text-rendering: optimizeSpeed;
  scroll-behavior: smooth;
  -webkit-text-stroke: 0px !important;
}
body::-webkit-scrollbar {
  width: 8px;
}
body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(209, 120, 66, 0.1);
  background-image: linear-gradient(to right bottom, rgba(209, 120, 66, 0.05), rgba(209, 120, 66, 0.05));
}
body::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #b45f2c;
}

ul,
li {
  list-style: none;
}

a {
  color: #19224d;
  transition: all 0.3s linear;
}
a:hover, a:focus {
  text-decoration: none;
  outline: none;
  color: #000;
}

label {
  margin-bottom: 0;
}

button {
  vertical-align: middle;
  background: unset;
}
button:hover, button:focus {
  outline: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin-bottom: 0;
}

ul,
ol {
  margin-bottom: 0;
}

.row {
  margin-left: -25px;
  margin-right: -25px;
}
.row *[class*=col-] {
  padding-left: 25px;
  padding-right: 25px;
}
@media (max-width: 1400.98px) {
  .row .col-md-3 {
    flex: 0 0 33.333333% !important;
    max-width: 33.333333% !important;
  }
  .row .col-md-4 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
  .row .col-md-9 {
    flex: 0 0 66.666666% !important;
    max-width: 66.666666% !important;
  }
}
@media (max-width: 991.98px) {
  .row .col-md-3 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
  .row .col-md-4 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  .row .col-md-9 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
}
@media (max-width: 767.98px) {
  .row .col-md-3 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  .row .col-md-4 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  .row .col-md-9 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
}
@media (max-width: 575.98px) {
  .row {
    margin-left: -15px;
    margin-right: -15px;
  }
  .row *[class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.WpcColorPrimary {
  color: #d17842 !important;
}

a.WpcColorPrimary:hover, a.WpcColorPrimary:focus {
  color: #b45f2c !important;
  box-shadow: none !important;
}

.WpcColorSecondary {
  color: #83858e !important;
}

a.WpcColorSecondary:hover, a.WpcColorSecondary:focus {
  color: #6a6c74 !important;
  box-shadow: none !important;
}

.WpcColorSuccess {
  color: #16ad9f !important;
}

a.WpcColorSuccess:hover, a.WpcColorSuccess:focus {
  color: #108075 !important;
  box-shadow: none !important;
}

.WpcColorDanger {
  color: #ec008c !important;
}

a.WpcColorDanger:hover, a.WpcColorDanger:focus {
  color: #b9006e !important;
  box-shadow: none !important;
}

.WpcColorInfo {
  color: #007cdb !important;
}

a.WpcColorInfo:hover, a.WpcColorInfo:focus {
  color: #005fa8 !important;
  box-shadow: none !important;
}

.WpcColorWarning {
  color: #e04f5f !important;
}

a.WpcColorWarning:hover, a.WpcColorWarning:focus {
  color: #d62639 !important;
  box-shadow: none !important;
}

.WpcColorDark {
  color: #19224d !important;
}

a.WpcColorDark:hover, a.WpcColorDark:focus {
  color: #0d1127 !important;
  box-shadow: none !important;
}

.WpcColorBlack {
  color: #000 !important;
}

.WpcColorLight {
  color: #e6e7e8 !important;
}

a.WpcColorLight:hover, a.WpcColorLight:focus {
  color: #cbced0 !important;
  box-shadow: none !important;
}

.WpcColorWhite {
  color: #fff !important;
}

a.WpcColorWhite:hover, a.WpcColorWhite:focus {
  color: #e6e6e6 !important;
  box-shadow: none !important;
}

.WpcBackgroundPrimary {
  background-color: #d17842 !important;
}

.WpcBackgroundSecondary {
  background-color: #83858e !important;
}

.WpcBackgroundSuccess {
  background-color: #16ad9f !important;
}

.WpcBackgroundDanger {
  background-color: #ec008c !important;
}

.WpcBackgroundInfo {
  background-color: #007cdb !important;
}

.WpcBackgroundWarning {
  background-color: #e04f5f !important;
}

.WpcBackgroundDark {
  background-color: #19224d !important;
}

.WpcBackgroundBlack {
  background-color: #000 !important;
}

.WpcBackgroundLight {
  background-color: #e6e7e8 !important;
}

.WpcBackgroundWhite {
  background-color: #fff !important;
}

.GradientPrimary {
  background-image: linear-gradient(to bottom right, #d17842, #e0a480);
  color: white;
}

.GradientSecondary {
  background-image: linear-gradient(to bottom right, #83858e, #abacb2);
  color: white;
}

.GradientSuccess {
  background-image: linear-gradient(to bottom right, #16ad9f, #2be4d3);
  color: white;
}

.GradientDanger {
  background-image: linear-gradient(to bottom right, #ec008c, #ff3aaf);
  color: white;
}

.GradientInfo {
  background-image: linear-gradient(to bottom right, #007cdb, #29a2ff);
  color: white;
}

.GradientWarning {
  background-image: linear-gradient(to bottom right, #e04f5f, #eb909a);
  color: white;
}

.GradientDark {
  background-image: linear-gradient(to bottom right, #19224d, #2c3c87);
  color: white;
}

.GradientBlack {
  background-image: linear-gradient(to bottom right, #000, #262626);
  color: white;
}

.GradientLight {
  background-image: linear-gradient(to bottom right, #e6e7e8, white);
  color: #19224d;
}

.GradientWhite {
  background-image: linear-gradient(to bottom right, #fff, white);
  color: #19224d;
}

.WpcButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  min-width: 90px;
  padding: 1px;
  z-index: 1;
  border-radius: 18px;
  background: rgba(209, 120, 66, 0.1);
  border: 1px solid #d17842;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: background, box-shadow;
  -moz-transition-property: background, box-shadow;
  -o-transition-property: background, box-shadow;
  transition-property: background, box-shadow;
  position: relative;
}
.WpcButton .Icon {
  height: 24px;
  width: 24px;
  min-width: 24px;
  background: #19224d;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  font-size: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, filter;
  -moz-transition-property: background, color, filter;
  -o-transition-property: background, color, filter;
  transition-property: background, color, filter;
}
.WpcButton .Text {
  padding: 0 25px;
  font-size: 1rem;
  font-weight: 500;
}
.WpcButton .Text:not(:first-child) {
  padding-left: 10px;
}
.WpcButton .Text:not(:last-child) {
  padding-right: 10px;
}
.WpcButton:hover {
  background: #d17842;
  box-shadow: 0 20px 20px -10px rgba(209, 120, 66, 0.4);
}
.WpcButton.WpcFilled {
  background: #d17842;
}
.WpcButton.WpcFilled:hover {
  background: #b45f2c;
  box-shadow: 0 20px 20px -10px rgba(209, 120, 66, 0.4);
}
.WpcButton.WpcDisabled {
  background: transparent;
  border-color: rgba(131, 133, 142, 0.2);
  cursor: not-allowed;
}
.WpcButton.WpcDisabled .Text {
  color: #83858e;
}
.WpcButton.WpcDisabled:hover {
  background: transparent;
  box-shadow: none;
}

.WpcEditButton {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  align-self: center;
}
.WpcEditButton .Icon {
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 10px;
  border: 1px solid rgba(25, 34, 77, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: #83858e;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: border-color, background-color, color;
  -moz-transition-property: border-color, background-color, color;
  -o-transition-property: border-color, background-color, color;
  transition-property: border-color, background-color, color;
}
.WpcEditButton .Icon:not(:last-child) {
  margin-right: 10px;
}
.WpcEditButton .Text {
  font-size: 0.875rem;
  line-height: 1.125rem;
  color: #19224d;
}
.WpcEditButton:hover .Icon {
  border-color: #d17842;
  background-color: #d17842;
  color: #fff;
}
.WpcEditButton.WpcBigSize .Icon {
  height: 50px;
  width: 50px;
  min-width: 50px;
  border-radius: 12px;
  font-size: 1.25rem;
}
.WpcEditButton.WpcBigSize .Icon:not(:last-child) {
  margin-right: 12px;
}
.WpcEditButton.WpcBigSize .Text {
  font-size: 1rem;
}
.WpcEditButton.WpcFilled .Icon {
  border-color: #d17842;
  background: #d17842;
  color: #19224d;
}
.WpcEditButton.WpcFilled:hover .Icon {
  background: #b45f2c;
  box-shadow: 0 20px 20px -10px rgba(209, 120, 66, 0.4);
}
.WpcEditButton.WpcDisabled .Icon {
  background: transparent;
  border-color: rgba(131, 133, 142, 0.2);
  color: rgba(131, 133, 142, 0.2);
  cursor: not-allowed;
}
.WpcEditButton.WpcDisabled .Text {
  color: #83858e;
}
.WpcEditButton.WpcDisabled:hover .Icon {
  background: transparent;
  box-shadow: none;
}

.WpcAddButton {
  margin-bottom: 10px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
.WpcAddButton .Icon {
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 20px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  color: #83858e;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.5rem;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: border-color, color;
  -moz-transition-property: border-color, color;
  -o-transition-property: border-color, color;
  transition-property: border-color, color;
}
.WpcAddButton .Icon:not(:last-child) {
  margin-right: 10px;
}
.WpcAddButton .Text {
  font-size: 0.875rem;
  line-height: 1.25;
  color: #83858e;
  -webkit-transition: color 0.3s ease-in-out 0s;
  -moz-transition: color 0.3s ease-in-out 0s;
  -o-transition: color 0.3s ease-in-out 0s;
  transition: color 0.3s ease-in-out 0s;
}
.WpcAddButton:hover .Icon {
  border-color: #83858e;
  color: #19224d;
}
.WpcAddButton:hover .Text {
  color: #19224d;
}

.WpcBackButton {
  display: flex;
  align-items: center;
  color: #19224d;
}
.WpcBackButton .Icon {
  font-size: 0.875rem;
  margin-right: 10px;
  display: inline-flex;
  align-items: center;
  color: #83858e;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcBackButton .Text {
  font-size: 1.125rem;
  font-weight: 400;
  display: flex;
  color: #83858e;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcBackButton:hover .Icon {
  color: #19224d;
}
.WpcBackButton:hover .Text {
  color: #19224d;
}

.WpcTabButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  min-width: 90px;
  padding: 1px;
  z-index: 1;
  border-radius: 18px;
  background: transparent;
  border: 1px solid #e6e7e8;
  margin-bottom: 10px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: background, box-shadow;
  -moz-transition-property: background, box-shadow;
  -o-transition-property: background, box-shadow;
  transition-property: background, box-shadow;
  position: relative;
}
.WpcTabButton .Icon {
  height: 24px;
  width: 24px;
  min-width: 24px;
  background: #19224d;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  font-size: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, filter;
  -moz-transition-property: background, color, filter;
  -o-transition-property: background, color, filter;
  transition-property: background, color, filter;
}
.WpcTabButton .Text {
  padding: 0 25px;
  font-size: 1rem;
  font-weight: 500;
  color: #83858e;
}
.WpcTabButton .Text:not(:first-child) {
  padding-left: 10px;
}
.WpcTabButton .Text:not(:last-child) {
  padding-right: 10px;
}
.WpcTabButton.active, .WpcTabButton:hover {
  border-color: #d17842;
}
.WpcTabButton.active .Icon, .WpcTabButton:hover .Icon {
  background: #d17842;
}
.WpcTabButton.active .Text, .WpcTabButton:hover .Text {
  color: #19224d;
}

.WpcProfileDropdown {
  padding: 20px;
  border: none;
  min-width: 200px !important;
  max-width: 250px !important;
  box-shadow: 0 10px 50px rgba(25, 34, 77, 0.07);
  margin-top: 5px;
  border-radius: 12px;
}
.WpcProfileDropdown ul li a,
.WpcProfileDropdown ul li button {
  display: flex;
  line-height: 1;
  padding: 0;
  align-items: center;
  cursor: pointer;
}
.WpcProfileDropdown ul li a .WpcNavigationIcon,
.WpcProfileDropdown ul li button .WpcNavigationIcon {
  width: 40px;
  min-width: 40px;
  height: 40px;
  border: 1px solid rgba(25, 34, 77, 0.1);
  border-radius: 12px;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #d17842;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, border;
  -moz-transition-property: background, color, border;
  -o-transition-property: background, color, border;
  transition-property: background, color, border;
}
.WpcProfileDropdown ul li a .WpcNavigationText,
.WpcProfileDropdown ul li button .WpcNavigationText {
  margin-left: 15px;
  color: #83858e;
  font-size: 1.125rem;
  line-height: 1;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, border;
  -moz-transition-property: background, color, border;
  -o-transition-property: background, color, border;
  transition-property: background, color, border;
}
.WpcProfileDropdown ul li a.Active .WpcNavigationIcon, .WpcProfileDropdown ul li a:hover .WpcNavigationIcon,
.WpcProfileDropdown ul li button.Active .WpcNavigationIcon,
.WpcProfileDropdown ul li button:hover .WpcNavigationIcon {
  background: #d17842;
  border-color: #d17842;
  color: #fff;
}
.WpcProfileDropdown ul li a.Active .WpcNavigationText, .WpcProfileDropdown ul li a:hover .WpcNavigationText,
.WpcProfileDropdown ul li button.Active .WpcNavigationText,
.WpcProfileDropdown ul li button:hover .WpcNavigationText {
  color: #d17842;
}
.WpcProfileDropdown ul li:not(:last-child) {
  margin-bottom: 15px;
}

.WpcNotificationDropdown {
  padding: 20px;
  border: none;
  min-width: 300px !important;
  box-shadow: 0 10px 50px rgba(25, 34, 77, 0.07);
  margin-top: 5px;
  border-radius: 12px;
}
.WpcNotificationDropdown ul li .WpcNotification {
  display: flex;
  flex-direction: column;
  padding: 0;
}
.WpcNotificationDropdown ul li .WpcNotification .WpcNotificationTitle {
  font-size: 1rem;
  line-height: 1;
  color: #19224d;
}
.WpcNotificationDropdown ul li .WpcNotification .WpcNotificationTitle:not(:last-child) {
  margin-bottom: 15px;
}
.WpcNotificationDropdown ul li .WpcNotification .WpcNotificationTime {
  font-size: 0.8275rem;
  color: #83858e;
}
.WpcNotificationDropdown ul li:not(:first-child) {
  padding-top: 15px;
}
.WpcNotificationDropdown ul li:not(:last-child) {
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(230, 231, 232, 0.6);
}

.dropdown .dropdown-menu-middle {
  left: 50% !important;
  transform: translateX(-50%) !important;
  top: 100% !important;
}

.WpcToggleDropdown .dropdown-menu {
  padding: 10px;
  border: none;
  min-width: 120px !important;
  max-width: 150px !important;
  box-shadow: 0 10px 50px rgba(25, 34, 77, 0.07);
  margin-top: 5px;
}
.WpcToggleDropdown .dropdown-menu ul li a,
.WpcToggleDropdown .dropdown-menu ul li button {
  display: flex;
  line-height: 1;
  padding: 0;
  align-items: center;
  cursor: pointer;
}
.WpcToggleDropdown .dropdown-menu ul li a .WpcNavigationIcon,
.WpcToggleDropdown .dropdown-menu ul li button .WpcNavigationIcon {
  width: 24px;
  min-width: 24px;
  height: 24px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  border-radius: 5px;
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #83858e;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, border;
  -moz-transition-property: background, color, border;
  -o-transition-property: background, color, border;
  transition-property: background, color, border;
}
.WpcToggleDropdown .dropdown-menu ul li a .WpcNavigationText,
.WpcToggleDropdown .dropdown-menu ul li button .WpcNavigationText {
  margin-left: 15px;
  color: #83858e;
  font-size: 0.9375rem;
  line-height: 1.3;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transition-property: background, color, border;
  -moz-transition-property: background, color, border;
  -o-transition-property: background, color, border;
  transition-property: background, color, border;
}
.WpcToggleDropdown .dropdown-menu ul li a.Active .WpcNavigationIcon, .WpcToggleDropdown .dropdown-menu ul li a:hover .WpcNavigationIcon,
.WpcToggleDropdown .dropdown-menu ul li button.Active .WpcNavigationIcon,
.WpcToggleDropdown .dropdown-menu ul li button:hover .WpcNavigationIcon {
  background: #d17842;
  border-color: #d17842;
  color: #fff;
}
.WpcToggleDropdown .dropdown-menu ul li a.Active .WpcNavigationText, .WpcToggleDropdown .dropdown-menu ul li a:hover .WpcNavigationText,
.WpcToggleDropdown .dropdown-menu ul li button.Active .WpcNavigationText,
.WpcToggleDropdown .dropdown-menu ul li button:hover .WpcNavigationText {
  color: #d17842;
}
.WpcToggleDropdown .dropdown-menu ul li:not(:last-child) {
  margin-bottom: 10px;
}

.WpcSectionGap {
  margin-top: 60px;
}

.WpcPaginationWrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 30px;
}

.WpcHasColGap {
  margin-bottom: -50px;
}
.WpcHasColGap *[class*=col-],
.WpcHasColGap > .col {
  margin-bottom: 50px !important;
}
@media (max-width: 767.98px) {
  .WpcHasColGap {
    margin-bottom: -30px;
  }
  .WpcHasColGap *[class*=col-],
  .WpcHasColGap > .col {
    margin-bottom: 30px !important;
  }
}
@media (max-width: 575.98px) {
  .WpcHasColGap {
    margin-bottom: -15px;
  }
  .WpcHasColGap *[class*=col-],
  .WpcHasColGap > .col {
    margin-bottom: 15px !important;
  }
}

.WpcHasColGapInForm {
  margin-bottom: -20px;
}
.WpcHasColGapInForm *[class*=col-],
.WpcHasColGapInForm > .col {
  margin-bottom: 20px !important;
}

.WpcSectionTitleWrap {
  display: flex;
  align-items: center;
  margin-bottom: 50px;
  flex-wrap: wrap;
}
@media (max-width: 767.98px) {
  .WpcSectionTitleWrap {
    flex-direction: column;
    align-items: baseline;
    margin-bottom: 30px;
  }
}
@media (max-width: 575.98px) {
  .WpcSectionTitleWrap {
    margin-bottom: 20px;
  }
}
.WpcSectionTitleWrap--flex {
  justify-content: space-between;
}
.WpcSectionTitleWrap--flex .WpcSectionTitle {
  margin-right: auto !important;
}
.WpcSectionTitleWrap--flex .timeline__filter {
  width: 130px;
  text-align: right;
  margin-right: 20px;
}
.WpcSectionTitleWrap--flex .timeline__filter #reportrange {
  background: #fff;
  cursor: pointer;
  padding: 12px 10px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  width: 100%;
  height: auto;
  border-radius: 12px;
}
.WpcSectionTitleWrap--flex .timeline__filter #reportrange span {
  font-size: 14px;
  color: #19224d;
}

.WpcSectionTitle {
  font-weight: 500;
  line-height: 1;
  font-size: 2.375rem;
  color: #19224d;
}
@media (max-width: 991.98px) {
  .WpcSectionTitle {
    font-size: 1.75rem;
  }
}
@media (max-width: 767.98px) {
  .WpcSectionTitle {
    margin-bottom: 10px;
  }
}

.WpcSectionSemiTitle {
  font-weight: 500;
  line-height: 1.3;
  font-size: 1.375rem;
  color: #d17842;
}

.WpcEmptyMessage {
  padding: 30px 26.5px;
  font-size: 1.125rem;
  line-height: 1.5;
  background: #fff;
  border-radius: 10px;
  color: #ff5f74;
  font-weight: 500;
}
.WpcEmptyMessage:not(first-child) {
  margin-top: 30px;
}
.WpcEmptyMessage:not(last-child) {
  margin-bottom: 30px;
}

.WpcButtonGroup {
  display: flex;
  margin-left: -10px;
  margin-right: -10px;
  margin-bottom: -10px;
}
@media (max-width: 991.98px) {
  .WpcButtonGroup {
    flex-wrap: wrap;
  }
}
.WpcButtonGroup > * {
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.WpcCounterCardWrapper {
  width: 400px;
  flex-basis: 400px;
  display: flex;
  flex-direction: column;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcCounterCardWrapper {
    width: 350px;
    flex-basis: 350px;
  }
}
@media (max-width: 1400.98px) {
  .WpcCounterCardWrapper {
    flex: 0 0 100%;
    display: grid;
    grid-gap: 30px;
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .WpcCounterCardWrapper {
    grid-gap: 15px;
  }
}
@media (max-width: 767.98px) {
  .WpcCounterCardWrapper {
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 10px;
  }
}
.WpcCounterCardWrapper .WpcCounterCard {
  display: flex;
  align-items: center;
  border: 2px solid rgba(209, 120, 66, 0.3);
  border-radius: 38px;
  padding: 30px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
@media (max-width: 1600.98px) {
  .WpcCounterCardWrapper .WpcCounterCard {
    padding: 20px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .WpcCounterCardWrapper .WpcCounterCard {
    padding: 15px;
    border-radius: 32px;
  }
}
.WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 30px;
}
@media (max-width: 1600.98px) {
  .WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent {
    margin-left: 20px;
  }
}
.WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent .WpcCounterCount {
  font-size: 3.125rem;
  line-height: 1;
  color: #d17842;
  letter-spacing: -0.03em;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-end;
}
@media (max-width: 1600.98px) {
  .WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent .WpcCounterCount {
    font-size: 2.75rem;
  }
}
.WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent .WpcCounterCount span {
  font-size: 1rem;
  line-height: 1;
  letter-spacing: -0.03em;
  color: #83858e;
  margin-left: 5px;
  margin-bottom: 5px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcCounterCardWrapper .WpcCounterCard .WpcCounterContent .WpcCounterTitle {
  font-size: 1.5rem;
  line-height: 1.2;
  color: #19224d;
  letter-spacing: -0.03em;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcCounterCardWrapper .WpcCounterCard.Active, .WpcCounterCardWrapper .WpcCounterCard:hover {
  background: #1c0a07;
}
.WpcCounterCardWrapper .WpcCounterCard.Active .WpcCounterContent .WpcCounterCount span, .WpcCounterCardWrapper .WpcCounterCard:hover .WpcCounterContent .WpcCounterCount span {
  color: #fff;
}
.WpcCounterCardWrapper .WpcCounterCard.Active .WpcCounterContent .WpcCounterTitle, .WpcCounterCardWrapper .WpcCounterCard:hover .WpcCounterContent .WpcCounterTitle {
  color: #fff;
}
.WpcCounterCardWrapper .WpcCounterCard:not(:last-child) {
  margin-bottom: 30px;
}
@media (max-width: 1600.98px) {
  .WpcCounterCardWrapper .WpcCounterCard:not(:last-child) {
    margin-bottom: 20px;
  }
}
@media (max-width: 1400.98px) {
  .WpcCounterCardWrapper .WpcCounterCard:not(:last-child) {
    margin-bottom: 0;
  }
}

.WpcDataTableWrapper {
  display: block;
  width: 100%;
  margin-top: -15px;
  margin-bottom: -15px;
  overflow-x: auto;
}
.WpcDataTableWrapper .WpcDataTable {
  width: 100%;
  display: table;
  border-collapse: separate !important;
  border-spacing: 0 15px;
}
.WpcDataTableWrapper .WpcDataTable thead tr th {
  padding: 0px 10px;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1;
  color: #83858e;
}
.WpcDataTableWrapper .WpcDataTable thead tr th:first-child {
  padding-left: 25px;
}
.WpcDataTableWrapper .WpcDataTable thead tr th:first-child .WpcCheckbox .Text {
  padding-left: 20px;
}
.WpcDataTableWrapper .WpcDataTable thead tr th:last-child {
  padding-right: 25px;
}
.WpcDataTableWrapper .WpcDataTable thead tr th.WpcTableCheckbox {
  width: 60px;
}
.WpcDataTableWrapper .WpcDataTable thead tr th.WpcTableActionHeader {
  width: 45px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td {
  padding: 20px 10px;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1;
  color: #83858e;
  border-top: 1px solid rgba(209, 120, 66, 0.3);
  border-bottom: 1px solid rgba(209, 120, 66, 0.3);
  -webkit-transition: border-color 0.3s ease-in-out 0s;
  -moz-transition: border-color 0.3s ease-in-out 0s;
  -o-transition: border-color 0.3s ease-in-out 0s;
  transition: border-color 0.3s ease-in-out 0s;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td {
    font-size: 1rem;
  }
}
.WpcDataTableWrapper .WpcDataTable tbody tr td:first-child {
  padding-left: 25px;
  border-left: 1px solid rgba(209, 120, 66, 0.3);
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td:first-child .WpcCheckbox .Text {
  padding-left: 20px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td:last-child {
  padding-right: 25px;
  border-right: 1px solid rgba(209, 120, 66, 0.3);
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox {
  display: flex;
  align-items: center;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxImage {
  background-color: rgba(209, 120, 66, 0.1);
  height: 100px;
  width: 100px;
  min-width: 100px;
  border-radius: 22px;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: center center;
  background-size: cover;
}
@media (max-width: 1600.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxImage {
    height: 80px;
    width: 80px;
    min-width: 80px;
    border-radius: 15px;
  }
}
@media (max-width: 1199.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxImage {
    height: 70px;
    width: 70px;
    min-width: 70px;
    border-radius: 15px;
  }
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxImage:not(:last-child) {
  margin-right: 20px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxTitle {
  font-size: 1.875rem;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.03em;
  color: #19224d;
}
@media (min-width: 992px) and (max-width: 1600.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxTitle {
    font-size: 1.375rem;
  }
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxTitle:not(:last-child) {
  margin-bottom: 15px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxInfo {
  font-size: 1.25rem;
  line-height: 1;
  font-weight: 400;
  color: #83858e;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxInfo {
    font-size: 1rem;
  }
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableInfoBoxDetails .WpcTableInfoBoxSemiInfo {
  font-size: 0.875rem;
  line-height: 1;
  font-weight: 400;
  color: #83858e;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableImageCollection {
  display: flex;
  align-content: center;
  flex-direction: row;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableImageCollection .WpdTableAddImageButton,
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableImageCollection .WpcTableInfoBoxImage {
  display: inline-flex;
  margin-right: -5px;
  height: 40px;
  width: 40px;
  min-width: 40px;
  border: 2px solid #fff;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableInfoBox .WpcTableImageCollection .WpdTableAddImageButton {
  background: #16ad9f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableActionWrapper {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 20px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableActionWrapper .WpcToggleDropdown {
  position: absolute;
  top: 0;
  right: 0;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableActionWrapper .WpcToggleDropdown > button {
  display: inline-flex;
  justify-content: center;
  color: #83858e;
  font-size: 1rem;
  opacity: 0.5;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -moz-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcTableActionWrapper .WpcToggleDropdown > button:hover {
  opacity: 1;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeePreview {
  width: 80px;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeePreview {
    width: 70px;
  }
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeePreview .WpcCoffeePreviewImage {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeePreview .WpcCoffeePreviewDetails {
  padding: 5px 2px;
  font-size: 0.875rem;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeeInfoCard {
  display: flex;
  align-items: center;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeeInfoCard .WpcCoffeeInfoIcon {
  height: 50px;
  width: 50px;
  min-width: 50px;
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  background: #d17842;
  color: #fff;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeeInfoCard .WpcCoffeeInfoIcon:not(:last-child) {
  margin-right: 15px;
}
.WpcDataTableWrapper .WpcDataTable tbody tr td .WpcCoffeeInfoCard .WpcCoffeeInfoText {
  display: inline-flex;
  align-items: center;
  font-size: 1.875rem;
  font-weight: 400;
  line-height: 1;
  letter-spacing: -0.03em;
  color: #19224d;
}
.WpcDataTableWrapper .WpcDataTable tbody tr:focus-within td, .WpcDataTableWrapper .WpcDataTable tbody tr:hover td {
  border-color: #d17842;
}
.WpcDataTableWrapper .WpcDataTable tbody tr:focus-within td:first-child, .WpcDataTableWrapper .WpcDataTable tbody tr:hover td:first-child {
  border-color: #d17842;
}
.WpcDataTableWrapper .WpcDataTable tbody tr:focus-within td:last-child, .WpcDataTableWrapper .WpcDataTable tbody tr:hover td:last-child {
  border-color: #d17842;
}
.WpcDataTableWrapper .WpcEmptyDataMessage {
  background: #fff;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px 30px;
  min-height: 50px;
  display: flex;
  align-items: center;
  color: #19224d;
}

.WpcPostGridWrapper .WpcPostGrid {
  background: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 38px;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridThumbnail {
  border-top-left-radius: 38px;
  border-top-right-radius: 38px;
  overflow: hidden;
  padding-top: 87.5%;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: scroll;
  background-size: cover;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails {
  padding: 30px;
  padding-bottom: 40px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-bottom-left-radius: 38px;
  border-bottom-right-radius: 38px;
  border: 1px solid rgba(209, 120, 66, 0.2);
  border-top-color: transparent;
  position: relative;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcPostGridTitle {
  font-size: 1.875rem;
  color: #19224d;
  font-weight: 500;
  line-height: 1;
  text-align: center;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcPostGridTitle {
    font-size: 1.5rem;
  }
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcPostGridContent {
  font-size: 1.25rem;
  line-height: 1;
  color: #19224d;
  line-height: 1.6;
  margin-top: 15px;
  text-align: center;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcPostGridContent span {
  color: #83858e;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcButton {
  margin-top: 30px;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcToggleDropdown {
  position: absolute;
  top: 20px;
  right: 15px;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcToggleDropdown > button {
  display: inline-flex;
  justify-content: center;
  color: #83858e;
  font-size: 1rem;
  opacity: 0.5;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -moz-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}
.WpcPostGridWrapper .WpcPostGrid .WpcPostGridDetails .WpcToggleDropdown > button:hover {
  opacity: 1;
}

.WpcInfoBoxWrapper .WpcInfoBox {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid rgba(209, 120, 66, 0.2);
  border-radius: 32px;
  min-height: 140px;
  position: relative;
  -webkit-transition: border 0.3s ease-in-out 0s;
  -moz-transition: border 0.3s ease-in-out 0s;
  -o-transition: border 0.3s ease-in-out 0s;
  transition: border 0.3s ease-in-out 0s;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcRibbonStyle {
  position: absolute;
  left: -10px;
  top: -10px;
  z-index: 1;
  overflow: hidden;
  width: 105px;
  height: 105px;
  text-align: right;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcRibbonStyle span {
  height: 25px;
  font-size: 12px;
  font-weight: bold;
  color: rgb(255, 255, 255);
  text-transform: uppercase;
  text-align: center;
  line-height: 1;
  transform: rotate(-45deg);
  transform-origin: center;
  width: 130px;
  display: block;
  position: absolute;
  top: 26px;
  left: -26px;
  background: #d17842;
  display: flex;
  align-items: center;
  justify-content: center;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcRibbonStyle span:before {
  content: "";
  position: absolute;
  left: 0px;
  top: calc(100% - 1px);
  z-index: -1;
  border-width: 7px;
  border-style: solid;
  border-color: #c96a31 transparent transparent #c96a31;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcRibbonStyle span:after {
  content: "";
  position: absolute;
  right: 0px;
  top: calc(100% - 1px);
  z-index: -1;
  border-width: 7px;
  border-style: solid;
  border-color: #c96a31 #c96a31 transparent transparent;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxImage {
  background-color: rgba(209, 120, 66, 0.1);
  height: 100px;
  width: 100px;
  min-width: 100px;
  overflow: hidden;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: scroll;
  background-size: cover;
  border-radius: 22px;
  margin-right: 30px;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxImage {
    height: 80px;
    width: 80px;
    min-width: 80px;
  }
}
@media (max-width: 575.98px) {
  .WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxImage {
    height: 80px;
    width: 80px;
    min-width: 80px;
    margin-right: 15px;
  }
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxDetails {
  display: flex;
  flex-direction: column;
  padding-right: 10px;
  flex-grow: 1;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxDetails .WpcInfoBoxTitle {
  font-size: 1.875rem;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.03em;
  color: #19224d;
  margin-bottom: 15px;
  margin-right: 20px;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxDetails .WpcInfoBoxTitle {
    font-size: 1.5rem;
  }
}
@media (max-width: 575.98px) {
  .WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxDetails .WpcInfoBoxTitle {
    font-size: 1.5rem;
  }
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcInfoBoxDetails .WpcPostGridContent {
  font-size: 0.875rem;
  line-height: 1.3;
  color: #83858e;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcAvailableCount {
  min-width: 40px;
  height: 40px;
  background: #fff;
  color: #19224d;
  font-size: 1rem;
  border: 1px solid rgba(25, 34, 77, 0.3);
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
  margin-top: 5px;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcToggleDropdown {
  position: absolute;
  top: 25px;
  right: 15px;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcToggleDropdown > button {
  display: inline-flex;
  color: #83858e;
  font-size: 1rem;
  opacity: 0.5;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -moz-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}
.WpcInfoBoxWrapper .WpcInfoBox .WpcToggleDropdown > button:hover {
  opacity: 1;
}
.WpcInfoBoxWrapper .WpcInfoBox.WpcAddItem {
  height: 100%;
  background: rgba(209, 120, 66, 0.1);
  border: 2px dashed rgba(209, 120, 66, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcInfoBoxWrapper .WpcInfoBox.WpcAddItem .WpcInfoBoxTitle {
  font-size: 1.5rem;
  color: #d17842;
  font-weight: 400;
  letter-spacing: -0.03em;
}
.WpcInfoBoxWrapper .WpcInfoBox.WpcAddItem:hover {
  background: rgba(209, 120, 66, 0.15);
  border: 2px dashed rgba(209, 120, 66, 0.75);
}
.WpcInfoBoxWrapper .WpcInfoBox:hover {
  border-color: #d17842;
}

.WpcCoffeePreviewWrapper {
  padding-top: 20px;
  display: flex;
  flex-wrap: wrap;
  margin-left: -5px;
  margin-right: -5px;
  margin-bottom: -10px;
  position: relative;
}
.WpcCoffeePreviewWrapper .WpcCoffeePreview {
  margin-left: 5px;
  margin-right: 5px;
  margin-bottom: 10px;
}
.WpcCoffeePreviewWrapper:before {
  content: "";
  position: absolute;
  left: 5px;
  right: 5px;
  top: 0;
  border-top: 1px solid rgba(131, 133, 142, 0.2);
}

.WpcCoffeePreview {
  display: inline-flex;
  flex-direction: column;
  width: 130px;
  position: relative;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcCoffeePreview {
    width: 100px;
  }
}
@media (max-width: 1199.98px) {
  .WpcCoffeePreview {
    width: 100px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .WpcCoffeePreview {
    width: 90px;
  }
}
.WpcCoffeePreview .deleteItem {
  position: absolute;
  top: 10px;
  right: 10px;
  height: 20px;
  width: 20px;
  background: #d17842;
  color: #fff;
  font-size: 12px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}
.WpcCoffeePreview .WpcCoffeePreviewImage {
  background-color: rgba(209, 120, 66, 0.1);
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  overflow: hidden;
  padding-top: 87.5%;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: scroll;
  background-size: cover;
}
.WpcCoffeePreview .WpcCoffeePreviewDetails {
  padding: 12px 2px;
  font-size: 1rem;
  line-height: 1;
  background: #fff;
  color: #19224d;
  width: 100%;
  text-align: center;
  border: 1px solid rgba(209, 120, 66, 0.2);
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
}
@media (min-width: 1401px) and (max-width: 1600.98px) {
  .WpcCoffeePreview .WpcCoffeePreviewDetails {
    font-size: 0.875rem;
    padding: 8px 2px;
  }
}

.WpcFont14 {
  font-size: 0.875rem;
}

.WpcFont500 {
  font-weight: 500;
}

.WpcFont700 {
  font-weight: 700;
}

.WpcCursorPointer {
  cursor: pointer;
}

.WpcSearchForm button {
  position: absolute;
  font-size: 1.125rem;
  height: 50px;
  right: 0;
  top: 0;
  color: #d9dadc;
  padding: 0 10px;
  line-height: 1;
}

.WpcFormGroup {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.WpcFormGroup .WpcFormLabel {
  font-size: 1rem;
  color: #83858e;
  margin-bottom: 10px;
  margin-right: 10px;
  line-height: 1;
}
.WpcFormGroup:not(:last-child) {
  margin-bottom: 20px;
}

input.form-control {
  height: 50px;
  padding: 0 20px !important;
  font-size: 1rem;
  color: #19224d;
  background: #fff;
  border-color: rgba(131, 133, 142, 0.2);
  border-radius: 12px;
  border-width: 1px;
}
input.form-control:focus {
  box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
}
input.form-control::placeholder {
  color: #b8babf;
}
input.form-control:not(:last-child) {
  margin-bottom: 5px;
}

select.form-control {
  height: 50px;
  padding: 0 20px !important;
  font-size: 1rem;
  color: #19224d;
  background: #fff;
  border-color: rgba(131, 133, 142, 0.2);
  border-radius: 12px;
  border-width: 1px;
  appearance: none;
  -webkit-appearance: none;
}
select.form-control:focus {
  box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
}
select.form-control::placeholder {
  color: #b8babf;
}
select.form-control:not(:last-child) {
  margin-bottom: 5px;
}

textarea.form-control {
  padding: 15px 20px !important;
  min-height: 100px;
  font-size: 1rem;
  color: #19224d;
  background: #fff;
  border-color: rgba(131, 133, 142, 0.2);
  border-radius: 12px;
  border-width: 1px;
}
textarea.form-control:focus {
  box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
}
textarea.form-control::placeholder {
  color: #b8babf;
}
textarea.form-control:not(:last-child) {
  margin-bottom: 5px;
}

.WpcHasError {
  border: 1px solid #ec008c !important;
}
.WpcHasError:focus {
  box-shadow: 0 0 5px rgba(236, 0, 140, 0.15) !important;
}

.WpcSearchForm {
  position: relative;
}
.WpcSearchForm .form-control {
  height: 50px;
  padding: 0 45px 0 20px !important;
  font-size: 1rem;
  color: #19224d;
  background: #fff;
  border-color: rgba(131, 133, 142, 0.2);
  border-radius: 18px;
  border-width: 1px;
  width: 200px;
  /* clears the 'X' from Chrome */
}
.WpcSearchForm .form-control:focus {
  box-shadow: 0 0 5px rgba(25, 34, 77, 0.15);
}
.WpcSearchForm .form-control::placeholder {
  color: #b8babf;
}
.WpcSearchForm .form-control::-ms-clear, .WpcSearchForm .form-control::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}
.WpcSearchForm .form-control::-webkit-search-decoration, .WpcSearchForm .form-control::-webkit-search-cancel-button, .WpcSearchForm .form-control::-webkit-search-results-button, .WpcSearchForm .form-control::-webkit-search-results-decoration {
  display: none;
}
.WpcCheckbox {
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcCheckbox .Text {
  font-size: 0.875rem;
  color: #19224d;
  display: inline-flex;
  align-items: flex-start;
  line-height: 20px;
  position: relative;
  padding-left: 30px;
  flex-wrap: wrap;
  min-height: 20px;
}
.WpcCheckbox .Text a {
  margin-left: 5px;
  margin-right: 5px;
}
.WpcCheckbox input[type=checkbox] {
  display: none;
}
.WpcCheckbox input[type=checkbox] + .Text:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  color: #e6e7e8;
  background: transparent;
  border: 2px solid #e6e7e8;
  border-radius: 4px;
  height: 20px;
  width: 20px;
  min-width: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-family: "Wpc-icon" !important;
  font-size: 0.625rem;
}
.WpcCheckbox input[type=checkbox]:checked + .Text:before {
  content: "\e906";
  line-height: 1;
  color: #16ad9f;
  border-color: #16ad9f;
}

input:disabled {
  cursor: no-drop;
  background-color: #dedfe1 !important;
}

.WpcImageSelector {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.WpcImageSelector .WpcImgBox {
  background: rgba(209, 120, 66, 0.1);
  height: 100px;
  width: 100px;
  min-width: 100px;
  overflow: hidden;
  border-radius: 20px;
  position: relative;
  margin-right: 20px;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: scroll;
  background-size: cover;
}
.WpcImageSelector .WpcImgBox input[type=file] {
  display: none;
}
.WpcImageSelector .WpcImgBox .WpcDummyImage {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px dashed rgba(209, 120, 66, 0.6);
  border-radius: 20px;
  font-size: 1.75rem;
  color: #d17842;
  cursor: pointer;
}

.WpcFilterSelectorWrapper {
  display: inline-flex;
  align-items: center;
  position: relative;
}
.WpcFilterSelectorWrapper label {
  font-size: 1rem;
  line-height: 1;
  color: #83858e;
  margin-right: 10px;
}
.WpcFilterSelectorWrapper .WpcFilterSelector {
  height: 50px;
  padding: 0 45px 0 20px;
  background: transparent !important;
  border: 1px solid rgba(131, 133, 142, 0.2);
  border-radius: 18px;
  font-size: 1rem;
  line-height: 1;
  color: #19224d;
  -webkit-appearance: none;
}
.WpcFilterSelectorWrapper:before {
  font-family: "Wpc-icon";
  content: "\e90f";
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 0.6875rem;
  color: rgba(131, 133, 142, 0.2);
}

.WpcIncDecButtonGroup {
  display: inline-flex;
  align-items: center;
}
.WpcIncDecButtonGroup .WpcIncDecInput::-webkit-outer-spin-button,
.WpcIncDecButtonGroup .WpcIncDecInput::-webkit-inner-spin-button {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: textfield;
}
.WpcIncDecButtonGroup .WpcIncDecInput,
.WpcIncDecButtonGroup .WpcIncButton,
.WpcIncDecButtonGroup .WpcDecButton {
  height: 40px;
  width: 40px;
  min-width: 40px;
  border: 1px solid rgba(131, 133, 142, 0.2);
  border-radius: 10px;
  padding: 2px;
  display: inline-flex;
  font-size: 1rem;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #19224d;
}
.WpcIncDecButtonGroup .WpcIncDecInput:not(:last-child),
.WpcIncDecButtonGroup .WpcIncButton:not(:last-child),
.WpcIncDecButtonGroup .WpcDecButton:not(:last-child) {
  margin-right: 10px;
}
.WpcIncDecButtonGroup .WpcIncButton,
.WpcIncDecButtonGroup .WpcDecButton {
  font-size: 0.75rem;
  color: #83858e;
}
.WpcIncDecButtonGroup:not(:last-child) {
  margin-right: 10px;
}

.select2 .select2-selection {
  min-height: 50px !important;
  padding: 0 50px 0 20px !important;
  font-size: 1rem;
  color: #19224d !important;
  background: #fff !important;
  border-color: rgba(131, 133, 142, 0.2) !important;
  border-radius: 12px !important;
  border-width: 1px !important;
  display: flex !important;
  align-items: center !important;
}
.select2 .select2-selection .select2-selection__rendered {
  margin-left: -5px !important;
  margin-right: -5px !important;
}
.select2 .select2-selection .select2-selection__rendered .select2-selection__choice {
  margin: 2px !important;
}
.select2 .select2-selection .select2-search {
  display: inline-flex !important;
}
.select2 .select2-selection .select2-search .select2-search__field {
  margin-top: 0 !important;
}
.select2 .select2-selection:focus-within {
  box-shadow: 0 0 5px rgba(25, 34, 77, 0.15) !important;
}
.select2 .select2-selection:after {
  font-family: "Wpc-icon";
  content: "\e90f";
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 0.6875rem;
  color: rgba(131, 133, 142, 0.4);
}
.select2 .select2-selection .select2-selection__arrow {
  display: none;
}

.WpcPaginationWrapper {
  margin-top: 50px;
}
@media (max-width: 767.98px) {
  .WpcPaginationWrapper {
    margin-top: 30px;
  }
}
@media (max-width: 575.98px) {
  .WpcPaginationWrapper {
    margin-top: 20px;
  }
}
.WpcPaginationWrapper .pagination .page-item .page-link {
  height: 40px;
  min-width: 40px;
  border: 1px solid rgba(131, 133, 142, 0.3);
  border-radius: 10px;
  color: #c4c4c4;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 2px;
  font-size: 1rem;
  line-height: 1;
  letter-spacing: -0.03em;
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transition-property: border-color, color;
  -moz-transition-property: border-color, color;
  -o-transition-property: border-color, color;
  transition-property: border-color, color;
}
.WpcPaginationWrapper .pagination .page-item .page-link.WpcPrev, .WpcPaginationWrapper .pagination .page-item .page-link.WpcNext {
  font-size: 0.6875rem;
}
.WpcPaginationWrapper .pagination .page-item .page-link:focus, .WpcPaginationWrapper .pagination .page-item .page-link:hover, .WpcPaginationWrapper .pagination .page-item .page-link.WpcActive {
  border-color: #d17842;
  color: #d17842;
  background: transparent;
  box-shadow: none;
}
.WpcPaginationWrapper .pagination .page-item:not(:last-child) {
  margin-right: 10px;
}

.WpcLoginBody {
  min-height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-color: #1c0a07;
}
.WpcLoginBody .WpcLoginHeader {
  position: absolute;
  top: 50px;
  left: 50px;
  right: 50px;
}
.WpcLoginBody .WpcLoginHeader .WpcSiteTitle {
  color: #fff;
  font-size: 1.875rem;
  font-weight: 500;
  line-height: 1.25;
  display: flex;
  letter-spacing: -0.06em;
}
.WpcLoginBody .WpcLoginWrapper {
  max-width: 600px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-left: 30px;
  margin-right: 30px;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 120px 0 50px;
  display: flex;
  align-items: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm {
  background: #fff;
  padding: 40px;
  width: 100%;
  border-radius: 35px;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm .WpcLoginTabWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm .WpcLoginTabWrapper .WpcLoginTab {
  cursor: pointer;
  font-size: 1.5rem;
  line-height: 1;
  font-weight: 500;
  letter-spacing: -0.03em;
  color: #b8babf;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm .WpcLoginTabWrapper .WpcLoginTab.WpcActive {
  color: #19224d;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm .WpcLoginTabWrapper .WpcLoginTab:not(:last-child) {
  margin-right: 30px;
}
.WpcLoginBody .WpcLoginWrapper .WpcLoginContent .WpcLoginForm .WpcLoginFormTitle {
  font-size: 1.5rem;
  font-weight: 500;
  color: #19224d;
  margin-bottom: 30px;
}

.modal-dialog {
  border: none;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 50px;
  padding-bottom: 150px;
  min-height: 100%;
  display: flex;
  justify-content: center;
}
.modal-content {
  padding: 40px;
  padding-bottom: 30px;
  border-radius: 35px;
  margin-top: auto;
  margin-bottom: auto;
  border: none;
}
.modal-backdrop {
  background-color: #19224d !important;
  opacity: 0.5 !important;
}
.modal .WpcModalHead {
  display: flex;
  align-items: center;
}
.modal .WpcModalTitle {
  font-size: 1.5rem;
  line-height: 1;
  color: #19224d;
  letter-spacing: -0.03em;
  font-weight: 500;
}
.modal .WpcModalBody {
  padding-top: 35px;
  background: #fff;
}

.WpcPageBody {
  display: flex;
  overflow-x: hidden;
}
@media (min-width: 1200px) {
  .WpcPageBody .WpcSidebar {
    width: 250px;
    min-width: 250px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: width, min-width;
    -moz-transition-property: width, min-width;
    -o-transition-property: width, min-width;
    transition-property: width, min-width;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent {
    width: 250px;
    min-width: 250px;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background: #1c0a07;
    padding: 30px 20px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: width, min-width, overflow;
    -moz-transition-property: width, min-width, overflow;
    -o-transition-property: width, min-width, overflow;
    transition-property: width, min-width, overflow;
    display: flex;
    flex-direction: column;
    -webkit-box-orient: vertical;
    z-index: 1010;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar {
    width: 6px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(131, 133, 142, 0.1);
    background-image: linear-gradient(to right bottom, rgba(209, 120, 66, 0.05), rgba(0, 124, 219, 0.05));
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-image: linear-gradient(to left top, rgba(209, 120, 66, 0.5), rgba(209, 120, 66, 0.3));
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop {
    margin-bottom: 40px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo {
    max-width: 170px;
    margin-left: 10px;
    display: flex;
    visibility: visible;
    opacity: 1;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1) 0.1s;
    -webkit-transition-property: visibility, opacity;
    -moz-transition-property: visibility, opacity;
    -o-transition-property: visibility, opacity;
    transition-property: visibility, opacity;
    cursor: pointer;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo img,
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo svg {
    display: flex;
    height: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo .WpcSiteTitle {
    color: #fff;
    font-size: 1.875rem;
    font-weight: 500;
    line-height: 1.25;
    display: flex;
    letter-spacing: -0.06em;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed {
    max-width: 45px;
    display: flex;
    visibility: hidden;
    opacity: 0;
    margin-left: 1px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: visibility, opacity;
    -moz-transition-property: visibility, opacity;
    -o-transition-property: visibility, opacity;
    transition-property: visibility, opacity;
    cursor: pointer;
    align-items: center;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed img,
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed svg {
    display: none;
    height: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed .WpcSiteTitle {
    color: #fff;
    font-size: 1.875rem;
    font-weight: 500;
    line-height: 1.25;
    display: none;
    letter-spacing: -0.15em;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContentMiddle {
    flex: 1;
    margin-bottom: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNav {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink {
    padding: 9px;
    text-decoration: none;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 1.3rem;
    color: #fff;
    border: 1px solid transparent;
    border-radius: 16px;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLinkIcon {
    width: 40px;
    min-width: 40px;
    height: 40px;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLinkText {
    margin-left: 15px;
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    min-width: 150px;
    transition: all 0.1s ease-in-out;
    transform-origin: left;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active {
    border-color: rgba(255, 255, 255, 0.15);
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover .WpcSidebarNavItemLinkIcon, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active .WpcSidebarNavItemLinkIcon {
    background: #d17842;
    border-color: #d17842;
    color: #fff;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover .WpcSidebarNavItemLinkText, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active .WpcSidebarNavItemLinkText {
    color: #fff;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItem:not(:last-child) {
    margin-bottom: 20px;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) {
    width: 100px;
    min-width: 100px;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContent {
    width: 100px;
    min-width: 100px;
    overflow: hidden;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogo {
    visibility: hidden;
    opacity: 0;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogo .WpcSiteTitle,
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogo img,
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogo svg {
    display: none;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogoCollapsed {
    visibility: visible;
    opacity: 1;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogoCollapsed .WpcSiteTitle,
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogoCollapsed img,
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContentTop .WpcSiteLogoCollapsed svg {
    display: block;
  }
  .WpcPageBody .WpcSidebar.Toggled:not(:hover) .WpcSidebarContent .WpcSidebarNav .WpcSidebarNavItem .WpcSidebarNavItemLink .WpcSidebarNavItemLinkText {
    margin-left: 0;
    transform: rotateY(90deg);
    visibility: hidden;
    opacity: 0;
  }
}
@media (max-width: 1199.98px) {
  .WpcPageBody .WpcSidebar {
    width: 250px;
    min-width: 250px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: width, min-width;
    -moz-transition-property: width, min-width;
    -o-transition-property: width, min-width;
    transition-property: width, min-width;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent {
    width: 250px;
    min-width: 250px;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background: #1c0a07;
    padding: 30px 20px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: width, min-width, overflow;
    -moz-transition-property: width, min-width, overflow;
    -o-transition-property: width, min-width, overflow;
    transition-property: width, min-width, overflow;
    display: flex;
    flex-direction: column;
    -webkit-box-orient: vertical;
    z-index: 1010;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar {
    width: 6px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(131, 133, 142, 0.1);
    background-image: linear-gradient(to right bottom, rgba(209, 120, 66, 0.05), rgba(0, 124, 219, 0.05));
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-image: linear-gradient(to left top, rgba(209, 120, 66, 0.5), rgba(209, 120, 66, 0.3));
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop {
    margin-bottom: 40px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo {
    max-width: 170px;
    margin-left: 10px;
    display: flex;
    visibility: visible;
    opacity: 1;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1) 0.1s;
    -webkit-transition-property: visibility, opacity;
    -moz-transition-property: visibility, opacity;
    -o-transition-property: visibility, opacity;
    transition-property: visibility, opacity;
    cursor: pointer;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo img,
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo svg {
    display: flex;
    height: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogo .WpcSiteTitle {
    color: #fff;
    font-size: 1.875rem;
    font-weight: 500;
    line-height: 1.25;
    display: flex;
    letter-spacing: -0.06em;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed {
    max-width: 45px;
    display: flex;
    visibility: hidden;
    opacity: 0;
    margin-left: 1px;
    transition: all 0.2s cubic-bezier(1, 0.2, 0.2, 1);
    -webkit-transition-property: visibility, opacity;
    -moz-transition-property: visibility, opacity;
    -o-transition-property: visibility, opacity;
    transition-property: visibility, opacity;
    cursor: pointer;
    align-items: center;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed img,
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed svg {
    display: none;
    height: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarContentTop .WpcSiteLogoCollapsed .WpcSiteTitle {
    color: #fff;
    font-size: 1.875rem;
    font-weight: 500;
    line-height: 1.25;
    display: none;
    letter-spacing: -0.15em;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContentMiddle {
    flex: 1;
    margin-bottom: 30px;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNav {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink {
    padding: 9px;
    text-decoration: none;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 1.3rem;
    color: #fff;
    border: 1px solid transparent;
    border-radius: 16px;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLinkIcon {
    width: 40px;
    min-width: 40px;
    height: 40px;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLinkText {
    margin-left: 15px;
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    min-width: 150px;
    transition: all 0.1s ease-in-out;
    transform-origin: left;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active {
    border-color: rgba(255, 255, 255, 0.15);
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover .WpcSidebarNavItemLinkIcon, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active .WpcSidebarNavItemLinkIcon {
    background: #d17842;
    border-color: #d17842;
    color: #fff;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink:hover .WpcSidebarNavItemLinkText, .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItemLink.Active .WpcSidebarNavItemLinkText {
    color: #fff;
  }
  .WpcPageBody .WpcSidebar .WpcSidebarContent .WpcSidebarNavItem:not(:last-child) {
    margin-bottom: 20px;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) {
    width: 100px;
    min-width: 100px;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContent {
    width: 100px;
    min-width: 100px;
    overflow: hidden;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogo {
    visibility: hidden;
    opacity: 0;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogo .WpcSiteTitle,
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogo img,
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogo svg {
    display: none;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogoCollapsed {
    visibility: visible;
    opacity: 1;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogoCollapsed .WpcSiteTitle,
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogoCollapsed img,
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContentTop .WpcSiteLogoCollapsed svg {
    display: block;
  }
  .WpcPageBody .WpcSidebar:not(.Toggled) .WpcSidebarContent .WpcSidebarNav .WpcSidebarNavItem .WpcSidebarNavItemLink .WpcSidebarNavItemLinkText {
    margin-left: 0;
    transform: rotateY(90deg);
    visibility: hidden;
    opacity: 0;
  }
}
.WpcPageBody .WpcContentArea {
  display: flex;
  flex-direction: column;
  background: #fff;
  min-height: 100vh;
  flex-grow: 1;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader {
  z-index: 1005;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper {
  padding: 50px;
  display: flex;
  align-items: center;
}
@media (max-width: 767.98px) {
  .WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper {
    padding: 30px;
  }
}
@media (max-width: 575.98px) {
  .WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper {
    padding: 15px;
  }
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcNotificationIcon {
  height: 74px;
  width: 74px;
  min-width: 74px;
  border: 1px solid rgba(209, 120, 66, 0.3);
  border-radius: 25px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 1.625rem;
  color: #d17842;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcNotificationIcon.WpcHasNotification {
  position: relative;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcNotificationIcon.WpcHasNotification:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 15px;
  width: 15px;
  background: #16ad9f;
  border-radius: 10px;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton {
  display: flex;
  align-items: center;
  position: relative;
  padding: 11px;
  padding-right: 50px;
  border: none;
  cursor: pointer;
  border: 1px solid rgba(209, 120, 66, 0.3);
  border-radius: 25px;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton .WpcProfileImage {
  height: 50px;
  width: 50px;
  min-width: 50px;
  display: flex;
  border-radius: 18px;
  background: rgba(209, 120, 66, 0.3);
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton .WpcProfileImage img {
  display: inline-flex;
  padding: 1px;
  width: 100%;
  height: 100%;
  border-radius: 16px;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton .WpcProfileDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 15px;
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton .WpcProfileDetails .WpcUserName {
  font-size: 1.25rem;
  font-weight: 400;
  color: #19224d;
}
@media (max-width: 575.98px) {
  .WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton .WpcProfileDetails .WpcUserName {
    display: none;
  }
}
.WpcPageBody .WpcContentArea .WpcContentAreaHeader .WpcHeaderWrapper .WpcContentAreaHeaderRight .WpcProfileControlButton:after {
  font-family: "Wpc-icon";
  content: "\e90f";
  position: absolute;
  border: none !important;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #d17842;
}
@media (min-width: 1200px) {
  .WpcPageBody .WpcContentArea {
    min-width: calc(100% - 250px);
  }
}
@media (max-width: 1199.98px) {
  .WpcPageBody .WpcContentArea {
    min-width: calc(100% - 100px);
  }
}
.WpcPageBody .WpcContentArea .WpcContentAreaBody {
  flex: 1;
  padding: 0 50px 100px;
  display: flex;
  flex-direction: column;
}
@media (max-width: 767.98px) {
  .WpcPageBody .WpcContentArea .WpcContentAreaBody {
    padding: 0 30px 50px;
  }
}
@media (max-width: 575.98px) {
  .WpcPageBody .WpcContentArea .WpcContentAreaBody {
    padding: 0 15px 30px;
  }
}

.WpcDashboardContentWrapper {
  display: flex;
}
@media (max-width: 1400.98px) {
  .WpcDashboardContentWrapper {
    flex-wrap: wrap-reverse;
  }
}
.WpcDashboardContentWrapper .WpcAnalyticsSection {
  flex: 1;
  margin-right: 50px;
  width: 100%;
}
@media (max-width: 1600.98px) {
  .WpcDashboardContentWrapper .WpcAnalyticsSection {
    margin-right: 20px;
  }
}
@media (max-width: 1400.98px) {
  .WpcDashboardContentWrapper .WpcAnalyticsSection {
    margin-right: 0;
    margin-top: 50px;
  }
}/*# sourceMappingURL=style.css.map */
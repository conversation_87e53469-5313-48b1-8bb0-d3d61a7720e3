{"version": 3, "sources": ["webpack:///./utilities/_item.scss", "webpack:///./style.scss", "webpack:///./_reboot.scss", "webpack:///./_variables.scss", "webpack:///./mixins/_background&color.scss", "webpack:///./utilities/_button.scss", "webpack:///./mixins/_transition.scss", "webpack:///./utilities/_utilities.scss", "webpack:///./utilities/_form.scss", "webpack:///./utilities/_welcome.scss", "webpack:///./utilities/_header.scss", "webpack:///./utilities/_statistic.scss", "webpack:///./utilities/_order.scss", "webpack:///./utilities/_modal.scss", "webpack:///./utilities/_notification.scss", "webpack:///./utilities/_drawer.scss", "webpack:///./utilities/_employee.scss"], "names": [], "mappings": ";AAwDwB,gBCu6BxB,CC39BA,EAGC,YAEA,sBAJA,SAGA,aAFA,SDED,CCGA,KACC,cDAD,CCEA,KAOC,6CACA,0CACA,6BAEA,gCALA,eCTO,CDIP,+BAEA,gBACA,iBAFA,gBAGA,iBAKA,sBDED,CCAC,wBACC,SDEF,CCAC,8BAEC,4FADA,oDDGF,CCIC,8BAEC,mBADA,iBDDF,CCOA,MAEC,eDJD,CCMA,EACC,aCzCM,CD0CN,yBDHD,CCIC,gBAIG,UC9CI,CD6CN,aADA,oBDDF,CCMA,MACC,eDHD,CCKA,OAEC,iBADA,qBDDD,CCGC,0BAEC,YDFF,CCcA,0BAEC,eDDD,CCIA,KACC,kBACA,kBDDD,CCEC,mBACC,kBACA,kBDAF,CGtFC,iBACC,uBHyFF,CGtFE,gDAIE,0BADA,uBHwFJ,CG/FC,mBACC,uBHkGF,CG/FE,oDAIE,0BADA,uBHiGJ,CGxGC,iBACC,uBH2GF,CGxGE,gDAIE,0BADA,uBH0GJ,CGjHC,gBACC,uBHoHF,CGjHE,8CAIE,0BADA,uBHmHJ,CG1HC,cACC,uBH6HF,CG1HE,0CAIE,0BADA,uBH4HJ,CGnIC,iBACC,uBHsIF,CGnIE,gDAIE,0BADA,uBHqIJ,CG5IC,cACC,uBH+IF,CG5IE,0CAIE,0BADA,uBH8IJ,CGrJC,eACC,oBHwJF,CGzJC,eACC,uBH4JF,CGzJE,4CAIE,0BADA,uBH2JJ,CGlKC,eACC,oBHqKF,CGlKE,4CAIE,0BADA,uBHoKJ,CG3JC,sBACC,kCH8JF,CG/JC,wBACC,kCHkKF,CGnKC,sBACC,kCHsKF,CGvKC,qBACC,kCH0KF,CG3KC,mBACC,kCH8KF,CG/KC,sBACC,kCHkLF,CGnLC,mBACC,kCHsLF,CGvLC,oBACC,+BH0LF,CG3LC,oBACC,kCH8LF,CG/LC,oBACC,+BHkMF,CG5LC,iBACC,kEAKA,UH2LF,CGjMC,mBACC,kEAKA,UHgMF,CGtMC,iBACC,kEAKA,UHqMF,CG3MC,gBACC,kEAKA,UH0MF,CGhNC,cACC,kEAKA,UH+MF,CGrNC,iBACC,kEAKA,UHoNF,CG1NC,cACC,kEAKA,UHyNF,CG/NC,eACC,+DAKA,UH8NF,CGpOC,eACC,+DAKA,aHmOF,CGzOC,eACC,4DAKA,aHwOF,CIvQA,WASC,+BACA,yBAFA,mBAGA,gBAPA,gBAEA,YAQA,kBCVA,kCAMA,yCDGA,CARA,WAEA,SJuRD,CI/QC,4BAbA,mBADA,oBAEA,sBJ+SD,CInSC,iBAIC,kBFXK,CEgBL,mBAJA,UFTM,CEcN,cATA,YAEA,eCdD,kCAMA,2CDiBC,CAVA,UJiSF,CIrRC,iBAEC,eACA,gBAFA,cJyRF,CIrRE,mCACC,iBJuRH,CIrRE,kCACC,kBJuRH,CIpRC,iBAEC,gDJsRF,CIpRC,sCAHC,kBJ0RF,CIrRE,2BACC,mBACA,gDJuRH,CIpRC,uBACC,uBACA,iCACA,kBJsRF,CIrRE,6BACC,aJuRH,CIrRE,6BACC,uBACA,eJuRH,CIlRA,yCACE,WJqRF,CIlRA,eAIC,kBAHA,cJwRD,CIpRC,oCAFA,mBADA,mBJ2SD,CIxSC,qBAKC,mCADA,mBAMA,aFnFQ,CEkFR,eARA,YAOA,uBALA,eC1ED,kCAMA,uDD6EC,CAVA,UJsSF,CI3RE,sCACC,iBJ6RH,CI1RC,qBAGC,aF5FU,CE0FV,kBACA,oBJ6RF,CIzRE,2BAEC,wBFlGO,CEiGP,oBFjGO,CEmGP,UJ2RH,CIvRE,gCAIC,mBACA,kBAJA,YAEA,eADA,UJ4RH,CIxRG,iDACC,iBJ0RJ,CIvRE,gCACC,cJyRH,CIrRE,+BAEC,kBFxHO,CEuHP,oBFvHO,CEyHP,aJuRH,CIpRG,qCACC,mBACA,gDJsRJ,CIjRE,iCACC,uBACA,iCACA,0BACA,kBJmRH,CIjRE,iCACC,aJmRH,CIhRG,uCACC,uBACA,eJkRJ,CI5QA,cAIC,eAHA,kBJkRD,CI9QC,kCAFA,mBADA,mBJqSD,CIlSC,oBAKC,qCADA,mBAEA,aF/JU,CEmKV,gBATA,YAQA,uBANA,eC3JD,kCAMA,sCD8JC,CAVA,UJgSF,CIrRE,qCACC,iBJuRH,CIpRC,oBAGC,aF7KU,CE2KV,kBACA,iBC3KD,mCLscD,CItRE,0BACC,oBFlLS,CEmLT,aJwRH,CItRE,0BACC,aJwRH,CInRA,eAEC,mBACA,aFzLM,CEuLN,YJwRD,CIrRC,qBAIC,mBADA,oBAFA,kBACA,iBJ8RF,CIxRC,0CAHG,aFpMQ,CGCX,iCLweD,CIlSC,qBAGC,aAFA,mBACA,eJgSF,CIvRI,sDACE,aJ4RN,CMjfA,KACC,kBNofD,CMjfA,SAEC,gBACA,iBNsfD,CMjfA,mBAPC,aAIA,cADA,eNigBD,CM7fA,UASC,mBAJA,aACA,sBAJA,kBAEA,eNyfD,CMjfA,MACC,eNofD,CMjfA,MACC,iBNofD,CMlfA,MACC,kBNqfD,CMlfA,QACC,kBJpCS,CIuCT,gBAFA,aACA,iBNsfD,CMpfC,UAEC,eACA,eNsfF,CMpfC,oBAJC,UN2fF,CMlfA,eAEC,wBACA,4BAFA,qBNufD,CMlfA,iEACC,eAGA,yBNqfD,CMnfE,wEAID,cAHA,cACA,eACA,eNufD,COzjBA,cACC,aACA,sBACA,0BP4jBD,CO3jBC,4BAEC,aLHU,CKEV,eAIA,cAFA,mBACA,iBP8jBF,CO3jBC,+BACC,mBACA,eP6jBF,COxjBA,mBAKC,eLZO,CKaP,iCACA,mBACA,iBAJA,aLdM,CKaN,eAFA,YACA,cPikBD,CO1jBC,yBACC,qCP4jBF,CO1jBC,qCACC,aP4jBF,CO7jBC,yCACC,aP4jBF,CO7jBC,gCACC,aP4jBF,CO1jBC,oCACC,iBP4jBF,CO1jBC,+BAEC,SAEA,4CAHA,gBAIA,gBAFA,YAGA,SP4jBF,CO1jBC,kCACC,mBP4jBF,COxjBA,qBAEC,mBADA,aAEA,kBP2jBD,CO1jBC,kCAGC,mBADA,YAEA,kBAHA,UP+jBF,COxjBA,aAEC,mBADA,iBP4jBD,CO1jBC,2BAGC,aL3DK,CKyDL,eACA,gBAEA,kBAEA,QADA,QP6jBF,CO1jBC,mBAGC,cADA,qBADA,kBP8jBF,COxjBA,qBACC,mBP2jBD,COxjBA,aAGC,mBAFA,eACA,iBP4jBD,CO1jBC,2BAKC,cADA,eADA,UAGA,oBALA,kBACA,QPgkBF,CO1jBC,4BACC,mBAGA,mBAGA,cAJA,YAGA,sBAJA,UPikBF,CO3jBE,wDACC,aP6jBH,CO3jBE,+CACC,aP6jBH,CO3jBE,mDACC,aP6jBH,CO3jBE,8CACC,aP6jBH,CQ5qBA,qBACI,aACA,sBAGA,aADA,8BAEA,aAHA,iBRkrBJ,CQ9qBI,0CACI,eRgrBR,CQ9qBI,+CACI,eRgrBR,CQ/qBQ,kDAGI,aNTL,CMOK,eACA,gBAEA,kBRirBZ,CQ/qBQ,iDAGI,aNpBA,CMkBA,eACA,eRkrBZ,CQ/qBQ,sEACI,eRirBZ,CQ9qBI,uCACI,kBRgrBR,CQ/qBQ,kDACI,kBRirBZ,CQ/qBQ,uDACI,aNhCA,CMiCA,eRirBZ,CQhrBY,yDACI,aN9BT,CM+BS,eRkrBhB,CSvtBA,QACI,kBPFM,COIN,iDADA,sBT2tBJ,CSztBI,qBACI,aACA,8BACA,mBT2tBR,CS1tBQ,gCACI,UT4tBZ,CS3tBY,kCACI,UPFR,COGQ,cT6tBhB,CSztBY,2CACI,aPjBN,COkBM,eACA,iBT2tBhB,CS1tBgB,kDAMI,kBPxBV,COyBU,kBACA,WAJA,WAHA,kBAEA,UADA,MAGA,ST+tBpB,CSvtBI,0BACI,aACA,iBTytBR,CSxtBQ,4BAEI,aPtCF,COqCE,eAEA,eT0tBZ,CSxtBQ,6BAGI,UPnCJ,COiCI,eACA,eT2tBZ,CSxtBQ,kDACI,kBAEA,WADA,QT2tBZ,CSxtBgB,8DACI,YT0tBpB,CSvtB4B,iFAEI,kBPvDtB,COsDsB,ST0tBhC,CSptBgB,6DAEI,UPvDZ,COyDY,qBAHA,eAIA,kBAFA,iBTwtBpB,CSrtBoB,oEAOI,8BADA,mBAEA,WAJA,YADA,OAFA,kBACA,QAGA,UT0tBxB,CSptBoB,mEAMI,kBPpFZ,COqFY,kBACA,WAJA,YADA,SAFA,kBACA,QAOA,wBAJA,UT0tBxB,CS9sBA,iBAGI,mBAFA,aACA,8BAGA,mBADA,cTktBJ,CShtBI,yCAGI,UPhGA,CO8FA,eACA,eTmtBR,CShtBI,wCAII,yBADA,mBAEA,aPhHE,CO4GF,YAKA,iBACA,kBALA,UTutBR,CSjtBQ,0CACI,aTmtBZ,CS9sBA,cAII,mBAFA,aACA,8BAEA,mBAJA,mBTqtBJ,CShtBI,2DAEI,MTktBR,CS/sBQ,2CAGI,UP9HJ,CO4HI,eACA,eTktBZ,CS9sBI,2BACI,cACA,iBTgtBR,CS/sBQ,8BAGI,UPvIJ,COqII,eACA,eTktBZ,CS9sBI,8BACI,gBTgtBR,CS/sBQ,6CAGI,UP/IJ,CO6II,eACA,eTktBZ,CUz2BA,mBAEI,aACA,8BACA,iBAHA,cV+2BJ,CU32BI,oCAGI,6BACA,mBAHA,2BACA,aAGA,uBV62BR,CU52BQ,0CACI,kBV82BZ,CU52BgB,2DACI,4BV82BpB,CU32BY,4CACI,UV62BhB,CU12BQ,+CAEI,mBADA,aAEA,kBV42BZ,CU32BY,qDAII,kBR9BN,CQ+BM,mBAJA,YAKA,kBAHA,kBAIA,wBALA,UVk3BhB,CU52BgB,uDACI,UR1BZ,CQ4BY,eADA,gBV+2BpB,CU32BY,kDAEI,URjCR,CQgCQ,eAEA,eV62BhB,CU12BQ,sCAEI,cADA,eAEA,uBV42BZ,CD75BA,YACI,aAEA,gBADA,mBCi6BJ,CD/5BI,uBACI,cCi6BR,CDh6BQ,2CACI,cCk6BZ,CDh6BQ,6BACI,kBCk6BZ,CD75BA,MAKI,wBGRK,CHML,mBAFA,2BACA,kBAEA,gBAEA,YCg6BJ,CD/5BI,iBACI,cCi6BR,CD/5BI,mBAEI,mBADA,gBCk6BR,CD/5BI,kBAII,+BADA,gCAFA,kBACA,iBCm6BR,CDh6BQ,8BAEI,UG3BJ,CH0BI,eAEA,eCk6BZ,CDh6BQ,oBAGI,aGzCA,CHuCA,eACA,gBAEA,cCk6BZ,CDj6BY,yBACI,aCm6BhB,CDh6BQ,wBACI,cCk6BZ,CDj6BY,8BACI,YCm6BhB,CDh6BwB,kDACI,oBGrDlB,CHwDkB,aGxDlB,CHsDkB,YACA,qBAEA,cCk6B5B,CD75BY,6BAGI,aGlEJ,CHgEI,eACA,gBAGA,kBADA,iBCg6BhB,CD95BgB,oCAII,qCACA,kBAGA,WAFA,YAHA,OAMA,iBARA,kBACA,QAKA,UCk6BpB,CD35BI,UAGI,mBAFA,0BACA,iBC85BR,CD55BQ,sBAGI,+BADA,gCADA,YCg6BZ,CD75BY,kCACI,eACA,eC+5BhB,CW9/BA,uBACI,iBXigCJ,CW//BA,iBAGI,mBACA,yBACA,mBAHA,aADA,gBAMA,yBAEA,uBXkgCJ,CWjgCI,wCAFA,kBXsgCJ,CWjgCY,gDACI,UXmgChB,CW//BI,yCAGI,mBAFA,eACA,gBAEA,eXigCR,CWhgCQ,6CACI,UXkgCZ,CW//BI,wCACI,iBXigCR,CWhgCQ,2CAGI,UTzBJ,CSuBI,eACA,gBAEA,kBXkgCZ,CWhgCQ,2CAGI,UT/BJ,CS6BI,eACA,gBAEA,iBXkgCZ,CWhgCQ,uDACI,kBAEA,WADA,QXmgCZ,CWhgCQ,0CAGI,cAFA,eACA,gBAEA,uBXkgCZ,CW7/BA,oBAGI,gBADA,SADA,cXkgCJ,CW//BI,mCACI,wBTlDC,CSmDD,mBACA,YXigCR,CWhgCQ,oDACI,kBXkgCZ,CWhgCQ,+CAGI,mBAFA,aACA,8BAEA,kBXkgCZ,CWjgCY,8DAII,+BAGA,qCADA,mBAHA,aT5EN,CS0EM,eACA,gBAGA,gBXqgChB,CWhgCQ,8CAGI,aTtFF,CSoFE,eACA,gBAEA,kBXkgCZ,CWhgCQ,mDAEI,mBADA,YXmgCZ,CWjgCY,gEAKI,mBAHA,YAEA,kBADA,eAFA,UXugChB,CWhgCgB,kEAGI,UT9FZ,CS4FY,eACA,gBAEA,iBXkgCpB,CWhgCgB,4EAGI,aT5GR,CS0GQ,eACA,eXmgCpB,CW3/BA,cAEI,gBACA,mBX8/BJ,CW7/BI,wBAGI,mBADA,gBAEA,mBAHA,kBXkgCR,CW9/BQ,kCAEI,aADA,eXigCZ,CW//BY,4CACI,SAGA,mBAGA,aT5HT,CS0HS,eACA,gBAJA,kBACA,iBXqgChB,CWhgCgB,mDACI,kBTzIV,CS0IU,UXkgCpB,CW7/BI,oCACI,aACA,yBACA,kBX+/BR,CW9/BQ,sCAKI,yBAGA,mBAFA,cACA,qBANA,YAEA,iBACA,kBAFA,UXsgCZ,CWz/BI,8BAII,yBADA,mCADA,mBADA,YX+/BR,CW3/BQ,+CACI,kBX6/BZ,CW3/BQ,6CAEI,mBADA,aAEA,kBX6/BZ,CW5/BY,gIAKI,mBAFA,YACA,kBAFA,UXigChB,CW3/BgB,+DAGI,UThLZ,CS8KY,eACA,eX8/BpB,CW3/BgB,iEAGI,cAFA,eACA,gBAEA,wBX6/BpB,CWz/BQ,2CAEI,uBADA,aAEA,6BX2/BZ,CW1/BY,iEACI,cACA,aX4/BhB,CW3/BgB,oEAGI,UTpMZ,CSkMY,eACA,eX8/BpB,CW3/BgB,4EAGI,aT1MZ,CSwMY,eACA,eX8/BpB,CW1/BY,gEACI,aACA,gBACA,kBX4/BhB,CW3/BgB,2EAQI,wBTjOV,CS8NU,mBAIA,WANA,qBAGA,eACA,gBANA,gBASA,gBARA,iBAUA,uBADA,mBAPA,UXqgCpB,CW3/BgB,sEAEI,yBACA,qBAFA,UX+/BpB,CWz/BQ,4CACI,eACA,iBX2/BZ,CW1/BY,8CAGI,cAFA,eAIA,kBAHA,gBAEA,kBAEA,iBX4/BhB,CW3/BgB,qDAMI,cALA,YAIA,gCADA,OAFA,kBACA,KXggCpB,CWt/BA,YACI,cXy/BJ,CWx/BI,+BAGI,aTzQI,CSuQJ,eACA,gBAEA,kBX0/BR,CWx/BI,8BAGI,mBAFA,aACA,8BAEA,kBX0/BR,CWz/BQ,2CAEI,cADA,iBX4/BZ,CW1/BY,gDAKI,aTzRJ,CSwRI,eAEA,oBALA,kBAEA,WADA,QXggChB,CW1/BY,kDAQI,6DADA,qCALA,mBAGA,aT5RT,CS2RS,eAEA,gBALA,YAEA,qBXigChB,CWz/BQ,oDACI,YX2/BZ,CW1/BY,mHASI,qCADA,mBADA,aThTJ,CS2SI,YAIA,iBAFA,eACA,kBAFA,UXkgChB,CW3/BgB,qJACI,iBX8/BpB,CW5/BgB,kSAEI,cX+/BpB,CW5/BY,0DACI,6DACA,iBX8/BhB,CY5zCI,uBACI,4BZ+zCR,CY7zCI,gCAII,SAEA,SALA,gBACA,kBACA,QAEA,wCZg0CR,CY9zCQ,+CACI,sBZg0CZ,CY7zCI,8BACI,SZ+zCR,CY9zCQ,oCAGI,kBVPH,CUKG,mBACA,eZi0CZ,CY/zCY,gDACI,sBZi0ChB,CYh0CgB,4DACI,cZk0CpB,CYh0CgB,kDACI,cZk0CpB,CYh0CgB,2DACI,eZk0CpB,Cah2CA,kBACI,cbm2CJ,Cal2CI,sCAEI,wBXSC,CWVD,mCAEA,mBACA,iBbo2CR,Can2CQ,uDACI,kBbq2CZ,Can2CQ,0DAGI,aXXA,CWSA,eACA,gBAEA,ebq2CZ,Can2CQ,4CAEI,uBADA,Qbs2CZ,Cap2CY,yDAEI,uBACA,gBAFA,Sbw2ChB,Can2CoB,iEAII,UXnBhB,CWiBgB,eACA,gBAFA,UAKA,kBADA,oBbs2CxB,Cap2CwB,sEAII,aXxBrB,CWyBqB,eAJA,kBAEA,QADA,QAIA,oBACA,uBbs2C5B,Can2C4B,gFACI,wBbq2ChC,Ca/1CY,uDAEI,cbi2ChB,Cah2CgB,yDAGI,aX3Cb,CWyCa,eACA,ebm2CpB,Ca31CA,sBAEI,cADA,Yb+1CJ,Ca51CQ,kDACI,gBb81CZ,Ca31CY,wDAEI,8BADA,0Bb81ChB,Caz1CY,uDAEI,+BADA,2Bb41ChB,Cav1CY,qDAGI,uBADA,iCADA,yBb21ChB,Car1CY,mDAGI,mBADA,qBADA,Uby1ChB,Cap1CQ,4CAOI,uBAEA,yBACA,kBAFA,cAHA,eAJA,YAGA,iBADA,UAGA,kBAJA,Ub81CZ,Car1CY,sDAGI,mBADA,qBADA,Uby1ChB,Car1CY,8CACI,cbu1ChB,Ccx8CA,8BACI,kBZaK,CYRL,YAFA,YAFA,kBACA,MAKA,wBAHA,WAEA,Ud48CJ,Cc18CI,mCACI,Md48CR,Ccx8CA,wBAEI,aACA,sBACA,YAHA,sBd88CJ,Cc18CI,oDAGI,UZXA,CYUA,qBADA,kBd88CR,Cc18CI,oDACI,aACA,8BACA,kBd48CR,Cc38CQ,8EAEI,mBADA,Yd88CZ,Cc58CY,qFAKI,mBAHA,YAEA,kBADA,eAFA,Udk9ChB,Cc38CgB,gGAGI,UZ/BZ,CY6BY,eACA,ed88CpB,Cc38CgB,iGAGI,aZlCb,CYgCa,eACA,ed88CpB,Ccz8CgB,sFAII,qCADA,mBAKA,aZzDV,CYwDU,qBANA,YAIA,iBACA,kBAJA,Udi9CpB,Ccp8CQ,4DAEI,aADA,cdu8CZ,Ccr8CY,6EACI,2Cdu8ChB,Ccr8CY,kEAUI,yBALA,qCAIA,mBAHA,aZ5EN,CY8EM,qBAPA,YAEA,iBAIA,kBAHA,kBAFA,Ud+8ChB,Ccr8CY,kEAGI,UZ5ER,CY0EQ,eACA,edw8ChB,Cct8CgB,uEACI,adw8CpB,Ce/hDA,sBAEI,mBADA,cfmiDJ,CehiDQ,qDAGI,abNA,CaIA,eACA,gBAEA,kBfkiDZ,Ce7hDA,4BAGI,mBAGA,wBbPK,CaML,mBAJA,aACA,8BAEA,YfkiDJ,Ce/hDI,6CACI,kBfiiDR,Ce/hDI,4CAEI,mBADA,YfkiDR,CehiDQ,6DAKI,mBAJA,cAEA,YACA,kBAFA,UfqiDZ,Ce/hDY,+DAGI,Ub5BR,Ca0BQ,eACA,efkiDhB,Ce/hDY,iEAGI,ablCR,CagCQ,eACA,efkiDhB,Ce7hDI,6CAEI,YADA,UfgiDR,Ce9hDQ,mDACI,YfgiDZ,Ce7hDoB,uEACI,oBbpDd,CauDc,abvDd,CaqDc,YACA,qBAEA,cf+hDxB,Ce1hDQ,kDAII,qBAFA,YADA,kBAEA,Uf6hDZ,Ce3hDY,yDAII,yBACA,kBAGA,WAFA,YAHA,OAMA,iBACA,iBATA,kBACA,QAKA,UfgiDhB,CevhDA,mBACI,iBACA,af0hDJ,CA9lDA,KACI,aAimDJ,C", "file": "BaristaApp/assets/css/style.css", "sourcesContent": ["\n\n.item__wrap {\n    display: flex;\n    padding: 0 15px 15px;\n    overflow-x: auto;\n    &.flex__wrap {\n        flex-wrap: wrap;\n        .item:nth-child(2n) {\n            margin-right: 0;\n        }\n        .item {\n            margin-bottom: 15px;\n        }\n    }\n}\n\n.item {\n    flex: 0 0 calc(50% - 7.5px);\n    margin-right: 15px;\n    border-radius: 20px;\n    overflow: hidden;\n    background-color: $accent;\n    padding: 15px;\n    &:last-child {\n        margin-right: 0;\n    }\n    .item__thumb {\n        padding-top: 100%;\n        border-radius: 20px;\n    }\n    .item__body {\n        padding: 20px 10px;\n        text-align: center;\n        border-bottom-right-radius: 20px;\n        border-bottom-left-radius: 20px;\n        .item__name {\n            font-size: 18px;\n            color: $white;\n            font-weight: 500;\n        }\n        p {\n            font-size: 14px;\n            font-weight: 400;\n            color: $secondary;\n            margin-top: 5px;\n            span {\n                color: $grey;\n            }\n        }\n        label {\n            margin-top: 5px;\n            input {\n                display: none;\n                &:checked {\n                    & ~ span {\n                        &:before {\n                            border-color: $success;\n                            content: '\\e906';\n                            font-family: 'wpc-icon';\n                            color: $success;\n                            font-size: 10px;\n                        }\n                    }\n                }\n            }\n            span {\n                font-size: 14px;\n                font-weight: 400;\n                color: $secondary;\n                position: relative;\n                padding-left: 22px;\n                &:before {\n                    position: absolute;\n                    top: 1px;\n                    left: 0;\n                    border: 2px solid rgba($secondary, .1);\n                    border-radius: 3px;\n                    height: 18px;\n                    width: 18px;\n                    content: '';\n                    line-height: 15px;\n                }\n            }\n        }\n    }\n    &--md {\n        flex: 0 0 calc(40% - 10px);\n        margin-right: 10px;\n        border-radius: 18px;\n        .item__body {\n            padding: 10px;\n            border-bottom-right-radius: 18px;\n            border-bottom-left-radius: 18px;\n            .item__name {\n                font-size: 16px;\n                font-weight: 400;\n            }\n        }\n    }\n}\n", "@import 'variables';\n@import 'mixins/media';\n@import 'reboot';\n@import 'functions';\n@import 'mixins/transition';\n@import 'utilities/background&color';\n@import 'utilities/button';\n@import 'utilities/utilities';\n@import 'utilities/form';\n@import 'utilities/welcome';\n@import 'utilities/header';\n@import 'utilities/statistic';\n@import 'utilities/item';\n@import 'utilities/order';\n@import 'utilities/modal';\n@import 'utilities/notification';\n@import 'utilities/drawer';\n@import 'utilities/employee';\n\n\nbody {\n    color: #f3f3f3;\n}\n// .wpc-body {\n//     background-color: #fff;\n// }\n\n\n// .wpc-body {\n// \twidth: 360px;\n// \theight: 780px;\n// \tmargin: 50px auto 0;\n// \tpadding-bottom: 30px;\n// }", "// google font\n@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');\n\n// basic\n* {\n\tmargin: 0;\n\tpadding: 0;\n\tborder: none;\n\toutline: none;\n\tbox-sizing: border-box;\n}\nhtml {\n\tfont-size: 16px;\n}\nbody {\n\tfont-family: 'DM Sans', sans-serif;\n\tline-height: 1.3;\n\tfont-weight: 400;\n\tletter-spacing: 0;\n\tmin-height: 100vh;\n\tbackground: $white;\n\t-webkit-font-smoothing: antialiased !important;\n\t-moz-font-smoothing: antialiased !important;\n\ttext-rendering: optimizeSpeed;\n\tscroll-behavior: smooth;\n\t-webkit-text-stroke: 0px !important;\n\t&::-webkit-scrollbar {\n\t\twidth: 8px;\n\t}\n\t&::-webkit-scrollbar-track {\n\t\t-webkit-box-shadow: inset 0 0 6px rgba($primary, 0.1);\n\t\tbackground-image: linear-gradient(\n\t\t\tto right bottom,\n\t\t\trgba($primary, 0.05),\n\t\t\trgba($primary, 0.05)\n\t\t);\n\t}\n\t&::-webkit-scrollbar-thumb {\n\t\tborder-radius: 3px;\n\t\tbackground: darken($primary, 10%);\n\t}\n}\n\n// component\nul,\nli {\n\tlist-style: none;\n}\na {\n\tcolor: $dark;\n\ttransition: all 0.3s linear;\n\t&:hover,\n\t&:focus {\n\t\ttext-decoration: none;\n\t\toutline: none;\n    color: $black;\n\t}\n}\nlabel {\n\tmargin-bottom: 0;\n}\nbutton {\n\tvertical-align: middle;\n\tbackground: unset;\n\t&:hover,\n\t&:focus {\n\t\toutline: none;\n\t}\n}\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np {\n\tmargin-bottom: 0;\n}\nul,\nol {\n\tmargin-bottom: 0;\n}\n\n.row {\n\tmargin-left: -25px;\n\tmargin-right: -25px;\n\t*[class*='col-'] {\n\t\tpadding-left: 25px;\n\t\tpadding-right: 25px;\n\t}\n}\n", "// color\n$sidebar: #0c0f14 !default;\n$primary: #d17842 !default;\n$secondary: #83858e !default;\n$success: #16ad9f !default;\n$danger: #ec008c !default;\n$info: #007cdb !default;\n$warning: #e04f5f !default;\n$dark: #19224d !default;\n$black: #000 !default;\n$light: #e6e7e8 !default;\n$white: #fff !default;\n$overlayColor: #0F1326;\n$grey: #DADADD;\n$accent: #1e222a;\n\n// template-color DADADD\n\n$template-colors: () !default;\n$template-colors: map-merge(\n\t(\n\t\t'Primary': $primary,\n\t\t'Secondary': $secondary,\n\t\t'Success': $success,\n\t\t'Danger': $danger,\n\t\t'Info': $info,\n\t\t'Warning': $warning,\n\t\t'Dark': $dark,\n\t\t'Black': $black,\n\t\t'Light': $light,\n\t\t'White': $white,\n\t),\n\t$template-colors\n);\n", "// content color\n@mixin color-emphasis-variant($parent, $color) {\n\t#{$parent} {\n\t\tcolor: $color !important;\n\t}\n\ta#{$parent} {\n\t\t&:hover,\n\t\t&:focus {\n\t\t\t@if $color != #000000 {\n\t\t\t\tcolor: darken($color, 10%) !important;\n\t\t\t\tbox-shadow: none !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// content background\n@mixin background-emphasis-variant($parent, $color) {\n\t#{$parent} {\n\t\tbackground-color: $color !important;\n\t}\n}\n\n// gradient background\n@mixin gradient-emphasis-variant($parent, $color) {\n\t#{$parent} {\n\t\tbackground-image: linear-gradient(\n\t\t\tto bottom right,\n\t\t\t$color,\n\t\t\tlighten($color, 15%)\n\t\t);\n\t\tcolor: set-text-color($color);\n\t}\n}\n", ".WpcButton {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmin-height: 50px;\n\twidth: 100%;\n\tpadding: 1px;\n\tz-index: 1;\n\tborder-radius: 15px;\n\tbackground: rgba($primary, 0.1);\n\tborder: 1px solid $primary;\n\tfont-weight: 500;\n\t@include transition;\n\t@include transition-properties(background, box-shadow);\n\tposition: relative;\n\t.Icon {\n\t\theight: 24px;\n\t\twidth: 24px;\n\t\tmin-width: 24px;\n\t\tbackground: $dark;\n\t\tcolor: $white;\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50px;\n\t\tfont-size: 8px;\n\t\t@include transition($duration: 0.2s);\n\t\t@include transition-properties(background, color, filter);\n\t}\n\t.Text {\n\t\tpadding: 0 25px;\n\t\tfont-size: 1rem;\n\t\tfont-weight: 500;\n\n\t\t&:not(:first-child) {\n\t\t\tpadding-left: 10px;\n\t\t}\n\t\t&:not(:last-child) {\n\t\t\tpadding-right: 10px;\n\t\t}\n\t}\n\t&:hover {\n\t\tbackground: $primary;\n\t\tbox-shadow: 0 20px 20px -10px rgba($primary, 0.4);\n\t}\n\t&.WpcFilled {\n\t\tbackground: $primary;\n\t\t&:hover {\n\t\t\tbackground: darken($primary, 10%);\n\t\t\tbox-shadow: 0 20px 20px -10px rgba($primary, 0.4);\n\t\t}\n\t}\n\t&.WpcDisabled {\n\t\tbackground: transparent;\n\t\tborder-color: rgba($secondary, 0.2);\n\t\tcursor: not-allowed;\n\t\t.Text {\n\t\t\tcolor: $secondary;\n\t\t}\n\t\t&:hover {\n\t\t\tbackground: transparent;\n\t\t\tbox-shadow: none;\n\t\t}\n\t}\n}\n\n.WpcButton[disabled], .WpcButton:disabled{\n\t\topacity: .75;\n}\n\n.WpcEditButton {\n\tcursor: pointer;\n\tdisplay: inline-flex;\n\talign-items: center;\n\talign-self: center;\n\t.Icon {\n\t\theight: 40px;\n\t\twidth: 40px;\n\t\tmin-width: 40px;\n\t\tborder-radius: 10px;\n\t\tborder: 1px solid rgba($dark, 0.1);\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 1rem;\n\t\tcolor: $primary;\n\t\t@include transition();\n\t\t@include transition-properties(border-color, background-color, color);\n\t\t&:not(:last-child) {\n\t\t\tmargin-right: 10px;\n\t\t}\n\t}\n\t.Text {\n\t\tfont-size: 0.875rem;\n\t\tline-height: 1.125rem;\n\t\tcolor: $secondary;\n\t}\n\t&:hover {\n\t\t.Icon {\n\t\t\tborder-color: $primary;\n\t\t\tbackground-color: $primary;\n\t\t\tcolor: $white;\n\t\t}\n\t}\n\t&.WpcBigSize {\n\t\t.Icon {\n\t\t\theight: 50px;\n\t\t\twidth: 50px;\n\t\t\tmin-width: 50px;\n\t\t\tborder-radius: 12px;\n\t\t\tfont-size: 1.25rem;\n\t\t\t&:not(:last-child) {\n\t\t\t\tmargin-right: 12px;\n\t\t\t}\n\t\t}\n\t\t.Text {\n\t\t\tfont-size: 1rem;\n\t\t}\n\t}\n\t&.WpcFilled {\n\t\t.Icon {\n\t\t\tborder-color: $primary;\n\t\t\tbackground: $primary;\n\t\t\tcolor: $dark;\n\t\t}\n\t\t&:hover {\n\t\t\t.Icon {\n\t\t\t\tbackground: darken($primary, 10%);\n\t\t\t\tbox-shadow: 0 20px 20px -10px rgba($primary, 0.4);\n\t\t\t}\n\t\t}\n\t}\n\t&.WpcDisabled {\n\t\t.Icon {\n\t\t\tbackground: transparent;\n\t\t\tborder-color: rgba($secondary, 0.2);\n\t\t\tcolor: rgba($secondary, 0.2);\n\t\t\tcursor: not-allowed;\n\t\t}\n\t\t.Text {\n\t\t\tcolor: $secondary;\n\t\t}\n\t\t&:hover {\n\t\t\t.Icon {\n\t\t\t\tbackground: transparent;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.WpcAddButton {\n\tmargin-bottom: 10px;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tcursor: pointer;\n\t.Icon {\n\t\theight: 20px;\n\t\twidth: 20px;\n\t\tmin-width: 20px;\n\t\tborder-radius: 20px;\n\t\tborder: 1px solid rgba($secondary, 0.2);\n\t\tcolor: $secondary;\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 0.5rem;\n\t\t@include transition();\n\t\t@include transition-properties(border-color, color);\n\t\t&:not(:last-child) {\n\t\t\tmargin-right: 10px;\n\t\t}\n\t}\n\t.Text {\n\t\tfont-size: 0.875rem;\n\t\tline-height: 1.25;\n\t\tcolor: $secondary;\n\t\t@include transition($for: color);\n\t}\n\t&:hover {\n\t\t.Icon {\n\t\t\tborder-color: $secondary;\n\t\t\tcolor: $dark;\n\t\t}\n\t\t.Text {\n\t\t\tcolor: $dark;\n\t\t}\n\t}\n}\n\n.WpcBackButton {\n\tdisplay: flex;\n\talign-items: center;\n\tcolor: $dark;\n\t.Icon {\n\t\tfont-size: 0.875rem;\n\t\tmargin-right: 10px;\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n    color: $secondary;\n    @include transition();\n\t}\n\t.Text {\n\t\tfont-size: 1.125rem;\n\t\tfont-weight: 400;\n\t\tdisplay: flex;\n    color: $secondary;\n    @include transition();\n\t}\n\t&:hover {\n    .Icon {\n      color: $dark;\n    }\n    .Text {\n      color: $dark;\n    }\n\t}\n}\n", "@mixin transition($for: all, $duration: 0.3s, $type: ease-in-out, $delay: 0s) {\n\t-webkit-transition: $for $duration $type $delay;\n\t-moz-transition: $for $duration $type $delay;\n\t-o-transition: $for $duration $type $delay;\n\ttransition: $for $duration $type $delay;\n}\n@mixin transition-properties($for...) {\n\t-webkit-transition-property: $for;\n\t-moz-transition-property: $for;\n\t-o-transition-property: $for;\n\ttransition-property: $for;\n}\n", "\nbody {\n\tbackground: #f3f3f3;\n}\n\n.wrapper {\n\theight: 100vh;\n\toverflow: hidden;\n\tposition: relative;\n\tmax-width: 550px;\n\tmargin: 0 auto;\n}\n\n.wpc-body {\n\toverflow-y: auto;\n\toverflow-x: hidden;\n\theight: 100vh;\n\toverflow-y: auto;\n\tdisplay: flex;\n\tflex-direction: column;\n\tmax-width: 550px;\n\tmargin: 0 auto;\n\tbackground: #0c0f14;\n}\n\n.mt30 {\n\tmargin-top: 30px;\n}\n\n.pl15 {\n\tpadding-left: 15px;\n}\n.pr15 {\n\tpadding-right: 15px;\n}\n\n.footer {\n\tbackground: $sidebar;\n\tpadding: 15px;\n\ttext-align: center;\n\tmargin-top: auto;\n\tp {\n\t\tcolor: $white;\n\t\tfont-size: 14px;\n\t\tfont-weight: 400;\n\t}\n\ta {\n\t\tcolor: $white;\n\t}\n}\n\n.bgi__property {\n\tbackground-size: cover;\n\tbackground-position: center;\n\tbackground-repeat: no-repeat;\n}\n\n.ordered__menu .ordered__item .menu__details .orderer__info span {\n\tfont-size: 10px;\n\tfont-weight: 400;\n\tcolor: #f3f3f3;\n\ttext-transform: capitalize;\n  }\n  .ordered__menu .ordered__item .menu__details .orderer__info .for__guest {\n\tdisplay: block;\n\tfont-size: 14px;\n\tfont-weight: 700;\n\tcolor: #d17842;\n  }", ".WpcFormGroup {\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: flex-start;\n\t.WpcFormLabel {\n\t\tfont-size: 1rem;\n\t\tcolor: $secondary;\n\t\tmargin-bottom: 10px;\n\t\tmargin-right: 10px;\n\t\tline-height: 1;\n\t}\n\t&:not(:last-child) {\n\t\tmargin-bottom: 20px;\n\t\tmargin-top: 15px;\n\t}\n}\n\n// form control\ninput.form-control {\n\theight: 50px;\n\tpadding: 0 20px;\n\tfont-size: 1rem;\n\tcolor: $dark;\n\tbackground: $white;\n\tborder-color: rgba($secondary, 0.2);\n\tborder-radius: 12px;\n\tborder-width: 1px;\n\t&:focus {\n\t\tbox-shadow: 0 0 5px rgba($dark, 0.15);\n\t}\n\t&::placeholder {\n\t\tcolor: lighten($secondary, 20%);\n\t}\n\t&:not(:last-child) {\n\t\tmargin-bottom: 5px;\n\t}\n\t&.fancy__form {\n\t\tborder-radius: 0;\n\t\tborder: 0;\n\t\theight: 60px;\n\t\tborder-bottom: 1px solid rgba($secondary, 0.2);\n\t\tbox-shadow: none;\n\t\tpadding: 0;\n\t}\n\t&[type=\"password\"] {\n\t\tpadding-right: 120px;\n\t}\n}\n\n.user__thumb__change {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 40px;\n\t.user__thumb {\n\t\twidth: 80px;\n\t\theight: 80px;\n\t\tborder-radius: 10px;\n\t\tmargin-right: 15px;\n\t}\n}\n\n.form__group {\n\tposition: relative;\n\tmargin-bottom: 15px;\n\t.forget__pass {\n\t\tfont-size: 15px;\n\t\tfont-weight: 400;\n\t\tcolor: $dark;\n\t\tposition: absolute;\n\t\ttop: 20px;\n\t\tright: 0;\n\t}\n\tlabel {\n\t\tmargin-bottom: 10px;\n\t\tdisplay: inline-block;\n\t\tcolor: #f3f3f3;\n\t}\n}\n\n.edit__profile__form {\n\tpadding: 0 15px 30px;\n}\n\n.search__bar {\n\tpadding: 0 15px;\n\tposition: relative;\n\tmargin-bottom: 30px;\n\t.search__icon {\n\t\tposition: absolute;\n\t\ttop: 13px;\n\t\tleft: 20px;\n\t\tfont-size: 22px;\n\t\tcolor: #52555a;\n\t\tpointer-events: none;\n\t}\n\t.form__control {\n\t\tbackground: #141921;\n\t\twidth: 100%;\n\t\theight: 50px;\n\t\tborder-radius: 25px;\n\t\tpadding: 0 25px;\n\t\tpadding-left: 50px;\n\t\tcolor: #f5f5f5;\n\t\t::-webkit-input-placeholder {\n\t\t\tcolor: #52555a;\n\t\t}\n\t\t::-moz-placeholder {\n\t\t\tcolor: #52555a;\n\t\t}\n\t\t:-ms-input-placeholder {\n\t\t\tcolor: #52555a;\n\t\t}\n\t\t:-moz-placeholder {\n\t\t\tcolor: #52555a;\n\t\t}\n\t}\n}\n", "\n\n.account__controller {\n    display: flex;\n    flex-direction: column;\n    text-align: center;\n    justify-content: space-between;\n    height: 100vh;\n    padding: 15px;\n    .site__welcome__logo {\n        padding-top: 70%;\n    }\n    .account__access__content {\n        padding-top: 40%;\n        h3 {\n            font-size: 35px;\n            font-weight: 500;\n            color: $dark;\n            margin-bottom: 20px;\n        }\n        p {\n            font-size: 16px;\n            font-weight: 400;\n            color: $secondary;\n        }\n        .account__access__form {\n            margin-top: 70px;\n        }\n    }\n    .alternet__access {\n        margin-bottom: 35px;\n        .WpcButton {\n            margin-bottom: 25px;\n        }\n        .alternet__text {\n            color: $secondary;\n            font-weight: 400;\n            a {\n                color: $dark;\n                font-weight: 500;\n            }\n        }\n    }\n}", "\n\n.header {\n    background: $sidebar;\n    padding: 30px 15px 85px;\n    box-shadow: inset 0 0 0 1000px rgba(16, 20, 25, .65);\n    .header__nav {\n        display: flex;\n        justify-content: space-between;\n        padding: 0 15px 20px;\n        .nav__icon {\n            width: 50px;\n            a {\n                color: $white;\n                font-size: 22px;\n            }\n        }\n        .notification__icon {\n            a {\n                color: $primary;\n                font-size: 22px;\n                position: relative;\n                &:before {\n                    position: absolute;\n                    top: 0;\n                    right: 2px;\n                    height: 8px;\n                    width: 8px;\n                    background: $success;\n                    border-radius: 50%;\n                    content: '';\n                }\n            }\n        }\n    }\n    .header__greeting {\n        padding: 15px;\n        position: relative;\n        p {\n            font-size: 17px;\n            color: $primary;\n            font-weight: 700;\n        }\n        h2 {\n            font-size: 32px;\n            font-weight: 700;\n            color: $white;\n        }\n        #barista_available_form {\n            position: absolute;\n            top: 15px;\n            right: 15px;\n            label {\n                input {\n                    display: none;\n                    &:checked {\n                        ~ span {\n                            &:after {\n                                left: 11px;\n                                background: $success;\n                            }\n                        }\n                    }\n                }\n                span {\n                    font-size: 1rem;\n                    color: $white;\n                    position: relative;\n                    display: inline-block;\n                    padding-left: 30px;\n                    &::before {\n                        position: absolute;\n                        top: 2px;\n                        left: 0;\n                        height: 15px;\n                        width: 25px;\n                        border-radius: 15px;\n                        background: rgba($white, .9);\n                        content: '';\n                        \n                    }\n                    &:after {\n                        position: absolute;\n                        top: 4px;\n                        left: 3px;\n                        height: 11px;\n                        width: 11px;\n                        background: $secondary;\n                        border-radius: 50%;\n                        content: '';\n                        transition:  all .3s ease;\n                    }\n                }\n            }\n        }\n    }\n}\n\n.section__header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 0 15px;\n    margin-bottom: 15px;\n    .section__header__title {\n        font-size: 24px;\n        font-weight: 700;\n        color: $white\n    }\n    .section__header__icon {\n        height: 40px;\n        width: 40px;\n        border-radius: 10px;\n        background-color: #242c39;\n        color: $primary;\n        line-height: 40px;\n        text-align: center;\n        i {\n            color: $primary;\n        }\n    }\n}\n\n.page__header {\n    padding: 20px 15px 0;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40px;\n    .left__content, \n    .right__content {\n        flex: 1;\n    }\n    .left__content {\n        .back__button {\n            font-size: 16px;\n            font-weight: 400;\n            color: $white;\n        }\n    }\n    .page__title {\n        flex: 0 0 auto;\n        text-align: center;\n        h5 {\n            font-size: 16px;\n            font-weight: 400;\n            color: $white;\n        }\n    }\n    .right__content {\n        text-align: right;\n        .clear__button {\n            font-size: 14px;\n            font-weight: 400;\n            color: $white;\n        }\n    }\n}\n", "\n\n.coupon__statistic {\n    padding: 0 15px;\n    display: flex;\n    justify-content: space-between;\n    margin-top: -69px;\n    .statistic__card {\n        flex: 0 0 calc(50% - 7.5px);\n        padding: 15px;\n        background: rgba($accent, 0.8);\n        border-radius: 20px;\n        transition: all .3s ease;\n        &:hover {\n            background: $primary;\n            .card__top {\n                .icon {\n                    background: rgba($sidebar, .8);\n                }\n            }\n            p {\n                color: #fff;\n            }\n        }\n        .card__top {\n            display: flex;\n            align-items: center;\n            margin-bottom: 10px;\n            .icon {\n                height: 54px;\n                width: 54px;\n                text-align: center;\n                background: $primary;\n                border-radius: 15px;\n                margin-right: 10px;\n                transition: all .3s ease;\n                i {\n                    color: $white;\n                    line-height: 54px;\n                    font-size: 20px;\n                }\n            }\n            h4 {\n                font-size: 30px;\n                color: $white;\n                font-weight: 400;\n            }\n        }\n        p {\n            font-size: 14px;\n            color: #cecece;\n            transition: all .3s ease;\n        }\n    }\n}\n", "\n.order__tracking__wrap {\n    padding: 10px 15px;\n}\n.order__tracking {\n    margin-top: 15px;\n    display: flex;\n    align-items: center;\n    border: 1px solid #d17842;\n    border-radius: 20px;\n    padding: 5px;\n    padding-right: 10px;\n    background: #d17842;\n    transition: all .3s ease;\n    &:hover {\n        background: $primary;\n        .order__tracking__body {\n            p {\n                color: $white;\n            }\n        }\n    }\n    & &__thumb {\n        flex: 0 0 100px;\n        min-width: 100px;\n        border-radius: 20px;\n        overflow: hidden;\n        img {\n            width: 100%;\n        }\n    }\n    & &__body {\n        position: relative;\n        h5 {\n            font-size: 14px;\n            font-weight: 400;\n            color: $white;\n            margin-bottom: 10px;\n        }\n        h3 {\n            font-size: 18px;\n            font-weight: 500;\n            color: $white;\n            margin-bottom: 2px;\n        }\n        .cancel__order {\n            position: absolute;\n            top: 15px;\n            right: 15px;\n        }\n        p {\n            font-size: 14px;\n            font-weight: 400;\n            color: #f3f3f3;\n            transition: all .3s ease;\n        }\n    }\n}\n\n.wpc-order__history {\n    padding: 0 15px;\n    margin: 0;\n    list-style: none;\n    .item__ordered {\n        background-color: $accent;\n        border-radius: 10px;\n        padding: 15px;\n        &:not(:last-child) {\n            margin-bottom: 20px;\n        }\n        .order__top {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 10px;\n            .order__status {\n                font-size: 12px;\n                font-weight: 400;\n                color: $primary;\n                background: rgba($primary, .1);\n                padding: 3px 10px;\n                border-radius: 15px;\n                border: 1px solid rgba($primary, .5);\n            }\n        }\n        .order__id {\n            font-size: 13px;\n            font-weight: 400;\n            color: $primary;\n            margin-bottom: 10px;\n        }\n        .order__details {\n            display: flex;\n            align-items: center;\n            .item__thumb {\n                width: 50px;\n                height: 50px;\n                min-width: 50px;\n                margin-right: 10px;\n                border-radius: 10px;\n            }\n            .item__body {\n                h5 {\n                    font-size: 16px;\n                    font-weight: 400;\n                    color: $white;\n                    margin-bottom: 5px;\n                }\n                .order__date {\n                    font-size: 13px;\n                    font-weight: 400;\n                    color: $secondary;\n                }\n            }\n        }\n    }\n}\n\n.order__queue {\n    padding: 0 15px;\n    margin-top: 30px;\n    padding-bottom: 30px;\n    .nav-tabs {\n        margin-bottom: 15px;\n        border-bottom: 0;\n        background: #343f4f;\n        border-radius: 15px;\n        .nav-item {\n            margin-bottom: 0;\n            flex: 0 0 50%;\n            .nav-link {\n                border: 0;\n                padding: 15px 10px;\n                text-align: center;\n                border-radius: 15px;\n                font-size: 14px;\n                font-weight: 500;\n                color: $grey;\n                &.active {\n                    background: $primary;\n                    color: $white\n                }\n            }\n        }\n    }\n    .tap__to__order__page {\n        display: flex;\n        justify-content: flex-end;\n        margin-bottom: 15px;\n        a {\n            height: 40px;\n            width: 40px;\n            line-height: 40px;\n            text-align: center;\n            background-color: #242c39;\n            color: #d17842;\n            display: inline-block;\n            border-radius: 12px;\n        }\n    }\n}\n\n\n.ordered__menu {\n    .ordered__item {\n        padding: 15px;\n        border-radius: 20px;\n        border: 1px solid rgba($accent, .2);\n        background-color: #242c39;\n        &:not(:last-child) {\n            margin-bottom: 15px;\n        }\n        .menu__details {\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n            .ordered__item__thumb,\n            .orderer__thumb {\n                width: 50px;\n                height: 50px;\n                margin-right: 15px;\n                border-radius: 12px;\n            }\n            .orderer__info {\n                h5 {\n                    font-size: 14px;\n                    font-weight: 500;\n                    color: $white;\n                }\n                span {\n                    font-size: 12px;\n                    font-weight: 400;\n                    color: #f3f3f3;\n                    text-transform: uppercase;\n                }\n            }\n        }\n        .order__info {\n            display: flex;\n            align-items: flex-start;\n            justify-content: space-between;\n            .order__info__details {\n                flex: 0 0 auto;\n                max-width: 40%;\n                h5 {\n                    font-size: 16px;\n                    font-weight: 500;\n                    color: $white;\n                }\n                .order__id {\n                    font-size: 13px;\n                    font-weight: 400;\n                    color: $light;\n                }\n            }\n            .order__info__button {\n                display: flex;\n                overflow: hidden;\n                white-space: nowrap;\n                .WpcButton {\n                    min-height: auto;\n                    padding: 8px 15px;\n                    display: inline-block;\n                    width: auto;\n                    border-radius: 10px;\n                    font-size: 14px;\n                    font-weight: 400;\n                    background-color: $primary;\n                    color: #fff;\n                    overflow: hidden;\n                    white-space: nowrap;\n                    text-overflow: ellipsis;\n                }\n                .mr-1{\n                    color: #fff;\n                    background-color: #dc3545;\n                    border-color: #dc3545;\n                }\n            }\n        }\n        .order__notes {\n            padding: 15px 0;\n            position: relative;\n            p {\n                font-size: 14px;\n                font-weight: 400;\n                color: #f3f3f3;\n                padding-left: 22px;\n                font-style: italic;\n                position: relative;\n                &::before {\n                    content: \"“\";\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    font: 4em/1em \"PT Sans\", sans-serif;\n                    color: #f3f3f3;\n                }\n            }\n        }\n    }\n}\n\n.add__order {\n    padding: 0 15px;\n    .add__order__title {\n        font-size: 14px;\n        font-weight: 400;\n        color: $secondary;\n        margin-bottom: 10px;\n    }\n    .add__order__form {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 30px;\n        .wpc__select {\n            position: relative;\n            flex: 0 0 auto;\n            span {\n                position: absolute;\n                top: 19px;\n                right: 20px;\n                font-size: 10px;\n                color: $secondary;\n                pointer-events: none;\n            }\n            select {\n                height: 50px;\n                border-radius: 15px;\n                padding: 0 40px 0 20px;\n                font-size: 16px;\n                color: $dark;\n                font-weight: 400;\n                border: 2px solid rgba($secondary, .2);\n                appearance: none;\n            }\n        }\n        .item__quantity__wrap {\n            display: flex;\n            input,\n            span {\n                height: 40px;\n                width: 40px;\n                min-width: 40px;\n                text-align: center;\n                line-height: 40px;\n                color: $secondary;\n                border-radius: 10px;\n                border: 2px solid rgba($secondary, .2);\n                &:not(:last-child) {\n                    margin-right: 15px;\n                }\n                &.item__decrease,\n                &.item__increase {\n                    cursor: pointer;\n                }\n            }\n            input {\n                appearance: none;\n                text-align: center;\n            }\n        }\n    }\n}\n\n\n", "\n.wpc__item__modal {\n    &.show {\n        background: rgba($overlayColor, .7);\n    }\n    .modal-dialog {\n        min-width: 290px;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%) !important;\n        margin: 0;\n        .modal-content {\n            background: transparent;\n        }\n    }\n    .modal-body {\n        padding: 0;\n        .item {\n            border-radius: 20px;\n            overflow: hidden;\n            background: $accent;\n            .item__body {\n                padding: 20px 18px 30px;\n                .item__name {\n                    font-size: 24px;\n                }\n                p {\n                    font-size: 18px;\n                }\n                .WpcButton {\n                    margin-top: 20px;\n                }\n            }\n        }\n    }\n}\n", "\n.wpc-notificatons {\n    padding: 0 15px;\n    .notification__item {\n        border: 1px solid rgba($accent, .1);\n        background-color: $accent;\n        border-radius: 10px;\n        padding: 15px 25px;\n        &:not(:last-child) {\n            margin-bottom: 15px;\n        }\n        .notification__time {\n            font-size: 13px;\n            font-weight: 400;\n            color: $secondary;\n            line-height: 1.5;\n        }\n        .card {\n            border: 0;\n            background: transparent;\n            .card-header {\n                padding: 0;\n                background: transparent;\n                border-bottom: 0;\n                \n                h2 {\n                    .btn {\n                        padding: 0;\n                        font-size: 16px;\n                        font-weight: 400;\n                        color: $white;\n                        text-decoration: none;\n                        position: relative;\n                        span {\n                            position: absolute;\n                            top: 5px;\n                            right: 0;\n                            color: $grey;\n                            font-size: 10px;\n                            transform: rotate(0);\n                            transition: all .3s ease;\n                        }\n                        &.collapsed {\n                            span {\n                                transform: rotate(-90deg);\n                            }\n                        }\n                    }\n                }\n            }\n            .card-body {\n                padding: 0;\n                padding: 10px 0;\n                p {\n                    font-size: 14px;\n                    font-weight: 400;\n                    color: $grey;\n                }\n            }\n        }\n    }\n}\n\n.WpcPaginationWrapper {\n    padding: 15px;\n    margin: 0 auto;\n    .page-item {\n        &:not(:last-child) {\n            margin-right: 5px;\n        }\n        &:first-child {\n            .page-link {\n                border-top-left-radius: 8px;\n                border-bottom-left-radius: 8px;\n            }\n        }\n        &:last-child {\n            .page-link {\n                border-top-right-radius: 8px;\n                border-bottom-right-radius: 8px;\n            }\n        }\n        &.disabled {\n            .page-link {\n                color: rgba(209, 120, 66, .5);\n                border-color: rgba(209, 120, 66, .5);\n                background: transparent;\n            }\n        }\n        &.active {\n            .page-link {\n                color: $white;\n                border-color: #d17842;\n                background: #d17842;\n            }\n        }\n        .page-link {\n            height: 30px;\n            width: 30px;\n            padding: 0;\n            line-height: 30px;\n            font-size: 13px;\n            text-align: center;\n            background: transparent;\n            color: #d17842;\n            border: 1px solid #d17842;\n            border-radius: 8px;\n            &.WpcActive {\n                color: $white;\n                border-color: #d17842;\n                background: #d17842;\n            }\n            i {\n                font-size: 12px;\n            }\n        }\n    }\n}", ".wpc-navigation__drawer__wrap {\n    background: $accent;\n    position: absolute;\n    top: 0;\n    left: -100vw;\n    width: 100%;\n    height: 100%;\n    z-index: 99;\n    transition: all .3s ease;\n    &.show {\n        left: 0;\n    }\n}\n\n.wpc-navigation__drawer {\n    padding: 20px 25px 40px;\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    .navigation__drawer__closer {\n        margin-bottom: 30px;\n        display: inline-block;\n        color: $white;\n    }\n    .navigation__drawer__header {\n        display: flex;\n        justify-content: space-between;\n        margin-bottom: 70px;\n        .navigation__drawer__user {\n            display: flex;\n            align-items: center;\n            &__thumb {\n                width: 50px;\n                height: 50px;\n                min-width: 50px;\n                margin-right: 15px;\n                border-radius: 12px;\n            }\n            &__body {\n                .user__name {\n                    font-size: 18px;\n                    font-weight: 700;\n                    color: $white;\n                }\n                .designation {\n                    font-size: 13px;\n                    font-weight: 400;\n                    color: $grey;\n                }\n            }\n            &__edit {\n                a {\n                    height: 40px;\n                    width: 40px;\n                    border-radius: 10px;\n                    border: 1px solid rgba($secondary, .3);\n                    line-height: 40px;\n                    text-align: center;\n                    display: inline-block;\n                    color: $primary;\n                }\n            }\n        }\n\n    }\n    .navigation__menu {\n        .navigation__list {\n            padding: 15px 0;\n            display: flex;\n            &:not(:last-child) {\n                border-bottom: 1px solid rgba($secondary, .1);\n            }\n            .icon {\n                height: 40px;\n                width: 40px;\n                line-height: 40px;\n                text-align: center;\n                border: 1px solid rgba($secondary, .1);\n                color: $primary;\n                margin-right: 10px;\n                display: inline-block;\n                border-radius: 12px;\n                background-color:  #242c39;\n            }\n            .text {\n                font-size: 18px;\n                font-weight: 400;\n                color: $white;\n                span {\n                    color: $primary;\n                }\n            }\n        }\n    }\n}", "\n\n.employer__suggestion {\n    padding: 0 15px;\n    margin-bottom: 30px;\n    & &__title {\n        p {\n            font-size: 16px;\n            font-weight: 400;\n            color: $secondary;\n            margin-bottom: 10px;\n        }\n    }\n}\n\n.employer__suggestion__item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 15px;\n    border-radius: 20px;\n    background-color: $accent;\n    &:not(:last-child) {\n        margin-bottom: 15px;\n    }\n    .employer__info {\n        display: flex;\n        align-items: center;\n        .employer__thumb {\n            flex: 0 0 50px;\n            width: 50px;\n            height: 50px;\n            margin-right: 15px;\n            border-radius: 12px;\n        }\n        .employer__name {\n            h5 {\n                font-size: 14px;\n                font-weight: 500;\n                color: $white;\n            }\n            span {\n                font-size: 12px;\n                font-weight: 400;\n                color: $light;\n            }\n        }\n    }\n    .check__employer {\n        width: 18px;\n        height: 18px;\n        input {\n            display: none;\n            &:checked {\n                & ~ span {\n                    &:before {\n                        border-color: $success;\n                        content: '\\e906';\n                        font-family: 'wpc-icon';\n                        color: $success;\n                        font-size: 10px;\n                    }\n                }\n            }\n        }\n        span {\n            position: relative;\n            height: 18px;\n            width: 18px;\n            display: inline-block;\n            &:before {\n                position: absolute;\n                top: 1px;\n                left: 0;\n                border: 2px solid rgba($secondary, 1);\n                border-radius: 3px;\n                height: 18px;\n                width: 18px;\n                content: '';\n                line-height: 15px;\n                padding-left: 2px;\n            }\n        }\n    }\n}\n\n.ajax_div_employee {\n    max-height: 485px;\n    overflow: auto;\n}"], "sourceRoot": ""}
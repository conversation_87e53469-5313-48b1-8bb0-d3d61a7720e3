
.order__tracking__wrap {
    padding: 0 15px;
}
.order__tracking {
    margin-top: 15px;
    display: flex;
    align-items: center;
    border: 1px solid rgba($primary, .15);
    border-radius: 20px;
    padding: 5px;
    padding-right: 10px;
    background: rgba($primary, .15);
    transition: all .3s ease;
    &:hover {
        background: $primary;
        .order__tracking__body {
            p {
                color: $dark;
            }
        }
    }
    & &__thumb {
        flex: 0 0 100px;
        min-width: 100px;
        margin-right: 15px;
        border-radius: 20px;
        overflow: hidden;
        img {
            width: 100%;
        }
    }
    & &__body {
        h5 {
            font-size: 14px;
            font-weight: 400;
            color: $dark;
            margin-bottom: 10px;
        }
        h3 {
            font-size: 18px;
            font-weight: 500;
            color: $dark;
            margin-bottom: 2px;
        }
        p {
            font-size: 14px;
            font-weight: 400;
            color: $secondary;
            transition: all .3s ease;
        }
    }
}

.wpc-order__history {
    padding: 0 15px;
    margin: 0;
    list-style: none;
    .item__ordered {
        border: 1px solid rgba($secondary, .1);
        border-radius: 10px;
        padding: 15px;
        &:not(:last-child) {
            margin-bottom: 15px;
        }
        .order__top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            .order__status {
                font-size: 12px;
                font-weight: 400;
                color: $primary;
                background: rgba($primary, .1);
                padding: 3px 10px;
                border-radius: 15px;
                border: 1px solid rgba($primary, .5);
            }
        }
        .order__id {
            font-size: 13px;
            font-weight: 400;
            color: $primary;
            margin-bottom: 10px;
        }
        .order__details {
            display: flex;
            align-items: center;
            .item__thumb {
                width: 50px;
                height: 50px;
                min-width: 50px;
                margin-right: 10px;
                border-radius: 10px;
            }
            .item__body {
                h5 {
                    font-size: 16px;
                    font-weight: 400;
                    color: $dark;
                    margin-bottom: 5px;
                }
                .order__date {
                    font-size: 13px;
                    font-weight: 400;
                    color: $secondary;
                }
            }
        }
    }
}

.order__queue {
    padding: 0 15px;
    margin-top: 30px;
    padding-bottom: 30px;
    .nav-tabs {
        margin-bottom: 15px;
        border-bottom: 0;
        background: rgba($primary, .2);
        border-radius: 15px;
        .nav-item {
            margin-bottom: 0;
            flex: 0 0 50%;
            .nav-link {
                border: 0;
                padding: 15px 10px;
                text-align: center;
                border-radius: 15px;
                font-size: 17px;
                font-weight: 500;
                color: $dark;
                &.active {
                    background: $primary;
                }
            }
        }
    }
    .tap__to__order__page {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;
        a {
            height: 40px;
            width: 40px;
            line-height: 40px;
            text-align: center;
            border: 1px solid rgba(131,133,142,0.1);
            color: #d17842;
            display: inline-block;
            border-radius: 12px;
        }
    }
}


.ordered__menu {
    .ordered__item {
        padding: 15px;
        border-radius: 20px;
        border: 1px solid rgba($primary, .2);
        &:not(:last-child) {
            margin-bottom: 15px;
        }
        .menu__details {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            .ordered__item__thumb,
            .orderer__thumb {
                width: 50px;
                height: 50px;
                margin-right: 15px;
                border-radius: 12px;
            }
            .orderer__info {
                h5 {
                    font-size: 14px;
                    font-weight: 500;
                    color: $dark;
                }
                span {
                    font-size: 12px;
                    font-weight: 400;
                    color: $secondary;
                    text-transform: uppercase;
                }
            }
        }
        .order__info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .order__info__details {
                h5 {
                    font-size: 16px;
                    font-weight: 500;
                    color: $dark;
                }
                .order__id {
                    font-size: 13px;
                    font-weight: 400;
                    color: $primary;
                }
            }
            .order__info__button {
                .WpcButton {
                    min-height: auto;
                    padding: 6px 15px;
                    display: inline-block;
                    width: auto;
                    border-radius: 10px;
                    font-size: 13px;
                    font-weight: 400;
                }
            }
        }
    }
}

.add__order {
    padding: 0 15px;
    .add__order__title {
        font-size: 14px;
        font-weight: 400;
        color: $secondary;
        margin-bottom: 10px;
    }
    .add__order__form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        .wpc__select {
            position: relative;
            flex: 0 0 auto;
            span {
                position: absolute;
                top: 19px;
                right: 20px;
                font-size: 10px;
                color: $secondary;
                pointer-events: none;
            }
            select {
                height: 50px;
                border-radius: 15px;
                padding: 0 40px 0 20px;
                font-size: 16px;
                color: $dark;
                font-weight: 400;
                border: 2px solid rgba($secondary, .2);
                appearance: none;
            }
        }
        .item__quantity__wrap {
            display: flex;
            input,
            span {
                height: 40px;
                width: 40px;
                min-width: 40px;
                text-align: center;
                line-height: 40px;
                color: $secondary;
                border-radius: 10px;
                border: 2px solid rgba($secondary, .2);
                &:not(:last-child) {
                    margin-right: 15px;
                }
                &.item__decrease,
                &.item__increase {
                    cursor: pointer;
                }
            }
            input {
                appearance: none;
                text-align: center;
            }
        }
    }
}



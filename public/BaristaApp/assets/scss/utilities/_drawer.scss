

.wpc-navigation__drawer__wrap {
    background: $white;
    position: absolute;
    top: 0;
    left: -100vw;
    width: 100%;
    height: 100%;
    z-index: 99;
    transition: all .3s ease;
    &.show {
        left: 0;
    }
}

.wpc-navigation__drawer {
    padding: 10px 25px 40px;
    display: flex;
    flex-direction: column;
    height: 100%;
    .navigation__drawer__closer {
        margin-bottom: 30px;
        display: inline-block;
    }
    .navigation__drawer__header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 70px;
        .navigation__drawer__user {
            display: flex;
            align-items: center;
            &__thumb {
                width: 50px;
                min-width: 50px;
                height: 50px;
                margin-right: 15px;
                border-radius: 12px;
            }
            &__body {
                .user__name {
                    font-size: 18px;
                    font-weight: 700;
                    color: $dark;
                }
                .designation {
                    font-size: 13px;
                    font-weight: 400;
                    color: $secondary;
                }
            }
            &__edit {
                a {
                    height: 40px;
                    width: 40px;
                    border-radius: 10px;
                    border: 1px solid rgba($secondary, .3);
                    line-height: 40px;
                    text-align: center;
                    display: inline-block;
                    color: $primary;
                }
            }
        }

    }
    .navigation__menu {
        .navigation__list {
            padding: 15px 0;
            display: flex;
            &:not(:last-child) {
                border-bottom: 1px solid rgba($secondary, .1);
            }
            .icon {
                height: 40px;
                width: 40px;
                line-height: 40px;
                text-align: center;
                border: 1px solid rgba($secondary, .1);
                color: $primary;
                margin-right: 15px;
                display: inline-block;
                border-radius: 12px;
            }
            .text {
                font-size: 18px;
                font-weight: 400;
                color: $dark;
                span {
                    color: $primary;
                }
            }
        }
    }
}

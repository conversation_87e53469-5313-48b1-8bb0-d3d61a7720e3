

.coupon__statistic {
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    margin-top: -69px;
    .statistic__card {
        flex: 0 0 calc(50% - 7.5px);
        padding: 15px;
        border: 1px solid rgba($primary, .2);
        background: $white;
        border-radius: 20px;
        transition: all .3s ease;
        &:hover {
            background: $primary;
            .card__top {
                .icon {
                    background: rgba($sidebar, .08);
                }
            }
            p {
                color: $sidebar;
            }
        }
        .card__top {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            .icon {
                height: 54px;
                width: 54px;
                text-align: center;
                background: $primary;
                border-radius: 15px;
                margin-right: 10px;
                transition: all .3s ease;
                i {
                    color: $white;
                    line-height: 54px;
                    font-size: 20px;
                }
            }
            h4 {
                font-size: 30px;
                color: $sidebar;
                font-weight: 400;
            }
        }
        p {
            font-size: 14px;
            color: $secondary;
            transition: all .3s ease;
        }
    }
}

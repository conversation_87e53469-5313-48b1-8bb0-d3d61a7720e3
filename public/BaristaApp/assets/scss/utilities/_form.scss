.WpcFormGroup {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	.WpcFormLabel {
		font-size: 1rem;
		color: $secondary;
		margin-bottom: 10px;
		margin-right: 10px;
		line-height: 1;
	}
	&:not(:last-child) {
		margin-bottom: 20px;
	}
}

// form control
input.form-control {
	height: 50px;
	padding: 0 20px;
	font-size: 1rem;
	color: $dark;
	background: $white;
	border-color: rgba($secondary, 0.2);
	border-radius: 12px;
	border-width: 1px;
	&:focus {
		box-shadow: 0 0 5px rgba($dark, 0.15);
	}
	&::placeholder {
		color: lighten($secondary, 20%);
	}
	&:not(:last-child) {
		margin-bottom: 5px;
	}
	&.fancy__form {
		border-radius: 0;
		border: 0;
		height: 60px;
		border-bottom: 1px solid rgba($secondary, 0.2);
		box-shadow: none;
		padding: 0;
	}
	&[type="password"] {
		padding-right: 120px;
	}
}

.user__thumb__change {
	display: flex;
	align-items: center;
	margin-bottom: 40px;
	.user__thumb {
		width: 80px;
		height: 80px;
		border-radius: 10px;
		margin-right: 15px;
	}
}

.form__group {
	position: relative;
	margin-bottom: 15px;
	.forget__pass {
		font-size: 15px;
		font-weight: 400;
		color: $dark;
		position: absolute;
		top: 20px;
		right: 0;
	}
	label {
		margin-bottom: 10px;
		display: inline-block;
	}
}

.edit__profile__form {
	padding: 0 15px 30px;
}

.search__bar {
	padding: 0 15px;
	position: relative;
	margin-bottom: 30px;
	.search__icon {
		position: absolute;
		top: 13px;
		left: 20px;
		font-size: 22px;
		color: $primary;
		pointer-events: none;
	}
	.form__control {
		background: rgba($secondary, 0.1);
		width: 100%;
		height: 50px;
		border-radius: 25px;
		padding: 0 25px;
		padding-left: 50px;
		::-webkit-input-placeholder {
			color: $secondary;
		}
		::-moz-placeholder {
			color: $secondary;
		}
		:-ms-input-placeholder {
			color: $secondary;
		}
		:-moz-placeholder {
			color: $secondary;
		}
	}
}



.employer__suggestion {
    padding: 0 15px;
    margin-bottom: 30px;
    & &__title {
        p {
            font-size: 16px;
            font-weight: 400;
            color: $secondary;
            margin-bottom: 10px;
        }
    }
}

.employer__suggestion__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 20px;
    border: 2px solid rgba($primary, .2);
    &:not(:last-child) {
        margin-bottom: 15px;
    }
    .employer__info {
        display: flex;
        align-items: center;
        .employer__thumb {
            flex: 0 0 50px;
            width: 50px;
            height: 50px;
            margin-right: 15px;
            border-radius: 12px;
        }
        .employer__name {
            h5 {
                font-size: 14px;
                font-weight: 500;
                color: $dark;
            }
            span {
                font-size: 12px;
                font-weight: 400;
                color: $secondary;
            }
        }
    }
    .check__employer {
        width: 18px;
        height: 18px;
        input {
            display: none;
            &:checked {
                & ~ span {
                    &:before {
                        border-color: $success;
                        content: '\e906';
                        font-family: 'wpc-icon';
                        color: $success;
                        font-size: 10px;
                    }
                }
            }
        }
        span {
            position: relative;
            height: 18px;
            width: 18px;
            display: inline-block;
            &:before {
                position: absolute;
                top: 1px;
                left: 0;
                border: 2px solid rgba($secondary, .1);
                border-radius: 3px;
                height: 18px;
                width: 18px;
                content: '';
                line-height: 15px;
                padding-left: 2px;
            }
        }
    }
}

.ajax_div_employee {
    max-height: 485px;
    overflow: auto;
}
// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
importScripts('https://www.gstatic.com/firebasejs/8.7.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.7.1/firebase-messaging.js');

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
firebase.initializeApp({
  apiKey: "AIzaSyBQV0teWOl_YWMO_ARUQIWuKBY44EUrwB0",
  authDomain: "team-rnd-wpdev-arcom.firebaseapp.com",
  databaseURL: "https://team-rnd-wpdev-arcom.firebaseio.com",
  projectId: "team-rnd-wpdev-arcom",
  storageBucket: "team-rnd-wpdev-arcom.appspot.com",
  messagingSenderId: "916088299463",
  appId: "1:916088299463:web:a177848b6e1125ee0f83e2"
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage(({data:{title,body,image}}) => {
  const notificationTitle = title
  const notificationOptions = {
    body: body,
    icon: image
  };
  return self.registration.showNotification(notificationTitle,
      notificationOptions);
});
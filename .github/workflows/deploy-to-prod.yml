name: wpcafe-prod-deployment
run-name: Deploy to wpcafe-prod by @${{ github.actor }}
on:
  push:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: ssh to job-board-server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.KEY }}
          port: ${{ secrets.SSH_PORT }}
          script: |
            cd /home/<USER>/wpcafe.app
            git pull --no-edit
            composer install
            php artisan down
            php artisan migrate --force
            php artisan optimize
            yarn
            yarn prod
            php artisan up

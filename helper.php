<?php
use Ku<PERSON>\Larafirebase\Facades\Larafirebase;
use App\Models\User;

function sendFcmMessage(User $user, $title, $body, $imageUrl = null, $actionUrl = null, $priority = null)
{
    $firebase = Larafirebase::withTitle($title);
    $firebase->withBody($body);

    if ($imageUrl) $firebase->withImage($imageUrl);
    if ($actionUrl) $firebase->withClickAction($actionUrl);
    if ($priority)  $firebase->withPriority('high');

    return  $firebase->sendNotification($user->fcm_token);

}
